// course list request and response

import 'package:golf/services/req/req.dart';

enum LinkType { code, password }

extension LinkTypeExtension on LinkType {
  static const statusValues = {
    LinkType.code: 1, // 邮箱+验证码
    LinkType.password: 2, // 邮箱+密码
  };

  int get value => statusValues[this] ?? 1;

  static LinkType fromInt(int value) {
    // 当值为3时，视为password类型, 后端定义的3,其实本质是2
    if (value == 3) value = 2;

    return statusValues.entries
        .firstWhere((entry) => entry.value == value,
            orElse: () => const MapEntry(LinkType.code, 1))
        .key;
  }
}

class PlatformResponse extends BaseResponse {
  final List<Platform> items;
  final Paginator paginator;

  PlatformResponse({
    required super.code,
    required super.msg,
    required this.items,
    required this.paginator,
  }) : super(data: {});

  factory PlatformResponse.fromJson(Map<String, dynamic> json) {
    var itemsJson = json['data']['items'] as List;
    List<Platform> itemsList =
        itemsJson.map((i) => Platform.fromJson(i)).toList();

    return PlatformResponse(
      code: json['code'],
      msg: json['msg'],
      items: itemsList,
      paginator: Paginator.fromJson(json['data']['paginator']),
    );
  }
}

class Platform {
  final String id;
  final String name;
  final LinkType linkType;
  bool binded = false;
  final bool? hasToken;
  final String website;
  // Add other fields...

  Platform({
    required this.id,
    required this.name,
    required this.website,
    required this.linkType,
    required this.hasToken,
    // Add other fields...
  });

  setbind() {
    binded = hasToken ?? false;
    return this;
  }

  factory Platform.fromJson(Map<String, dynamic> json) {
    // assert field not null
    assert(json['id'] != null);
    assert(json['name'] != null);
    assert(json['website'] != null);
    assert(json['bind_category'] != null);
    return Platform(
      id: json['id'],
      name: json['name'],
      website: json['website'],
      hasToken: json['has_token'],
      linkType: LinkTypeExtension.fromInt(int.parse(json['bind_category'])),
      // Add other fields...
    ).setbind();
  }
}
