// booking request and response

import 'package:golf/services/req/req.dart';

import 'platform_req.dart';

// define a enum for status it mappings
enum BookingStatus { all, success, pending, failed }

extension BookingStatusExtension on BookingStatus {
  static const statusValues = {
    BookingStatus.all: 0,
    BookingStatus.success: 1,
    BookingStatus.pending: 2,
    BookingStatus.failed: 3,
  };
  int get value => statusValues[this] ?? 1;
  String get name {
    switch (this) {
      case BookingStatus.all:
        return 'All';
      case BookingStatus.success:
        return 'Successful';
      case BookingStatus.pending:
        return 'Processing';
      case BookingStatus.failed:
        return 'Failed';
      default:
        return 'Successful';
    }
  }

  static BookingStatus fromString(String value) {
    switch (value) {
      case 'All':
        return BookingStatus.all;
      case 'Successful':
        return BookingStatus.success;
      case 'Processing':
        return BookingStatus.pending;
      case 'Failed':
        return BookingStatus.failed;
      case '1':
        return BookingStatus.success;
      case '2':
        return BookingStatus.pending;
      case '3':
        return BookingStatus.failed;
      case '4':
        return BookingStatus.pending;
      default:
        return BookingStatus.success;
    }
  }
}

enum BookingType { reservation, notification }

extension BookingTypeExtension on BookingType {
  static const statusValues = {
    BookingType.reservation: 1,
    BookingType.notification: 2,
  };
  int get value => statusValues[this] ?? 1;
}

class BookingRequest {
  final String courseId;
  final TimeRange timeRange;
  final List<String> dates;
  final int players;
  final String? price;
  final BookingType type;
  final int expiredHours;
  final double maxPrice;
  final bool hotOnly;
  final bool cancelIfNotEnough;

  BookingRequest({
    required this.courseId,
    required this.timeRange,
    required this.dates,
    required this.players,
    this.price,
    required this.type,
    required this.expiredHours,
    this.maxPrice = 0,
    this.hotOnly = true,
    this.cancelIfNotEnough = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'course_id': courseId,
      'time_range': timeRange.toJson(),
      'dates': dates,
      'players': players,
      'price': price,
      'type': type.value,
      'expired_hours': expiredHours,
      'maxprice': maxPrice,
      'hotonly': hotOnly,
      'allowsplit': cancelIfNotEnough ? 0 : 1,
    };
  }
}

class BookingResponse extends BaseResponse {
  BookingResponse(
      {required super.code, required super.msg, required super.data});
  factory BookingResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return BookingResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      data: baseResponse.data,
    );
  }
}

// booking list request and response
class BookingListRequest {
  final String courseId;
  final BookingStatus status;
  final int pageNum;
  final int pageSize;

  BookingListRequest({
    required this.courseId,
    required this.status,
    required this.pageNum,
    required this.pageSize,
  });

  Map<String, String> toJson() {
    return {
      'course_id': courseId,
      'status': status.value.toString(),
      'page': pageNum.toString(),
      'size': pageSize.toString(),
      "order": "dates desc, time_start desc",
    };
  }
}

class BookingListResponse extends BaseResponse {
  final List<Plan> items;
  final Paginator paginator;

  BookingListResponse({
    required super.code,
    required super.msg,
    required this.items,
    required this.paginator,
  }) : super(data: {});

  factory BookingListResponse.fromJson(Map<String, dynamic> json) {
    // is json['data'] is a map and json['data']['items'] is a list
    if (json['data'] is Map && json['data']['items'] is List) {
      var itemsJson = json['data']['items'] as List;
      List<Plan> itemsList = itemsJson.map((i) => Plan.fromJson(i)).toList();

      return BookingListResponse(
        code: json['code'],
        msg: json['msg'],
        items: itemsList,
        paginator: Paginator.fromJson(json['data']['paginator']),
      );
    } else {
      return BookingListResponse(
        code: json['code'],
        msg: json['msg'],
        items: [],
        paginator: Paginator(prev: 0, next: 0, total: 0),
      );
    }
  }
}

class Plan {
  final String id;
  final String userId;
  final String courseId;
  final String players;
  final String? price;
  final String? priceName;
  final BookingStatus status;
  final String updateTime;
  final String insertTime;
  final TimeRange timeTange;
  final String courseName;
  final String? teetime;
  // 预定日期
  final String reservationDate;
  String showTime = '';
  String? courseWebsite;
  String? coursePhone;
  final bool allowSplit; // cancel if not enough
  final String? maxPrice; // budget price
  final bool hotOnly; // hot only
  final int expiredHours; // 添加过期小时数字段
  final Platform platform;
  final LastLog? lastlog; // 最后执行日志
  Plan({
    required this.id,
    required this.userId,
    required this.courseId,
    required this.players,
    required this.price,
    required this.status,
    required this.updateTime,
    required this.insertTime,
    required this.timeTange,
    required this.priceName,
    required this.courseName,
    required this.reservationDate,
    required this.teetime,
    required this.courseWebsite,
    required this.coursePhone,
    required this.allowSplit,
    required this.maxPrice,
    required this.hotOnly,
    required this.platform,
    this.expiredHours = 0, // 添加可选参数
    this.lastlog, // 最后执行日志，可选参数
  });

  formatShowTime() {
    // convert status to BookingStatus

    if (status == BookingStatus.success &&
        teetime != null &&
        teetime!.isNotEmpty) {
      // teetime 可能包含多个了。逗号隔开的
      List<String> teetimes = teetime!.split(',');
      String fixedTeetime = teetimes.first;
      showTime = fixedTeetime;

      // compare current time and date teetime, format to x days to go, y hous to go
      DateTime now = DateTime.now();
      DateTime teeTimeDate =
          DateTime.parse("$reservationDate $fixedTeetime:00");

      Duration difference = teeTimeDate.difference(now);

      if (difference.inDays > 0) {
        showTime = '${difference.inDays} days to go';
      } else if (difference.inHours > 0) {
        showTime = '${difference.inHours} hours to go';
      } else if (difference.inSeconds > 0) {
        showTime = 'Starting soon';
      } else {
        showTime = 'Past';
      }

      return this;
    }
    showTime = '${timeTange.start} - ${timeTange.end}';
    return this;
  }

  factory Plan.fromJson(Map<String, dynamic> json) {
    // 处理不同的数据结构
    String courseName;
    String? courseWebsite;
    String? coursePhone;

    if (json['Course'] != null) {
      // 详情接口的数据结构
      courseName = json['Course']['name'];
      courseWebsite = json['Course']['website'];
      coursePhone = json['Course']['phone'];
    } else {
      // 列表接口的数据结构
      courseName = json['course_name'] ?? '';
      courseWebsite = json['platform']?['website'];
      coursePhone = json['course']?['phone'];
    }

    return Plan(
      id: json['id'],
      userId: json['user_id'],
      courseId: json['course_id'],
      players: json['players'],
      price: json['price'],
      status: BookingStatusExtension.fromString(json['status']),
      updateTime: json['update_time'],
      insertTime: json['insert_time'],
      timeTange: TimeRange.fromJson(json['time_range']),
      courseName: courseName,
      priceName: json['price_name'],
      reservationDate: json['dates'],
      teetime: json['teetime'],
      courseWebsite: courseWebsite,
      coursePhone: coursePhone,
      allowSplit: json['allowsplit'] == '1',
      maxPrice: json['maxprice'],
      hotOnly: json['hotonly'] == '1',
      platform: json['platform'] != null
          ? Platform.fromJson(json['platform'])
          : Platform.fromJson(json['Platform']),
      expiredHours:
          json['expired_hours'] != null ? int.parse(json['expired_hours']) : 0,
      lastlog: json['lastlog'] != null &&
              json['lastlog'] is Map &&
              json['lastlog'].isNotEmpty
          ? LastLog.fromJson(json['lastlog'])
          : null,
    ).formatShowTime();
  }
}

// Booking detail request and response
class BookingDetailRequest {
  final String id;

  BookingDetailRequest({required this.id});

  Map<String, String> toJson() {
    return {'id': id};
  }
}

class BookingDetailResponse extends BaseResponse {
  final Plan item;

  BookingDetailResponse({
    required super.code,
    required super.msg,
    required this.item,
  }) : super(data: {});

  factory BookingDetailResponse.fromJson(Map<String, dynamic> json) {
    return BookingDetailResponse(
      code: json['code'],
      msg: json['msg'],
      item: Plan.fromJson(json['data']),
    );
  }
}

// delete booking request and response
class DeleteBookingRequest {
  final String id;

  DeleteBookingRequest({required this.id});

  Map<String, String> toJson() {
    return {'id': id};
  }
}

class DeleteBookingResponse extends BaseResponse {
  DeleteBookingResponse({
    required super.code,
    required super.msg,
    required super.data,
  });
}

// cancel booking request and response
class CancelBookingRequest {
  final String id;

  CancelBookingRequest({required this.id});

  Map<String, String> toJson() {
    return {'id': id};
  }
}

class CancelBookingResponse extends BaseResponse {
  CancelBookingResponse({
    required super.code,
    required super.msg,
    required super.data,
  });
}

// share booking detail request and response
class ShareBookingDetailRequest {
  final String id;

  ShareBookingDetailRequest({required this.id});

  Map<String, String> toJson() {
    return {'id': id};
  }
}

class ShareBookingDetailResponse extends BaseResponse {
  final ShareBookingDetail item;

  ShareBookingDetailResponse({
    required super.code,
    required super.msg,
    required this.item,
  }) : super(data: {});

  factory ShareBookingDetailResponse.fromJson(Map<String, dynamic> json) {
    return ShareBookingDetailResponse(
      code: json['code'],
      msg: json['msg'],
      item: ShareBookingDetail.fromJson(json['data']),
    );
  }
}

class ShareBookingDetail {
  final String id;
  final String userId;
  final String courseId;
  final String players;
  final String courseName;
  final String reservationDate;
  final String teetime;

  ShareBookingDetail({
    required this.id,
    required this.userId,
    required this.courseId,
    required this.players,
    required this.courseName,
    required this.reservationDate,
    required this.teetime,
  });

  factory ShareBookingDetail.fromJson(Map<String, dynamic> json) {
    return ShareBookingDetail(
      id: json['id'],
      userId: json['user_id'],
      courseId: json['course_id'],
      players: json['players'],
      courseName: json['Course']['name'],
      reservationDate: json['dates'],
      teetime: json['teetime'],
    );
  }
}

/// 最后执行日志模型
/// 用于存储 plan/get API 响应中的 lastlog 字段
class LastLog {
  final String id;
  final String userId;
  final String planId;
  final String planDates;
  final String? responseTeetime;
  final String? responseHotdeal;
  final String responsePlayer;
  final String responsePrice;
  final String message;
  final String level;
  final String insertTime;

  LastLog({
    required this.id,
    required this.userId,
    required this.planId,
    required this.planDates,
    this.responseTeetime,
    this.responseHotdeal,
    required this.responsePlayer,
    required this.responsePrice,
    required this.message,
    required this.level,
    required this.insertTime,
  });

  factory LastLog.fromJson(Map<String, dynamic> json) {
    // 处理空对象的情况
    if (json.isEmpty) {
      return LastLog(
        id: '',
        userId: '',
        planId: '',
        planDates: '',
        responseTeetime: null,
        responseHotdeal: null,
        responsePlayer: '',
        responsePrice: '',
        message: '',
        level: '',
        insertTime: '',
      );
    }

    return LastLog(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      planId: json['plan_id'] ?? '',
      planDates: json['plan_dates'] ?? '',
      responseTeetime: json['response_teetime'],
      responseHotdeal: json['response_hotdeal'],
      responsePlayer: json['response_player'] ?? '',
      responsePrice: json['response_price'] ?? '',
      message: json['message'] ?? '',
      level: json['level'] ?? '',
      insertTime: json['insert_time'] ?? '',
    );
  }
}

// planCourseList request and response
class SimpleCourse {
  final String id;
  final String clubId;
  final String name;

  SimpleCourse({
    required this.id,
    required this.clubId,
    required this.name,
    // Add other fields...
  });

  factory SimpleCourse.fromJson(Map<String, dynamic> json) {
    return SimpleCourse(
      id: json['id'],
      clubId: json['club_id'],
      name: json['name'],
    );
  }
}

/// 预订日志列表请求
class PlanLogListRequest {
  final String planId;
  final int page;
  final int size;

  PlanLogListRequest({
    required this.planId,
    required this.page,
    required this.size,
  });

  Map<String, String> toJson() {
    return {
      'plan_id': planId,
      'page': page.toString(),
      'size': size.toString(),
    };
  }
}

/// 预订日志项
class PlanLogItem {
  final String id;
  final String userId;
  final String planId;
  final String planDates;
  final String? responseTeetime;
  final String? responseHotdeal;
  final String responsePlayer;
  final String responsePrice;
  final String message;
  final String level;
  final String insertTime;
  final String? executeStartTime;
  final String? executeEndTime;

  PlanLogItem({
    required this.id,
    required this.userId,
    required this.planId,
    required this.planDates,
    this.responseTeetime,
    this.responseHotdeal,
    required this.responsePlayer,
    required this.responsePrice,
    required this.message,
    required this.level,
    required this.insertTime,
    this.executeStartTime,
    this.executeEndTime,
  });

  factory PlanLogItem.fromJson(Map<String, dynamic> json) {
    return PlanLogItem(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      planId: json['plan_id'] ?? '',
      planDates: json['plan_dates'] ?? '',
      responseTeetime: json['response_teetime'],
      responseHotdeal: json['response_hotdeal'],
      responsePlayer: json['response_player'] ?? '',
      responsePrice: json['response_price'] ?? '',
      message: json['message'] ?? '',
      level: json['level'] ?? '',
      insertTime: json['insert_time'] ?? '',
      executeStartTime: json['execute_starttime'],
      executeEndTime: json['execute_endtime'],
    );
  }
}

/// 预订日志列表响应
class PlanLogListResponse extends BaseResponse {
  final List<PlanLogItem> items;
  final Paginator paginator;

  PlanLogListResponse({
    required super.code,
    required super.msg,
    required this.items,
    required this.paginator,
  }) : super(data: {});

  factory PlanLogListResponse.fromJson(Map<String, dynamic> json) {
    if (json['data'] is Map && json['data']['items'] is List) {
      var itemsJson = json['data']['items'] as List;
      List<PlanLogItem> itemsList =
          itemsJson.map((i) => PlanLogItem.fromJson(i)).toList();

      return PlanLogListResponse(
        code: json['code'],
        msg: json['msg'],
        items: itemsList,
        paginator: Paginator.fromJson(json['data']['paginator']),
      );
    } else {
      return PlanLogListResponse(
        code: json['code'],
        msg: json['msg'],
        items: [],
        paginator: Paginator(prev: 0, next: 0, total: 0),
      );
    }
  }
}

class PlanCourseListResponse extends BaseResponse {
  final List<SimpleCourse> items;

  PlanCourseListResponse({
    required super.code,
    required super.msg,
    required this.items,
  }) : super(data: {});

  factory PlanCourseListResponse.fromJson(Map<String, dynamic> json) {
    // is json['data'] is a map and json['data']['items'] is a list
    if (json['data'] is Map && json['data']['items'] is List) {
      var itemsJson = json['data']['items'] as List;
      List<SimpleCourse> itemsList =
          itemsJson.map((i) => SimpleCourse.fromJson(i)).toList();

      return PlanCourseListResponse(
        code: json['code'],
        msg: json['msg'],
        items: itemsList,
      );
    } else {
      return PlanCourseListResponse(
        code: json['code'],
        msg: json['msg'],
        items: [],
      );
    }
  }
}
