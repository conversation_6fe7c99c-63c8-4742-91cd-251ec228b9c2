// common definitions for request and response
import 'dart:convert';

enum ResponseCode {
  success,
  invalidParam,
  failedEmail,
  failedPassword,
  authExpired,
  emailExisted,
  emailNotExisted,
  error,
  network,
}

extension ResponseCodeExtension on ResponseCode {
  static const statusValues = {
    ResponseCode.success: 0,
    ResponseCode.error: 999,
    ResponseCode.invalidParam: 1, // 参数错误
    ResponseCode.failedEmail: 2, // 邮箱发送失败
    ResponseCode.failedPassword: 3, // 密码错误
    ResponseCode.authExpired: 6, // token 过期
    ResponseCode.emailExisted: 7, // 注册时，邮箱已存在
    ResponseCode.emailNotExisted: 8, // 登录时，邮箱不存在
    ResponseCode.network: 9999, // 网络错误
  };

  int get value => statusValues[this] ?? 0;
  static ResponseCode fromInt(int value) {
    return statusValues.entries
        .firstWhere((entry) => entry.value == value,
            orElse: () => const MapEntry(ResponseCode.error, 0))
        .key;
  }
}

class BaseResponse {
  final int code;
  final String msg;
  final dynamic data;

  BaseResponse({required this.code, required this.msg, required this.data});

  factory BaseResponse.fromJson(Map<String, dynamic> json) {
    var dataFromJson = json['data'];
    dynamic data;

    if (dataFromJson is List) {
      data = List<dynamic>.from(dataFromJson);
    } else if (dataFromJson is Map) {
      data = dataFromJson;
    } else {
      data = dataFromJson;
    }

    return BaseResponse(
      code: json['code'],
      msg: json['msg'],
      data: data,
    );
  }
}

class Paginator {
  final int prev;
  final int next;
  final int total;

  Paginator({required this.prev, required this.next, required this.total});

  factory Paginator.fromJson(Map<String, dynamic> json) {
    return Paginator(
      prev: json['prev'],
      next: json['next'],
      total: json['total'],
    );
  }
}

class TimeRange {
  final String start;
  final String end;

  TimeRange({required this.start, required this.end});

  factory TimeRange.fromJson(Map<String, dynamic> json) {
    return TimeRange(
      start: json['start'],
      end: json['end'],
    );
  }

  factory TimeRange.fromJsonStr(String json) {
    Map<String, dynamic> map = jsonDecode(json);
    return TimeRange.fromJson(map);
  }

  Map<String, String> toJson() {
    return {
      'start': start,
      'end': end,
    };
  }
}
