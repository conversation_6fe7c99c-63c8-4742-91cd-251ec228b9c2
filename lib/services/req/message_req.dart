import 'dart:convert';
import 'package:golf/services/req/req.dart';

/// 消息列表请求
class MessageListRequest {
  final int page;
  final int size;
  final int status;
  final int? readstatus;
  final int? sendstatus;
  final String order;

  MessageListRequest({
    this.page = 1,
    this.size = 10,
    this.status = 1, // Always set status to 1 by default
    this.readstatus,
    this.sendstatus,
    this.order = 'id desc',
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> params = {
      'page': page,
      'size': size,
      'order': order,
      'status': status, // Always include status
    };

    // Only include readstatus if explicitly provided
    if (readstatus != null) {
      params['readstatus'] = readstatus!;
    }

    // Only include sendstatus if explicitly provided
    if (sendstatus != null) {
      params['sendstatus'] = sendstatus!;
    }

    return params;
  }
}

/// 消息列表响应
class MessageListResponse extends BaseResponse {
  final List<MessageItem> items;
  final Paginator paginator;
  final int unreadCount;

  MessageListResponse({
    required super.code,
    required super.msg,
    required this.items,
    required this.paginator,
    required this.unreadCount,
  }) : super(data: {});

  factory MessageListResponse.fromJson(Map<String, dynamic> json) {
    final items = (json['data']['items'] as List)
        .map((item) => MessageItem.fromJson(item))
        .toList();

    // Calculate unread count from items since API doesn't provide it directly
    final unreadCount = items.where((item) => !item.isRead).length;

    return MessageListResponse(
      code: json['code'],
      msg: json['msg'],
      items: items,
      paginator: Paginator.fromJson(json['data']['paginator']),
      unreadCount: unreadCount,
    );
  }
}

/// 消息项
class MessageItem {
  final String id;
  final String userId;
  final String title;
  final String body;
  final String datatype;
  final String data;
  final String sendstatus;
  final String readstatus;
  final String status;
  final DateTime insertTime;

  MessageItem({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.datatype,
    required this.data,
    required this.sendstatus,
    required this.readstatus,
    required this.status,
    required this.insertTime,
  });

  bool get isRead => readstatus == '1';
  String get type => 'general'; // Default type for backward compatibility
  DateTime get createdAt => insertTime; // Alias for backward compatibility
  Map<String, dynamic>? get extraData => data.isNotEmpty
      ? {'data': data}
      : null; // Alias for backward compatibility

  /// Parse the data field based on datatype
  Map<String, dynamic>? get parsedData {
    if (data.isEmpty) return null;
    try {
      final Map<String, dynamic> jsonData = Map<String, dynamic>.from(
        const JsonDecoder().convert(data),
      );
      return jsonData;
    } catch (e) {
      return null;
    }
  }

  /// Get plan_id from parsed data (for datatype = "1")
  String? get planId {
    final parsed = parsedData;

    /// 兼容 plan_id 和 PLANID 两种写法
    if (parsed != null) {
      if (parsed.containsKey('plan_id')) {
        return parsed['plan_id'].toString();
      } else if (parsed.containsKey('PLANID')) {
        return parsed['PLANID'].toString();
      }
    }
    return null;
  }

  factory MessageItem.fromJson(Map<String, dynamic> json) {
    return MessageItem(
      id: json['id'].toString(),
      userId: json['user_id'].toString(),
      title: json['title'] ?? '',
      body: json['body'] ?? '',
      datatype: json['datatype']?.toString() ?? '0',
      data: json['data'] ?? '{}',
      sendstatus: json['sendstatus'].toString(),
      readstatus: json['readstatus'].toString(),
      status: json['status'].toString(),
      insertTime: DateTime.parse(json['insert_time']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'type': type,
      'is_read': isRead,
      'created_at': createdAt.toIso8601String(),
      'extra_data': extraData,
    };
  }
}

/// 标记消息已读请求
class MarkMessageReadRequest {
  final int id;
  final String readstatus;

  MarkMessageReadRequest({
    required this.id,
    this.readstatus = '1',
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'readstatus': readstatus,
    };
  }
}

/// 标记消息已读响应
class MarkMessageReadResponse extends BaseResponse {
  MarkMessageReadResponse({
    required super.code,
    required super.msg,
    required super.data,
  });

  factory MarkMessageReadResponse.fromJson(Map<String, dynamic> json) {
    return MarkMessageReadResponse(
      code: json['code'],
      msg: json['msg'],
      data: json['data'],
    );
  }
}

/// 批量标记消息已读请求
class MarkAllMessagesReadRequest {
  final String readstatus;

  MarkAllMessagesReadRequest({this.readstatus = '1'});

  Map<String, dynamic> toJson() {
    return {
      'readstatus': readstatus,
    };
  }
}

/// 批量标记消息已读响应
class MarkAllMessagesReadResponse extends BaseResponse {
  MarkAllMessagesReadResponse({
    required super.code,
    required super.msg,
    required super.data,
  });

  factory MarkAllMessagesReadResponse.fromJson(Map<String, dynamic> json) {
    return MarkAllMessagesReadResponse(
      code: json['code'],
      msg: json['msg'],
      data: json['data'] ?? [],
    );
  }
}

/// 消息统计请求
class MessageStatusRequest {
  Map<String, dynamic> toJson() => {};
}

/// 消息统计响应
class MessageStatusResponse extends BaseResponse {
  final int unread;
  final int read;
  final int unsend;
  final int send;

  MessageStatusResponse({
    required super.code,
    required super.msg,
    required this.unread,
    required this.read,
    required this.unsend,
    required this.send,
  }) : super(data: {});

  factory MessageStatusResponse.fromJson(Map<String, dynamic> json) {
    final d = json['data'] ?? {};
    return MessageStatusResponse(
      code: json['code'],
      msg: json['msg'],
      unread: d['unread'] ?? 0,
      read: d['read'] ?? 0,
      unsend: d['unsend'] ?? 0,
      send: d['send'] ?? 0,
    );
  }
}
