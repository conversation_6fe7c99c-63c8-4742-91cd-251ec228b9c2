import 'package:golf/services/req/req.dart';

// 热门优惠分析请求
class HotDealAnalysisRequest {
  final String courseId;
  final String courseName;
  final String startTime;
  final String endTime;
  final int players;
  final List<String> dates;
  final double? budget;

  HotDealAnalysisRequest({
    required this.courseId,
    required this.courseName,
    required this.startTime,
    required this.endTime,
    required this.players,
    required this.dates,
    this.budget,
  });

  Map<String, dynamic> toJson() {
    return {
      'course_id': courseId,
      'course_name': courseName,
      'start_time': startTime,
      'end_time': endTime,
      'players': players,
      'dates': dates,
      if (budget != null) 'budget': budget,
    };
  }
}

// 热门优惠分析响应
class HotDealAnalysisResponse extends BaseResponse {
  final HotDealAnalysisData analysisData;

  HotDealAnalysisResponse({
    required super.code,
    required super.msg,
    required this.analysisData,
  }) : super(data: {});

  factory HotDealAnalysisResponse.fromJson(Map<String, dynamic> json) {
    return HotDealAnalysisResponse(
      code: json['code'],
      msg: json['msg'],
      analysisData: HotDealAnalysisData.fromJson(json['data']),
    );
  }
}

// 热门优惠分析数据
class HotDealAnalysisData {
  final String recommend;
  final HotDealAnalysis analysis;

  HotDealAnalysisData({
    required this.recommend,
    required this.analysis,
  });

  factory HotDealAnalysisData.fromJson(Map<String, dynamic> json) {
    return HotDealAnalysisData(
      recommend: json['recommend'] ?? '',
      analysis: HotDealAnalysis.fromJson(json['analysis'] ?? {}),
    );
  }
}

// 热门优惠分析详情
class HotDealAnalysis {
  final String dataStatus;
  final List<HotDealAdjustment> adjustments;
  final List<String>? commonHotDealTimes;
  final PlayerRequirements? playerRequirements;
  final PriceAnalysis? priceAnalysis;
  final DateAnalysis? dateAnalysis;

  HotDealAnalysis({
    required this.dataStatus,
    required this.adjustments,
    this.commonHotDealTimes,
    this.playerRequirements,
    this.priceAnalysis,
    this.dateAnalysis,
  });

  factory HotDealAnalysis.fromJson(Map<String, dynamic> json) {
    var adjustmentsList = <HotDealAdjustment>[];
    if (json['adjustments'] != null) {
      adjustmentsList = (json['adjustments'] as List)
          .map((item) => HotDealAdjustment.fromJson(item))
          .toList();
    }

    var hotDealTimes = <String>[];
    if (json['common_hot_deal_times'] != null) {
      hotDealTimes = List<String>.from(json['common_hot_deal_times']);
    }

    return HotDealAnalysis(
      dataStatus: json['data_status'] ?? '',
      adjustments: adjustmentsList,
      commonHotDealTimes: hotDealTimes.isNotEmpty ? hotDealTimes : null,
      playerRequirements: json['player_requirements'] != null
          ? PlayerRequirements.fromJson(json['player_requirements'])
          : null,
      priceAnalysis: json['price_analysis'] != null
          ? PriceAnalysis.fromJson(json['price_analysis'])
          : null,
      dateAnalysis: json['date_analysis'] != null
          ? DateAnalysis.fromJson(json['date_analysis'])
          : null,
    );
  }
}

// 调整建议
class HotDealAdjustment {
  final String type;
  final String status;
  final String suggestion;

  HotDealAdjustment({
    required this.type,
    required this.status,
    required this.suggestion,
  });

  factory HotDealAdjustment.fromJson(Map<String, dynamic> json) {
    return HotDealAdjustment(
      type: json['type'] ?? '',
      status: json['status'] ?? '',
      suggestion: json['suggestion'] ?? '',
    );
  }
}

// 玩家数量要求
class PlayerRequirements {
  final int minRequiredPlayers;
  final int userSelectedPlayers;

  PlayerRequirements({
    required this.minRequiredPlayers,
    required this.userSelectedPlayers,
  });

  factory PlayerRequirements.fromJson(Map<String, dynamic> json) {
    return PlayerRequirements(
      minRequiredPlayers: json['min_required_players'] ?? 1,
      userSelectedPlayers: json['user_selected_players'] ?? 1,
    );
  }
}

// 价格分析
class PriceAnalysis {
  final double minPrice;
  final double maxPrice;
  final double avgPrice;
  final double userBudget;

  PriceAnalysis({
    required this.minPrice,
    required this.maxPrice,
    required this.avgPrice,
    required this.userBudget,
  });

  factory PriceAnalysis.fromJson(Map<String, dynamic> json) {
    return PriceAnalysis(
      minPrice: (json['min_price'] ?? 0.0).toDouble(),
      maxPrice: (json['max_price'] ?? 0.0).toDouble(),
      avgPrice: (json['avg_price'] ?? 0.0).toDouble(),
      userBudget: (json['user_budget'] ?? 0.0).toDouble(),
    );
  }
}

// 日期分析
class DateAnalysis {
  final List<String> dates;
  final String status;
  final bool waitlistAvailable;
  final int recommendedAdvanceDays;

  DateAnalysis({
    required this.dates,
    required this.status,
    required this.waitlistAvailable,
    required this.recommendedAdvanceDays,
  });

  factory DateAnalysis.fromJson(Map<String, dynamic> json) {
    return DateAnalysis(
      dates: List<String>.from(json['dates'] ?? []),
      status: json['status'] ?? '',
      waitlistAvailable: json['waitlist_available'] ?? false,
      recommendedAdvanceDays: json['recommended_advance_days'] ?? 7,
    );
  }
}
