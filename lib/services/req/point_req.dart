import 'package:golf/services/req/req.dart';

/// 1. 获取积分余额
/// 2. 获取积分历史
/// 3. 获取积分规则

// 获取积分余额 request and response

class PointBalanceResponse extends BaseResponse {
  final int balance;

  PointBalanceResponse(
      {required this.balance, required super.code, required super.msg})
      : super(data: {});

  factory PointBalanceResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return PointBalanceResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      balance: json['data']['balance'],
    );
  }
}

// 获取积分历史 request and response
class PointHistoryRequest {
  final int page;
  final int size;

  PointHistoryRequest({required this.page, required this.size});

  Map<String, String> toJson() {
    return {
      'page': page.toString(),
      'size': size.toString(),
      "order": "id desc",
    };
  }
}

class PointDetail {
  final String description;
  final String date;
  final int points;
  final int id;

  PointDetail(
      {required this.description,
      required this.date,
      required this.points,
      required this.id});
}

class PointHistoryResponse extends BaseResponse {
  final List<PointDetail> items;
  final Paginator paginator;

  PointHistoryResponse(
      {required this.items,
      required super.code,
      required super.msg,
      required this.paginator})
      : super(data: {});

  factory PointHistoryResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    List<PointDetail> items = [];
    Paginator paginator = Paginator(prev: 0, next: 0, total: 0);
    try {
      items = List<PointDetail>.from(json['data']['items']
          .map((data) => PointHistoryData.fromJson(data).toPointDetail()));
      paginator = Paginator.fromJson(json['data']['paginator']);
    } catch (e) {
      paginator = Paginator(prev: 0, next: 0, total: 0);
      // ignore error
    }

    return PointHistoryResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      items: items,
      paginator: paginator,
    );
  }
}

class PointHistoryData {
  final String id;
  final String userId;
  final String points;
  final PointHistoryReasonData reason;
  final String status;
  final String updateTime;
  final String insertTime;

  PointHistoryData(
      {required this.id,
      required this.userId,
      required this.points,
      required this.reason,
      required this.status,
      required this.updateTime,
      required this.insertTime});
  // convert to PointDetail
  PointDetail toPointDetail() {
    return PointDetail(
        description: reason.invitee != null && reason.invitee!.contains("@")
            ? "${reason.reason} ${reason.invitee}"
            : reason.reason,
        date: updateTime,
        id: int.parse(id),
        points: int.parse(points));
  }

  factory PointHistoryData.fromJson(Map<String, dynamic> json) {
    return PointHistoryData(
      id: json['id'],
      userId: json['user_id'],
      points: json['points'],
      reason: PointHistoryReasonData.fromJson(json['reason']),
      status: json['status'],
      updateTime: json['update_time'],
      insertTime: json['insert_time'],
    );
  }
}

class PointHistoryReasonData {
  final String reason;
  final String? invitee;

  PointHistoryReasonData({required this.reason, required this.invitee});

  factory PointHistoryReasonData.fromJson(Map<String, dynamic> json) {
    return PointHistoryReasonData(
      reason: json['reason'],
      invitee: json['invitee'],
    );
  }
}

// 获取积分规则 request and response

class PointRuleResponse extends BaseResponse {
  final Map<String, int> pointrules;

  PointRuleResponse(
      {required this.pointrules, required super.code, required super.msg})
      : super(data: {});

  factory PointRuleResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return PointRuleResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      pointrules: Map<String, int>.from(json['data']['pointrules']),
    );
  }
}

// 兑换积分 request and response
class RedeemPointsRequest {
  final String code;

  RedeemPointsRequest({required this.code});

  Map<String, dynamic> toJson() => {'code': code};
}

class RedeemPointsResponse {
  final int code;
  final String msg;
  final String points;

  RedeemPointsResponse(
      {required this.code, required this.msg, required this.points});

  factory RedeemPointsResponse.fromJson(Map<String, dynamic> json) {
    return RedeemPointsResponse(
      code: json['code'],
      msg: json['msg'],
      points: json['data']['points'],
    );
  }
}
