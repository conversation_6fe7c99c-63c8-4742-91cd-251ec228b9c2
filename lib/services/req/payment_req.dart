import 'package:golf/services/req/req.dart';

// 支付包列表请求
class PaymentPackageListRequest {
  final int page;
  final int size;
  final String order;

  PaymentPackageListRequest({
    required this.page,
    required this.size,
    this.order = 'points asc',
  });

  Map<String, String> toJson() {
    return {
      'page': page.toString(),
      'size': size.toString(),
      'order': order,
    };
  }
}

// 支付包详情
class PaymentPackage {
  final String id;
  final String name;
  final int points;
  final double originalPrice;
  final double discountPrice;
  final double discountRate;
  final String description;
  final String status;
  final String updateTime;
  final String insertTime;

  PaymentPackage({
    required this.id,
    required this.name,
    required this.points,
    required this.originalPrice,
    required this.discountPrice,
    required this.discountRate,
    required this.description,
    required this.status,
    required this.updateTime,
    required this.insertTime,
  });

  factory PaymentPackage.fromJson(Map<String, dynamic> json) {
    return PaymentPackage(
      id: json['id'],
      name: json['name'],
      points: int.parse(json['points']),
      originalPrice: double.parse(json['original_price']),
      discountPrice: double.parse(json['discount_price']),
      discountRate: double.parse(json['discount_rate']),
      description: json['description'],
      status: json['status'],
      updateTime: json['update_time'],
      insertTime: json['insert_time'],
    );
  }
}

// 支付包列表响应
class PaymentPackageListResponse extends BaseResponse {
  final List<PaymentPackage> items;
  final Paginator paginator;

  PaymentPackageListResponse({
    required super.code,
    required super.msg,
    required this.items,
    required this.paginator,
  }) : super(data: {});

  factory PaymentPackageListResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    List<PaymentPackage> items = [];
    Paginator paginator = Paginator(prev: 0, next: 0, total: 0);

    if (json['data'] is Map && json['data']['items'] is List) {
      items = List<PaymentPackage>.from(
          json['data']['items'].map((x) => PaymentPackage.fromJson(x)));
      paginator = Paginator.fromJson(json['data']['paginator']);
    }

    return PaymentPackageListResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      items: items,
      paginator: paginator,
    );
  }
}

class PaymentIntentResponse extends BaseResponse {
  final String clientSecret;

  PaymentIntentResponse({
    required super.code,
    required super.msg,
    required this.clientSecret,
  }) : super(data: {});

  factory PaymentIntentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentIntentResponse(
      code: json['code'],
      msg: json['msg'],
      clientSecret: json['data']['client_secret'],
    );
  }
}

// 支付订单详情
class PaymentOrder {
  final String id;
  final String uuid;
  final String userId;
  final String packageId;
  final String clientSecret;
  final double amount;
  final int points;
  final String status;
  final String updateTime;
  final String insertTime;
  final PaymentPackage package;
  final String publicKey;

  PaymentOrder({
    required this.id,
    required this.uuid,
    required this.userId,
    required this.packageId,
    required this.clientSecret,
    required this.amount,
    required this.points,
    required this.status,
    required this.updateTime,
    required this.insertTime,
    required this.package,
    required this.publicKey,
  });

  factory PaymentOrder.fromJson(Map<String, dynamic> json) {
    return PaymentOrder(
      id: json['id'],
      uuid: json['uuid'],
      userId: json['user_id'],
      packageId: json['package_id'],
      clientSecret: json['client_secret'],
      amount: double.parse(json['amount']),
      points: int.parse(json['points']),
      status: json['status'],
      updateTime: json['update_time'],
      insertTime: json['insert_time'],
      package: PaymentPackage.fromJson(json['package']),
      publicKey: json['public_key'],
    );
  }
}

// 创建支付订单请求
class CreatePaymentOrderRequest {
  final String packageId;

  CreatePaymentOrderRequest({
    required this.packageId,
  });

  Map<String, dynamic> toJson() {
    return {
      'package_id': packageId,
    };
  }
}

// 支付订单响应
class CreatePaymentOrderResponse extends BaseResponse {
  final PaymentOrder order;

  CreatePaymentOrderResponse({
    required super.code,
    required super.msg,
    required this.order,
  }) : super(data: {});

  factory CreatePaymentOrderResponse.fromJson(Map<String, dynamic> json) {
    return CreatePaymentOrderResponse(
      code: json['code'],
      msg: json['msg'],
      order: PaymentOrder.fromJson(json['data']),
    );
  }
}

/// 订单详情请求
class PaymentOrderDetailRequest {
  final String orderId;

  PaymentOrderDetailRequest({required this.orderId});

  Map<String, dynamic> toJson() {
    return {
      'uuid': orderId,
    };
  }
}

/// 订单详情响应
class PaymentOrderDetailResponse {
  final PaymentOrderDetail order;

  PaymentOrderDetailResponse({required this.order});

  factory PaymentOrderDetailResponse.fromJson(Map<String, dynamic> json) {
    return PaymentOrderDetailResponse(
      order: PaymentOrderDetail.fromJson(json['data']),
    );
  }
}

/// 订单详情
class PaymentOrderDetail {
  final String id;
  final String uuid;
  final String status;
  final double amount;
  final int points;
  final String createdAt;
  final String? paidAt;
  final PaymentPackage? package;

  PaymentOrderDetail({
    required this.id,
    required this.uuid,
    required this.status,
    required this.amount,
    required this.points,
    required this.createdAt,
    this.paidAt,
    this.package,
  });

  factory PaymentOrderDetail.fromJson(Map<String, dynamic> json) {
    // 处理状态字段，可能是字符串或数字
    String status = '';
    if (json['status'] != null) {
      status = json['status'].toString();
    }

    return PaymentOrderDetail(
      id: json['id'] ?? '',
      uuid: json['uuid'] ?? '',
      status: status,
      amount: double.tryParse(json['amount']?.toString() ?? '0') ?? 0,
      points: int.tryParse(json['points']?.toString() ?? '0') ?? 0,
      createdAt: json['insert_time'] ?? json['created_at'] ?? '',
      paidAt: json['paid_at'] ?? json['update_time'],
      package: json['package'] != null
          ? PaymentPackage.fromJson(json['package'])
          : null,
    );
  }
}
