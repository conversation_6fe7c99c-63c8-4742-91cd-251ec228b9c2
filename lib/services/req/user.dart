// link account request and response
// email with code and club_id
import 'package:golf/utils/logger.dart';
import 'package:golf/services/req/platform_req.dart';
import 'package:golf/services/req/req.dart';

class BindSendEmailRequest {
  final String email;
  final String platformId;

  BindSendEmailRequest({
    required this.email,
    required this.platformId,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'platform_id': platformId,
    };
  }
}

class BindSendEmailResponse extends BaseResponse {
  BindSendEmailResponse(
      {required super.code, required super.msg, required super.data});
  factory BindSendEmailResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return BindSendEmailResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      data: baseResponse.data,
    );
  }
}

class LinkAccountByCodeRequest {
  final String email;
  final String code;
  final String platformId;

  LinkAccountByCodeRequest(
      {required this.email, required this.code, required this.platformId});

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'code': code,
      'platform_id': platformId,
    };
  }
}

class LinkAccountResponse extends BaseResponse {
  LinkAccountResponse(
      {required super.code, required super.msg, required super.data});
  factory LinkAccountResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return LinkAccountResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      data: baseResponse.data,
    );
  }
}

class LinkAccountByPasswordRequest {
  final String email;
  final String password;
  final String platformId;

  LinkAccountByPasswordRequest(
      {required this.email, required this.password, required this.platformId});

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'platform_id': platformId,
    };
  }
}

// linked account request and response
// no param
class LinkedAccountRequest {
  Map<String, dynamic> toJson() {
    return {};
  }
}

class LinkedAccountResponse extends BaseResponse {
  final List<LinkedAccount> items;
  final Paginator paginator;

  LinkedAccountResponse({
    required super.code,
    required super.msg,
    required this.items,
    required this.paginator,
  }) : super(data: {});

  factory LinkedAccountResponse.fromJson(Map<String, dynamic> json) {
    try {
      var itemsJson = json['data']['items'] as List;
      List<LinkedAccount> itemsList =
          itemsJson.map((i) => LinkedAccount.fromJson(i)).toList();

      return LinkedAccountResponse(
        code: json['code'],
        msg: json['msg'],
        items: itemsList,
        paginator: Paginator.fromJson(json['data']['paginator']),
      );
    } catch (e) {
      AppLogger.info('LinkedAccountResponse.fromJson error: $e', "API");
      return LinkedAccountResponse(
        code: json['code'],
        msg: json['msg'],
        items: [],
        paginator: Paginator(
          next: 0,
          prev: 0,
          total: 0,
        ),
      );
    }
  }
}

class Club {
  final String id;
  final String name;
  final String status;
  final String updateTime;
  final String insertTime;

  Club({
    required this.id,
    required this.name,
    required this.status,
    required this.updateTime,
    required this.insertTime,
  });

  factory Club.fromJson(Map<String, dynamic> json) {
    assert(json['id'] != null);
    assert(json['name'] != null);
    return Club(
      id: json['id'],
      name: json['name'],
      status: json['status'],
      updateTime: json['update_time'],
      insertTime: json['insert_time'],
    );
  }
}

class LinkedAccount {
  final String id;
  final String userId;
  final String platformId;
  // final String courseId;
  final String status;
  final String? bindAccount; //  先兼容下，服务端返回null是不正确的
  final String? bindDateTime;
  String available = 'Active';
  final Platform platform;

  LinkedAccount({
    required this.id,
    required this.userId,
    required this.platformId,
    // required this.courseId,
    required this.status,
    required this.bindAccount,
    required this.platform,
    required this.bindDateTime,
  });

  // 根据 bindAccount, BindDateTime, status 来更新 available
  LinkedAccount updateAvailable() {
    if (status == '1') {
      available = 'Active';
    } else {
      available = 'Inactive';
    }
    return this;
  }

  factory LinkedAccount.fromJson(Map<String, dynamic> json) {
    // assert field not null
    assert(json['id'] != null);
    assert(json['user_id'] != null);
    assert(json['platform_id'] != null);
    // assert(json['course_id'] != null);
    assert(json['status'] != null);
    // assert(json['bind_account'] != null);
    // assert(json['bind_datetime'] != null);

    assert(json['platform'] != null);

    return LinkedAccount(
      id: json['id'],
      userId: json['user_id'],
      platformId: json['platform_id'],
      // courseId: json['course_id'],
      status: json['status'],
      bindAccount: json['bind_account'],
      bindDateTime: json['bind_datetime'],
      platform: Platform.fromJson(json['platform']),
    ).updateAvailable();
  }
}

// deleteAccount request and response
// with param code
class DeleteAccountRequest {
  final String code;
  final String token;

  DeleteAccountRequest({required this.code, required this.token});

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'token': token,
    };
  }
}

class DeleteAccountResponse extends BaseResponse {
  DeleteAccountResponse(
      {required super.code, required super.msg, required super.data});
  factory DeleteAccountResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return DeleteAccountResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      data: baseResponse.data,
    );
  }
}

// change password request and response
// with param old_password and new_password
class ChangePasswordRequest {
  final String oldPassword;
  final String newPassword;

  ChangePasswordRequest({required this.oldPassword, required this.newPassword});

  Map<String, dynamic> toJson() {
    return {
      'currentpassword': oldPassword,
      'password': newPassword,
      'checkpass': newPassword,
    };
  }
}

class ChangePasswordResponse extends BaseResponse {
  ChangePasswordResponse(
      {required super.code, required super.msg, required super.data});
  factory ChangePasswordResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return ChangePasswordResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      data: baseResponse.data,
    );
  }
}

// 获取用户详情response
class UserDetail {
  final String id;
  final String email;
  final String status;
  final String points;
  final String updateTime;
  final String insertTime;

  UserDetail({
    required this.id,
    required this.email,
    required this.status,
    required this.points,
    required this.updateTime,
    required this.insertTime,
  });

  factory UserDetail.fromJson(Map<String, dynamic> json) {
    return UserDetail(
      id: json['id'],
      email: json['email'],
      status: json['status'],
      points: json['points'],
      updateTime: json['update_time'],
      insertTime: json['insert_time'],
    );
  }
}

class UserDetailResponse extends BaseResponse {
  final UserDetail user;

  UserDetailResponse({
    required super.code,
    required super.msg,
    required this.user,
  }) : super(data: {});

  factory UserDetailResponse.fromJson(Map<String, dynamic> json) {
    return UserDetailResponse(
      code: json['code'],
      msg: json['msg'],
      user: UserDetail.fromJson(json['data']['user']),
    );
  }
}
