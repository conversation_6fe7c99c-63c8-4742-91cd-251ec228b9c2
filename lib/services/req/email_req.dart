// send email request and response

import 'package:golf/services/req/auth_req.dart';
import 'package:golf/services/req/req.dart';

// define enum of email type, mapping to int
enum EmailType { register, reset, changeEmail, deleteAccount }

// extend the enum to get the value
extension EmailTypeExtension on EmailType {
  static const emailTypeValues = {
    EmailType.register: 1,
    EmailType.reset: 2,
    EmailType.changeEmail: 4,
    EmailType.deleteAccount: 5,
  };
  int get value => emailTypeValues[this] ?? 1;
}

class SendEmailRequest {
  final String email;
  final EmailType type;
  final String? clubId; // 可选的，绑定账号时才需要，有些前置条件

  SendEmailRequest({required this.email, required this.type, this.clubId});

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'type': type.value.toString(),
      'club_id': clubId,
    };
  }
}

class SendEmailResponse extends BaseResponse {
  SendEmailResponse(
      {required super.code, required super.msg, required super.data});
  factory SendEmailResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return SendEmailResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      data: TokenData.fromJson(json['data']),
    );
  }
}

// verify email request and response
// field with email and code, type
class VerifyEmailRequest {
  final String email;
  final String code;
  final EmailType type;

  VerifyEmailRequest(
      {required this.email, required this.code, required this.type});

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'code': code,
      'type': type.value.toString(),
    };
  }
}

// reponse with code, msg, and data, data with token
class VerifyEmailResponse extends BaseResponse {
  VerifyEmailResponse(
      {required super.code, required super.msg, required super.data});
  factory VerifyEmailResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return VerifyEmailResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      data: VerifyEmailData.fromJson(json['data']),
    );
  }
}

// Check email request and response
// field with email
class CheckEmailRequest {
  final String email;

  CheckEmailRequest({required this.email});

  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }
}

class CheckEmailResponse extends BaseResponse {
  final bool exist;
  CheckEmailResponse(
      {required super.code,
      required super.msg,
      required super.data,
      required this.exist});
  factory CheckEmailResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return CheckEmailResponse(
        code: baseResponse.code,
        msg: baseResponse.msg,
        data: baseResponse.data,
        exist: json['data']['exist']);
  }
}

class VerifyEmailData {
  final String token;

  VerifyEmailData({required this.token});

  factory VerifyEmailData.fromJson(Map<String, dynamic> json) {
    return VerifyEmailData(
      token: json['token'],
    );
  }
}

// change email request and response
// field with email and code and password
class ChangeEmailRequest {
  final String email;
  final String code;
  final String password;
  final String token;

  ChangeEmailRequest(
      {required this.password,
      required this.email,
      required this.code,
      required this.token});

  Map<String, dynamic> toJson() {
    return {
      'password': password,
      'email': email,
      'code': code,
      'token': token,
    };
  }
}

class ChangeEmailResponse extends BaseResponse {
  ChangeEmailResponse(
      {required super.code, required super.msg, required super.data});
  factory ChangeEmailResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return ChangeEmailResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      data: baseResponse.data,
    );
  }
}
