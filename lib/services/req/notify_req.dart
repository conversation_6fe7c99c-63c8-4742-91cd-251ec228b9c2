// define a enum for status it mappings
import 'package:golf/services/req/booking_req.dart';
/**
 * 1. 提交notification
 * 2. 获取notification列表
 * 3. 获取notification关联的球场信息
 * 4. 取消notification
 */
import 'package:golf/services/req/req.dart';

enum NotificationStatus { all, success, running, failed }

extension NotificationStatusExtension on NotificationStatus {
  static const statusValues = {
    NotificationStatus.all: 0,
    NotificationStatus.success: 1,
    NotificationStatus.running: 2,
    NotificationStatus.failed: 3,
  };
  int get value => statusValues[this] ?? 1;
  String get name {
    switch (this) {
      case NotificationStatus.all:
        return 'All';
      case NotificationStatus.success:
        return 'Successful';
      case NotificationStatus.running:
        return 'Running';
      case NotificationStatus.failed:
        return 'Failed';
      default:
        return 'Successful';
    }
  }

  static NotificationStatus fromString(String value) {
    switch (value) {
      case 'All':
        return NotificationStatus.all;
      case 'Running':
        return NotificationStatus.running;
      case 'Successful':
        return NotificationStatus.success;
      case 'Failed':
        return NotificationStatus.failed;
      case '1':
        return NotificationStatus.success;
      case '2':
        return NotificationStatus.running;
      case '3':
        return NotificationStatus.failed;
      default:
        return NotificationStatus.running;
    }
  }
}

// Notification list request and response
class NotificationListRequest {
  final String courseId;
  final NotificationStatus status;
  final int pageNum;
  final int pageSize;

  NotificationListRequest({
    required this.courseId,
    required this.status,
    required this.pageNum,
    required this.pageSize,
  });

  Map<String, String> toJson() {
    return {
      'course_id': courseId,
      'status': status.value.toString(),
      'page': pageNum.toString(),
      'size': pageSize.toString(),
      "order": "dates desc, time_start desc",
    };
  }
}

class NotificationListResponse extends BaseResponse {
  final List<NotificationData> items;
  final Paginator paginator;

  NotificationListResponse({
    required super.code,
    required super.msg,
    required this.items,
    required this.paginator,
  }) : super(data: {});

  factory NotificationListResponse.fromJson(Map<String, dynamic> json) {
    // is json['data'] is a map and json['data']['items'] is a list
    if (json['data'] is Map && json['data']['items'] is List) {
      var itemsJson = json['data']['items'] as List;
      List<NotificationData> itemsList =
          itemsJson.map((i) => NotificationData.fromJson(i)).toList();

      return NotificationListResponse(
        code: json['code'],
        msg: json['msg'],
        items: itemsList,
        paginator: Paginator.fromJson(json['data']['paginator']),
      );
    } else {
      return NotificationListResponse(
        code: json['code'],
        msg: json['msg'],
        items: [],
        paginator: Paginator(prev: 0, next: 0, total: 0),
      );
    }
  }
}

class NotificationData {
  final String id;
  final String userId;
  final String courseId;
  final String players;
  final String? price;
  final String? priceName;
  final String? teeTime;
  final String status;
  final String updateTime;
  final String dates;
  final TimeRange timeTange;
  final String courseName;
  // 预定日期
  final String reservationDate;
  String showTime = '';

  NotificationData({
    required this.id,
    required this.userId,
    required this.courseId,
    required this.players,
    required this.price,
    required this.status,
    required this.updateTime,
    required this.dates,
    required this.timeTange,
    required this.priceName,
    required this.courseName,
    required this.reservationDate,
    required this.teeTime,
  });

  formatShowTime() {
    // convert status to NotificationStatus

    showTime = '${timeTange.start} - ${timeTange.end}';
    return this;
  }

  factory NotificationData.fromJson(Map<String, dynamic> json) {
    return NotificationData(
      id: json['id'],
      userId: json['user_id'],
      courseId: json['course_id'],
      players: json['players'],
      price: json['price'],
      status: json['status'],
      updateTime: json['update_time'],
      dates: json['dates'],
      timeTange: TimeRange.fromJson(json['time_range']),
      courseName: json['course_name'],
      priceName: json['price_name'],
      reservationDate: json['dates'],
      teeTime: json['teetime'],
    ).formatShowTime();
  }
}

// cancel Notification request and response
class DeleteNotificationRequest {
  final String id;

  DeleteNotificationRequest({required this.id});

  Map<String, String> toJson() {
    return {'id': id};
  }
}

class CancelNotificationResponse extends BaseResponse {
  CancelNotificationResponse({
    required super.code,
    required super.msg,
    required super.data,
  });
}

class NotificationCourseListResponse extends BaseResponse {
  final List<SimpleCourse> items;

  NotificationCourseListResponse({
    required super.code,
    required super.msg,
    required this.items,
  }) : super(data: {});

  factory NotificationCourseListResponse.fromJson(Map<String, dynamic> json) {
    // is json['data'] is a map and json['data']['items'] is a list
    if (json['data'] is Map && json['data']['items'] is List) {
      var itemsJson = json['data']['items'] as List;
      List<SimpleCourse> itemsList =
          itemsJson.map((i) => SimpleCourse.fromJson(i)).toList();

      return NotificationCourseListResponse(
        code: json['code'],
        msg: json['msg'],
        items: itemsList,
      );
    } else {
      return NotificationCourseListResponse(
        code: json['code'],
        msg: json['msg'],
        items: [],
      );
    }
  }
}
