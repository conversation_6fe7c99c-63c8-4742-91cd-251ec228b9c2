import 'package:golf/services/req/course_req.dart';
import 'package:golf/services/req/req.dart';

// search golf course request and response
class SearchCourseRequest {
  final String keyword;
  final int pageNum;
  final int pageSize;
  LatLng? location;
  final bool? used;
  final bool? isFavorite;
  final int? holes;
  final String? platformId;

  SearchCourseRequest(
      {required this.keyword,
      required this.pageNum,
      required this.pageSize,
      this.location,
      this.used,
      this.isFavorite,
      this.holes,
      this.platformId});

  ListCourseRequest toListCourseRequest() {
    return ListCourseRequest(
      page: pageNum,
      size: pageSize,
      status: null,
      order: null,
      location: location,
      kw: keyword,
      used: used,
      isFavorite: isFavorite,
      holes: holes,
      platformId: platformId,
    );
  }
}

class NearByCourseRequest {
  final int? page;
  final int? size;
  LatLng? location;
  int? holes;
  String? platformId;

  NearByCourseRequest({
    required this.page,
    required this.size,
    this.location,
    this.holes,
    this.platformId,
  });

  ListCourseRequest toListCourseRequest() {
    return ListCourseRequest(
      page: page,
      size: size,
      status: null,
      // order: "distance asc",
      order: null,
      location: location,
      kw: '',
      holes: holes,
      platformId: platformId,
    );
  }
}

class UsedCourseRequest {
  final int? page;
  final int? size;
  LatLng? location;
  int? holes;
  String? platformId;
  UsedCourseRequest({
    required this.page,
    required this.size,
    this.location,
    this.holes,
    this.platformId,
  });

  ListCourseRequest toListCourseRequest() {
    return ListCourseRequest(
      page: page,
      size: size,
      status: null,
      // order: "distance asc",
      order: null,
      location: location,
      kw: '',
      used: true,
      holes: holes,
      platformId: platformId,
    );
  }
}

class FavoriteCourseRequest {
  final int? page;
  final int? size;
  LatLng? location;
  int? holes;
  String? platformId;
  FavoriteCourseRequest({
    required this.page,
    required this.size,
    this.location,
    this.holes,
    this.platformId,
  });

  ListCourseRequest toListCourseRequest() {
    return ListCourseRequest(
      page: page,
      size: size,
      status: null,
      // order: "distance asc",
      order: null,
      location: location,
      kw: '',
      used: null,
      isFavorite: true,
      holes: holes,
      platformId: platformId,
    );
  }
}

class SearchCourseResponse extends BaseResponse {
  final List<Course> items;
  final Paginator paginator;

  SearchCourseResponse({
    required super.code,
    required super.msg,
    required this.items,
    required this.paginator,
  }) : super(data: {});

  factory SearchCourseResponse.fromJson(Map<String, dynamic> json) {
    var itemsJson = json['data']['items'] as List;
    List<Course> itemsList = itemsJson.map((i) => Course.fromJson(i)).toList();

    return SearchCourseResponse(
      code: json['code'],
      msg: json['msg'],
      items: itemsList,
      paginator: Paginator.fromJson(json['data']['paginator']),
    );
  }
}
