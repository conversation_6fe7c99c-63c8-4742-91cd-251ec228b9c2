// login request and response
import 'package:golf/services/req/req.dart';

class LoginRequest {
  final String email;
  final String password;

  LoginRequest({required this.email, required this.password});

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
    };
  }
}

class LoginResponse extends BaseResponse {
  LoginResponse({required super.code, required super.msg, required super.data});

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    // it'ok if code is 0, else data is []
    if (json['code'] != 0) {
      return LoginResponse(
        code: json['code'],
        msg: json['msg'],
        data: [],
      );
    }
    return LoginResponse(
      code: json['code'],
      msg: json['msg'],
      data: TokenData.fromJson(json['data']),
    );
  }
}

class TokenData {
  final String token;

  TokenData({required this.token});

  factory TokenData.fromJson(Map<String, dynamic> json) {
    return TokenData(
      token: json['token'],
    );
  }
}

// registration request and response
class RegisterRequest {
  final String email;
  final String password;
  final String code;
  final String token; //发送邮件的验证码
  final String invitor; //邀请人

  RegisterRequest(
      {required this.email,
      required this.password,
      required this.code,
      required this.token,
      required this.invitor});

  Map<String, dynamic> toJson() {
    // invitor is id-username, should be id
    final invitorId = invitor.split('-')[0];
    return {
      'email': email,
      'password': password,
      'checkpass': password,
      'code': code,
      'token': token,
      'invitor': invitorId,
    };
  }
}

class RegisterResponse extends BaseResponse {
  RegisterResponse(
      {required super.code, required super.msg, required super.data});

  factory RegisterResponse.fromJson(Map<String, dynamic> json) {
    // it'ok if code is 0, else data is []
    if (json['code'] != 0) {
      return RegisterResponse(
        code: json['code'],
        msg: json['msg'],
        data: [],
      );
    }
    return RegisterResponse(
      code: json['code'],
      msg: json['msg'],
      data: TokenData.fromJson(json['data']),
    );
  }
}

// reset password with token request and response
class ResetPasswordRequest {
  final String password;
  final String code;
  final String email;
  final String token;

  ResetPasswordRequest(
      {required this.password,
      required this.token,
      required this.code,
      required this.email});

  Map<String, dynamic> toJson() {
    return {
      'password': password,
      'checkpass': password,
      'token': token,
      'code': code,
      'email': email,
    };
  }
}

class ResetPasswordResponse extends BaseResponse {
  ResetPasswordResponse(
      {required super.code, required super.msg, required super.data});

  factory ResetPasswordResponse.fromJson(Map<String, dynamic> json) {
    // it'ok if code is 0, else data is []
    if (json['code'] != 0) {
      return ResetPasswordResponse(
        code: json['code'],
        msg: json['msg'],
        data: [],
      );
    }
    return ResetPasswordResponse(
      code: json['code'],
      msg: json['msg'],
      data: TokenData.fromJson(json['data']),
    );
  }
}
