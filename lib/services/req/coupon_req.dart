import 'package:golf/services/req/req.dart';

/// 优惠码相关接口
/// 1. 获取优惠码状态列表
/// 2. 添加优惠码
/// 3. 获取优惠码列表

// 获取优惠码状态列表 response
class CouponStatusResponse extends BaseResponse {
  final Map<String, String> statuses;

  CouponStatusResponse(
      {required this.statuses, required super.code, required super.msg})
      : super(data: {});

  factory CouponStatusResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return CouponStatusResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      statuses: Map<String, String>.from(json['data'] ?? {}),
    );
  }
}

// 添加优惠码 request and response
class AddCouponRequest {
  final String code;
  final String? expiryDate;

  AddCouponRequest({required this.code, this.expiryDate});

  Map<String, dynamic> toJson() {
    Map<String, dynamic> data = {
      'code': code,
    };
    if (expiryDate != null && expiryDate!.isNotEmpty) {
      data['expiry_date'] = expiryDate;
    }
    return data;
  }
}

class AddCouponResponse extends BaseResponse {
  AddCouponResponse({required super.code, required super.msg})
      : super(data: {});

  factory AddCouponResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    return AddCouponResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
    );
  }
}

// 获取优惠码列表 request and response
class CouponListRequest {
  final int page;
  final int size;
  final int? status;
  final String order;

  CouponListRequest({
    required this.page,
    required this.size,
    this.status,
    this.order = "id desc",
  });

  Map<String, String> toJson() {
    Map<String, String> data = {
      'page': page.toString(),
      'size': size.toString(),
      'order': order,
    };
    if (status != null) {
      data['status'] = status.toString();
    }
    return data;
  }

  Map<String, dynamic> toJsonBody() {
    Map<String, dynamic> data = {
      'page': page,
      'size': size,
      'order': order,
    };
    if (status != null) {
      data['status'] = status;
    }
    return data;
  }
}

class CouponDetail {
  final String id;
  final String code;
  final String status;
  final String statusText;
  final String expiryDate;
  final String insertTime;
  final String updateTime;

  CouponDetail({
    required this.id,
    required this.code,
    required this.status,
    required this.statusText,
    required this.expiryDate,
    required this.insertTime,
    required this.updateTime,
  });

  factory CouponDetail.fromJson(Map<String, dynamic> json) {
    return CouponDetail(
      id: json['id']?.toString() ?? '',
      code: json['code']?.toString() ?? '',
      status: json['status']?.toString() ?? '',
      statusText: json['status_text']?.toString() ?? '',
      expiryDate: json['expiry_date']?.toString() ?? '',
      insertTime: json['insert_time']?.toString() ?? '',
      updateTime: json['update_time']?.toString() ?? '',
    );
  }
}

class CouponListResponse extends BaseResponse {
  final List<CouponDetail> items;
  final Paginator paginator;

  CouponListResponse({
    required this.items,
    required super.code,
    required super.msg,
    required this.paginator,
  }) : super(data: {});

  factory CouponListResponse.fromJson(Map<String, dynamic> json) {
    BaseResponse baseResponse = BaseResponse.fromJson(json);
    List<CouponDetail> items = [];
    Paginator paginator = Paginator(prev: 0, next: 0, total: 0);

    try {
      if (json['data'] != null) {
        if (json['data']['items'] != null) {
          items = List<CouponDetail>.from(
              json['data']['items'].map((data) => CouponDetail.fromJson(data)));
        }
        if (json['data']['paginator'] != null) {
          paginator = Paginator.fromJson(json['data']['paginator']);
        }
      }
    } catch (e) {
      paginator = Paginator(prev: 0, next: 0, total: 0);
      // ignore error
    }

    return CouponListResponse(
      code: baseResponse.code,
      msg: baseResponse.msg,
      items: items,
      paginator: paginator,
    );
  }
}
