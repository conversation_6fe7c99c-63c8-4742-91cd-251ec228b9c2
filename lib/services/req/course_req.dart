// course list request and response

import 'package:flutter/material.dart';
import 'package:golf/services/req/booking_req.dart';
import 'package:golf/services/req/platform_req.dart';
import 'package:golf/services/req/req.dart';

class LatLng {
  final double latitude;
  final double longitude;

  LatLng({
    required this.latitude,
    required this.longitude,
  });

  List<dynamic> toJson() {
    return [longitude, latitude];
  }

  factory LatLng.fromJson(Map<String, dynamic> json) {
    return LatLng(
      latitude: json['latitude'],
      longitude: json['longitude'],
    );
  }
}

class ListCourseRequest {
  final int? page;
  final int? size;
  final int? status;
  String? order;
  LatLng? location;
  String? kw;
  final bool? used;
  final bool? isFavorite;
  final int? holes;
  final String? platformId;

  ListCourseRequest({
    required this.page,
    required this.size,
    this.status,
    this.order,
    this.location,
    this.kw,
    this.used,
    this.isFavorite,
    this.holes,
    this.platformId,
  });

  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = {};
    if (page != null) {
      json['page'] = page;
    }
    if (size != null) {
      json['size'] = size;
    }
    if (status != null) {
      json['status'] = status;
    }
    if (order != null) {
      json['order'] = order;
    }
    if (location != null) {
      json['location'] = location!.toJson();
    }
    if (kw != null) {
      json['kw'] = kw;
    }
    if (used != null) {
      json['used'] = used;
    }
    if (isFavorite != null) {
      json['favorited'] = isFavorite;
    }
    if (holes != null) {
      json['holes'] = holes;
    }
    if (platformId != null) {
      // json['platform_id'] = platformId;
      json['platform_filter_id'] = platformId;
    }
    return json;
  }
}

class FavoriteItem {
  final String id;
  final String userId;
  final String courseId;
  final String status;
  final String updateTime;
  final String insertTime;
  final SearchCourseItem course;

  FavoriteItem({
    required this.id,
    required this.userId,
    required this.courseId,
    required this.status,
    required this.updateTime,
    required this.insertTime,
    required this.course,
  });

  factory FavoriteItem.fromJson(Map<String, dynamic> json) {
    return FavoriteItem(
      id: json['id'],
      userId: json['user_id'],
      courseId: json['course_id'],
      status: json['status'],
      updateTime: json['update_time'],
      insertTime: json['insert_time'],
      course: SearchCourseItem.fromJson(json['course']),
    );
  }
}

class CourseResponse extends BaseResponse {
  final List<SearchCourseItem> items;
  final Paginator paginator;

  CourseResponse({
    required super.code,
    required super.msg,
    required this.items,
    required this.paginator,
  }) : super(data: {});

  factory CourseResponse.fromJson(Map<String, dynamic> json) {
    if (json['data'] is List || json['data'].isEmpty) {
      return CourseResponse(
        code: json['code'],
        msg: json['msg'],
        items: [],
        paginator: Paginator(prev: 0, next: 0, total: 0),
      );
    }
    var itemsJson = json['data']['items'] as List;
    List<SearchCourseItem> itemsList =
        itemsJson.map((i) => SearchCourseItem.fromJson(i)).toList();

    return CourseResponse(
      code: json['code'],
      msg: json['msg'],
      items: itemsList,
      paginator: Paginator.fromJson(json['data']['paginator']),
    );
  }
}

class FavoriteListResponse extends BaseResponse {
  final List<FavoriteItem> items;
  final Paginator paginator;

  FavoriteListResponse({
    required super.code,
    required super.msg,
    required this.items,
    required this.paginator,
  }) : super(data: {});

  factory FavoriteListResponse.fromJson(Map<String, dynamic> json) {
    if (json['data'] is List || json['data'].isEmpty) {
      return FavoriteListResponse(
        code: json['code'],
        msg: json['msg'],
        items: [],
        paginator: Paginator(prev: 0, next: 0, total: 0),
      );
    }
    var itemsJson = json['data']['items'] as List;
    List<FavoriteItem> itemsList =
        itemsJson.map((i) => FavoriteItem.fromJson(i)).toList();

    return FavoriteListResponse(
      code: json['code'],
      msg: json['msg'],
      items: itemsList,
      paginator: Paginator.fromJson(json['data']['paginator']),
    );
  }

  CourseResponse toCourseResponse() {
    List<SearchCourseItem> searchCourseItems = items
        .map((favoriteItem) {
          favoriteItem.course.isFavorite = true;
          return favoriteItem.course;
        })
        .cast<SearchCourseItem>()
        .toList();
    return CourseResponse(
      code: code,
      msg: msg,
      items: searchCourseItems,
      paginator: paginator,
    );
  }
}

class Price {
  final String label;
  final String value;

  Price({required this.label, required this.value});
}

List<Price> fromJson(Map<String, dynamic> jsonMap) {
  return jsonMap.entries
      .map((e) => Price(label: e.value, value: e.key))
      .toList();
}

class CourseDetailResponse extends BaseResponse {
  final Course course;

  CourseDetailResponse({
    required super.code,
    required super.msg,
    required this.course,
  }) : super(data: {});

  factory CourseDetailResponse.fromJson(Map<String, dynamic> json) {
    return CourseDetailResponse(
      code: json['code'],
      msg: json['msg'],
      course: Course.fromJson(json['data']),
    );
  }
}

class Course {
  final String id;
  final String clubId;
  final String? outerId;
  final String name;
  bool binded = false; // 是否已绑定
  final String? bindAccount; //
  final bool hasToken; // 是否有token
  final List<Price> prices; // 价格列表
  // final LinkType linkType; // 绑定账号类型（可扩展)，密码还是邮箱验证码，1: 验证码，2: 密码
  // 可选时间范围
  final TimeOfDay startTime; // 开始时间
  final TimeOfDay endTime; // 结束时间
  // 可选日期范围
  final DateTime bookStartDate; // 开始日期 date only
  final DateTime bookEndDate; // 开始日期 date only
  final DateTime notifyStartDate; // 开始日期 date only
  final DateTime notifyEndDate; // 开始日期 date only

  // 候补配置
  final int minExpiredHours;
  final int maxExpiredHours;
  final int defaultExpiredHours;

  final Platform platform;
  final List<String> images;
  final String? holes;
  final String province;
  final String city;
  final String? distance;
  bool isFavorite;
  bool allowGroup;

  // Add other fields...

  Course({
    required this.id,
    required this.clubId,
    this.outerId,
    required this.name,
    required this.bindAccount,
    required this.hasToken,
    required this.prices,
    // required this.linkType,
    required this.startTime,
    required this.endTime,
    required this.bookStartDate,
    required this.bookEndDate,
    required this.notifyStartDate,
    required this.notifyEndDate,
    required this.minExpiredHours,
    required this.maxExpiredHours,
    required this.defaultExpiredHours,
    required this.platform,
    required this.images,
    required this.holes,
    required this.province,
    required this.city,
    required this.distance,
    this.isFavorite = false,
    this.allowGroup = false,
    // Add other fields...
  });

  setbind() {
    binded = hasToken;
    return this;
  }

  factory Course.fromJson(Map<String, dynamic> json) {
    return Course(
      id: json['id'],
      outerId: json['outer_id'],
      minExpiredHours: int.parse(json['expired_hours_min']),
      maxExpiredHours: int.parse(json['expired_hours_max']),
      defaultExpiredHours: int.parse(json['expired_hours_default']),
      clubId: json['club_id'],
      name: json['name'],
      // binded: json['binded'] || false,
      bindAccount: json['bind_account'],
      hasToken: json['has_token'],
      // json['prices'] is map, we need to convert it to List<Price>
      prices: json['prices'] is Map<String, dynamic>
          ? fromJson(json['prices'])
          : [],
      // string to int, then to LinkType
      // linkType: LinkTypeExtension.fromInt(
      //     int.parse(json['platform']['bind_category'])),

      // linkType: LinkType.code,
      // start_time like 18:20
      startTime: TimeOfDay(
        hour: int.parse(json['book_starttime'].split(':')[0]),
        minute: int.parse(json['book_starttime'].split(':')[1]),
      ),
      endTime: TimeOfDay(
        hour: int.parse(json['book_endtime'].split(':')[0]),
        minute: int.parse(json['book_endtime'].split(':')[1]),
      ),
      // startDate like 2024-07-06
      bookStartDate: DateTime.parse(json['book_startdate']),
      bookEndDate: DateTime.parse(json['book_enddate']),
      notifyStartDate: DateTime.parse(json['notify_startdate']),
      notifyEndDate: DateTime.parse(json['notify_enddate']),
      platform: json['platform'] != null
          ? Platform.fromJson(json['platform'])
          : Platform.fromJson(json['Platform']),
      images: json['images'] is Map<String, dynamic>
          ? (json['images'] as Map<String, dynamic>)
              .values
              .toList()
              .cast<String>()
          : List<String>.from(json['images']),
      holes: json['holes'],
      province: json['province'],
      city: json['city'],
      distance: json['distance'],
      allowGroup: json['allow_group'] == '1',
      // Add other fields...
    ).setbind();
  }
}

// get course list of club request and reponse
class ClubCoursesRequest {
  final String platformId;

  ClubCoursesRequest({required this.platformId});

  Map<String, String> toJson() {
    return {'platform_id': platformId};
  }
}

class ClubCoursesResponse extends BaseResponse {
  final List<SimpleCourse> items;
  final Paginator paginator;

  ClubCoursesResponse({
    required super.code,
    required super.msg,
    required this.items,
    required this.paginator,
  }) : super(data: {});

  factory ClubCoursesResponse.fromJson(Map<String, dynamic> json) {
    var itemsJson = json['data']['items'] as List;
    List<SimpleCourse> itemsList =
        itemsJson.map((i) => SimpleCourse.fromJson(i)).toList();

    return ClubCoursesResponse(
      code: json['code'],
      msg: json['msg'],
      items: itemsList,
      paginator: Paginator.fromJson(json['data']['paginator']),
    );
  }
}

// unbind club request and response
class UnbindClubRequest {
  final String clubId;

  UnbindClubRequest({required this.clubId});

  Map<String, String> toJson() {
    return {'id': clubId};
  }
}

class SearchCourseItem {
  final String id;
  final String name;
  final String clubName;
  final String? outerId;
  final List<Price> prices; // 价格列表
  // final LinkType linkType; // 绑定账号类型（可扩展)，密码还是邮箱验证码，1: 验证码，2: 密码
  // 可选时间范围

  final List<String> images;
  final String? holes;
  final String province;
  final String city;
  final String zip;
  final String? distance;
  bool isFavorite;
  Platform? platform;

  // Add other fields...

  SearchCourseItem({
    required this.id,
    required this.name,
    this.outerId,
    required this.prices,
    required this.images,
    required this.holes,
    required this.province,
    required this.city,
    required this.zip,
    required this.distance,
    this.isFavorite = false,
    required this.clubName,
    this.platform,
    // Add other fields...
  });

  factory SearchCourseItem.fromJson(Map<String, dynamic> json) {
    return SearchCourseItem(
      id: json['id'],
      outerId: json['outer_id'],
      name: json['name'],
      // binded: json['binded'] || false,
      // json['prices'] is map, we need to convert it to List<Price>
      prices: json['prices'] is Map<String, dynamic>
          ? fromJson(json['prices'])
          : [],
      // string to int, then to LinkType
      // linkType: LinkTypeExtension.fromInt(
      //     int.parse(json['platform']['bind_category'])),

      // linkType: LinkType.code,
      // start_time like 18:20
      images: json['images'] is Map<String, dynamic>
          ? (json['images'] as Map<String, dynamic>)
              .values
              .toList()
              .cast<String>()
          : List<String>.from(json['images']),
      holes: json['holes'],
      province: json['province'],
      city: json['city'],
      zip: json['zip'],
      distance: json['distance'],
      isFavorite: json['isfavorite'] == 1,
      clubName: json['club_name'],
      platform:
          json['platform'] != null ? Platform.fromJson(json['platform']) : null,
      // Add other fields...
    );
  }

  // 转成SelectedCourseItem
  SelectedCourseItem toSelectedCourseItem() {
    return SelectedCourseItem(
      id: id,
      name: name,
      outerId: outerId,
    );
  }
}

// 添加新的类
class SelectedCourseItem {
  final String id;
  final String name;
  final String? outerId;

  SelectedCourseItem({
    required this.id,
    required this.name,
    this.outerId,
  });
}
