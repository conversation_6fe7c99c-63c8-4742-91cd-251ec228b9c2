import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:io' show Platform;
import 'package:flutter/services.dart' show MethodChannel;
// 使用条件导入，这样在非web平台编译时不会包含dart:html
import 'web_url_service_stub.dart'
    if (dart.library.html) 'web_url_service.dart';

/// URL服务接口
abstract class URLService {
  /// 获取当前URL
  Future<String> getCurrentUrl();

  /// 获取URL参数
  Future<Map<String, String>> getQueryParameters();

  /// 替换URL
  Future<void> replaceUrl(String url);
}

/// 移动平台实现
class MobileURLService implements URLService {
  static const _channel = MethodChannel('app/url_service');

  @override
  Future<String> getCurrentUrl() async {
    if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) {
      try {
        final String? url = await _channel.invokeMethod('getCurrentUrl');
        return url ?? '';
      } catch (e) {
        print('Error getting current URL: $e');
      }
    }
    return '';
  }

  @override
  Future<Map<String, String>> getQueryParameters() async {
    if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) {
      try {
        final String? url = await _channel.invokeMethod('getCurrentUrl');
        if (url != null) {
          return Uri.parse(url).queryParameters;
        }
      } catch (e) {
        print('Error getting URL parameters: $e');
      }
    }
    return {};
  }

  @override
  Future<void> replaceUrl(String url) async {
    if (!kIsWeb && (Platform.isAndroid || Platform.isIOS)) {
      try {
        await _channel.invokeMethod('replaceUrl', {'url': url});
      } catch (e) {
        print('Error replacing URL: $e');
      }
    }
  }
}

/// 获取平台对应的URL服务实现
URLService getURLService() {
  if (kIsWeb) {
    return WebURLService();
  } else {
    return MobileURLService();
  }
}
