import 'package:flutter/material.dart';
import 'package:golf/services/platform/platform_service.dart';

class WebPlatformService implements PlatformService {
  @override
  String getCurrentVersion() {
    throw UnsupportedError('Web is not supported on this platform.');
  }

  @override
  void reloadPage() {
    throw UnsupportedError('Web is not supported on this platform.');
  }

  @override
  Future<void> handleVersionUpdate(
      BuildContext context, String newVersion) async {
    throw UnsupportedError('Web is not supported on this platform.');
  }
}
