import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:golf/services/platform/mobile_platform_service.dart';
import 'web_platform_service_stub.dart'
    if (dart.library.html) 'web_platform_service.dart';

/// 平台服务接口
abstract class PlatformService {
  /// 获取当前版本
  String getCurrentVersion();

  /// 刷新页面
  void reloadPage();

  /// 处理版本更新
  Future<void> handleVersionUpdate(BuildContext context, String newVersion);
}

/// 获取平台对应的服务实现
PlatformService getPlatformService() {
  if (kIsWeb) {
    return WebPlatformService();
  } else {
    return MobilePlatformService();
  }
}
