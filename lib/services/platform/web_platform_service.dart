import 'dart:js' as js;
import 'dart:html' as html;
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:golf/services/platform/platform_service.dart';

class WebPlatformService implements PlatformService {
  @override
  String getCurrentVersion() {
    try {
      return js.context['version'] as String? ?? '1.0.0';
    } catch (e) {
      return '1.0.0';
    }
  }

  @override
  void reloadPage() {
    html.window.location.reload();
  }

  @override
  Future<void> handleVersionUpdate(
      BuildContext context, String newVersion) async {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('New Version Available'),
        content:
            const Text('Please refresh the page to get the latest version.'),
        actions: [
          TextButton(
            child: const Text('Refresh Now'),
            onPressed: () {
              reloadPage();
            },
          ),
        ],
      ),
    );
  }
}
