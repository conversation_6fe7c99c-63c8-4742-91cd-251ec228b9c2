import 'dart:html' as html;
import 'package:golf/services/platform/url_service.dart';

class WebURLService implements URLService {
  @override
  Future<String> getCurrentUrl() async {
    return html.window.location.href;
  }

  @override
  Future<Map<String, String>> getQueryParameters() async {
    return Uri.parse(html.window.location.href).queryParameters;
  }

  @override
  Future<void> replaceUrl(String url) async {
    html.window.location.replace(url);
  }
}
