import 'package:golf/services/platform/url_service.dart';

class WebURLService implements URLService {
  @override
  Future<String> getCurrentUrl() async {
    throw UnsupportedError('Web is not supported on this platform.');
  }

  @override
  Future<Map<String, String>> getQueryParameters() async {
    throw UnsupportedError('Web is not supported on this platform.');
  }

  @override
  Future<void> replaceUrl(String url) async {
    throw UnsupportedError('Web is not supported on this platform.');
  }
}
