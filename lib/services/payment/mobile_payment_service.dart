import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:golf/services/payment/payment_interface.dart';
import 'package:golf/services/req/payment_req.dart';
import 'package:golf/utils/logger.dart';

class MobilePaymentService implements PaymentService {
  @override
  Future<void> initialize(String publicKey) async {
    Stripe.publishableKey = publicKey;
    await Stripe.instance.applySettings();
  }

  @override
  Future<bool> handlePayment({
    required String clientSecret,
    required String publicKey,
    required PaymentOrder order,
    required Function onSuccess,
    required Function onError,
  }) async {
    try {
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: clientSecret,
          merchantDisplayName: 'TeeTimeBot',
          style: ThemeMode.light,
          appearance: const PaymentSheetAppearance(
            colors: PaymentSheetAppearanceColors(
              primary: Colors.blue,
            ),
          ),
        ),
      );

      await Stripe.instance.presentPaymentSheet();
      onSuccess();
      return true;
    } catch (e) {
      AppLogger.error('Mobile payment error: $e', 'PAYMENT');
      onError(e);
      return false;
    }
  }

  @override
  Future<void> checkPaymentResult() async {
    // 移动端支付结果直接通过回调处理，无需额外检查
  }

  @override
  Future<void> dispose() async {
    // 清理移动端相关资源
  }
}
