import 'dart:async';
import 'dart:js' as js;
import 'dart:js_util' as js_util;
import 'package:golf/services/req/payment_req.dart';
import 'package:golf/services/payment/payment_interface.dart';
import 'package:golf/utils/logger.dart';
import 'dart:html' as html;

class WebPaymentService implements PaymentService {
  html.DivElement? _paymentContainer;
  html.FormElement? _form;
  final _completer = Completer<bool>();

  @override
  Future<void> initialize(String publicKey) async {
    // 检查Stripe是否已经加载
    if (js.context.hasProperty('Stripe')) {
      AppLogger.info('Stripe.js already loaded', 'PAYMENT');
      return;
    }

    // 添加Stripe.js脚本
    final completer = Completer<void>();
    final stripeScript = html.ScriptElement()
      ..src = 'https://js.stripe.com/v3/'
      ..type = 'text/javascript'
      ..async = true
      ..onLoad.listen((_) {
        AppLogger.info('Stripe.js loaded successfully', 'PAYMENT');
        completer.complete();
      })
      ..onError.listen((error) {
        AppLogger.error('Failed to load Stripe.js: $error', 'PAYMENT');
        completer.completeError(Exception('Failed to load Stripe.js'));
      });
    
    html.document.head?.append(stripeScript);
    
    // 等待脚本加载完成或超时
    await completer.future.timeout(
      const Duration(seconds: 5),
      onTimeout: () {
        throw Exception('Timeout loading Stripe.js');
      },
    );
  }

  @override
  Future<bool> handlePayment({
    required String clientSecret,
    required String publicKey,
    required PaymentOrder order,
    required Function onSuccess,
    required Function onError,
  }) async {
    try {
      // 确保Stripe已初始化
      if (!js.context.hasProperty('Stripe')) {
        throw Exception('Stripe.js not loaded. Please call initialize() first.');
      }

      // 创建支付容器和表单
      _createPaymentUI();

      final stripe = js.context.callMethod('Stripe', [publicKey]);
      AppLogger.info('Stripe.js initialized', 'PAYMENT');

      // 获取当前URL作为基础
      final baseUrl = Uri.base.origin;
      final returnPath =
          '/user/point_detail?order_id=${order.uuid}&payment_result=true&redirect_from=stripe';
      final returnUrl = baseUrl + returnPath;

      // 创建Elements实例
      final elements = stripe.callMethod('elements', [
        js.JsObject.jsify({
          'clientSecret': clientSecret,
          'appearance': {
            'theme': 'stripe',
          }
        })
      ]);

      // 创建支付元素
      final paymentElement = elements.callMethod('create', ['payment']);
      paymentElement.callMethod('mount', ['#payment-element']);

      // 处理支付确认
      _form?.onSubmit.listen((event) async {
        event.preventDefault();
        try {
          final jsPromise = stripe.callMethod('confirmPayment', [
            js.JsObject.jsify({
              'elements': elements,
              'confirmParams': {
                'return_url': returnUrl,
              }
            })
          ]);

          js_util.setProperty(jsPromise, 'then', js.allowInterop((result) {
            if (js_util.hasProperty(result, 'error')) {
              final error = js_util.getProperty(result, 'error');
              onError(error);
              _completer.complete(false);
            } else {
              onSuccess();
              _completer.complete(true);
            }
          }));
        } catch (e) {
          onError(e);
          _completer.complete(false);
        }
      });

      return await _completer.future;
    } catch (e) {
      onError(e);
      return false;
    }
  }

  void _createPaymentUI() {
    // 获取窗口尺寸
    final windowHeight = html.window.innerHeight ?? 600;
    final windowWidth = html.window.innerWidth ?? 400;

    // 计算容器尺寸
    final maxHeight = (windowHeight * 0.8).toInt();
    final containerWidth =
        windowWidth < 600 ? '${(windowWidth * 0.9).toInt()}px' : '500px';

    // 创建支付容器
    _paymentContainer = html.DivElement()
      ..id = 'payment-container'
      ..style.position = 'fixed'
      ..style.top = '50%'
      ..style.left = '50%'
      ..style.transform = 'translate(-50%, -50%)'
      ..style.zIndex = '9999'
      ..style.backgroundColor = 'white'
      ..style.padding = '20px'
      ..style.borderRadius = '8px'
      ..style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)'
      ..style.minWidth = '300px'
      ..style.maxWidth = containerWidth
      ..style.maxHeight = '${maxHeight}px'
      ..style.overflow = 'auto';

    // 创建标题容器
    final titleContainer = html.DivElement()
      ..style.display = 'flex'
      ..style.justifyContent = 'space-between'
      ..style.alignItems = 'center'
      ..style.marginBottom = '20px';

    // 创建标题
    final titleElement = html.HeadingElement.h3()
      ..text = 'Payment'
      ..style.margin = '0';

    // 创建关闭按钮
    final closeButton = html.ButtonElement()
      ..type = 'button'
      ..text = '×'
      ..style.background = 'none'
      ..style.border = 'none'
      ..style.fontSize = '24px'
      ..style.cursor = 'pointer'
      ..style.padding = '0 8px';

    // 添加关闭按钮点击事件
    closeButton.onClick.listen((event) {
      _dispose();
      _completer.complete(false);
    });

    titleContainer.children.addAll([titleElement, closeButton]);

    // 创建支付表单
    _form = html.FormElement()
      ..id = 'payment-form'
      ..style.width = '100%'
      ..style.margin = '0 auto';

    // 创建支付元素容器
    final paymentElementContainer = html.DivElement()
      ..id = 'payment-element'
      ..style.width = '100%'
      ..style.minHeight = '200px'
      ..style.marginBottom = '20px';

    // 创建按钮容器
    final buttonContainer = html.DivElement()
      ..style.display = 'flex'
      ..style.justifyContent = 'space-between'
      ..style.marginTop = '20px';

    // 创建按钮
    final cancelButton = html.ButtonElement()
      ..type = 'button'
      ..text = 'Cancel'
      ..style.padding = '12px'
      ..style.backgroundColor = '#f5f5f5'
      ..style.color = '#666'
      ..style.border = '1px solid #ddd'
      ..style.borderRadius = '4px'
      ..style.width = '30%'
      ..onClick.listen((event) {
        _dispose();
        _completer.complete(false);
      });

    final submitButton = html.ButtonElement()
      ..type = 'submit'
      ..text = 'Pay'
      ..style.width = '65%'
      ..style.padding = '12px'
      ..style.backgroundColor = '#0570de'
      ..style.color = 'white'
      ..style.border = 'none'
      ..style.borderRadius = '4px';

    buttonContainer.children.addAll([cancelButton, submitButton]);

    // 组装表单
    _form!.children.addAll([
      paymentElementContainer,
      buttonContainer,
    ]);

    // 组装容器
    _paymentContainer!.children.addAll([
      titleContainer,
      _form!,
    ]);

    // 添加到页面
    html.document.body?.children.add(_paymentContainer!);
  }

  void _dispose() {
    _paymentContainer?.remove();
    _paymentContainer = null;
    _form = null;
  }

  @override
  Future<void> checkPaymentResult() async {
    final uri = Uri.base;
    final params = uri.queryParameters;

    if (params.containsKey('payment_result')) {
      AppLogger.info(
          'Payment result detected: ${params['payment_result']}', 'PAYMENT');
      // 处理支付结果...
    }
  }

  @override
  Future<void> dispose() async {
    _dispose();
  }
}
