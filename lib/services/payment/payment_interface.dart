import 'package:golf/services/req/payment_req.dart';

/// 支付服务接口
abstract class PaymentService {
  /// 初始化支付服务
  Future<void> initialize(String publicKey);

  /// 处理支付
  Future<bool> handlePayment({
    required String clientSecret,
    required String publicKey,
    required PaymentOrder order,
    required Function onSuccess,
    required Function onError,
  });

  /// 检查支付结果
  Future<void> checkPaymentResult();

  /// 清理支付资源
  Future<void> dispose();
}
