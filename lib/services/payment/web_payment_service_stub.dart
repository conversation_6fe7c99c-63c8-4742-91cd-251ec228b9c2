import 'package:golf/services/payment/payment_interface.dart';
import 'package:golf/services/req/payment_req.dart';

/// Web支付服务的存根实现，用于非web平台
class WebPaymentService implements PaymentService {
  @override
  Future<void> initialize(String publicKey) async {
    throw UnsupportedError('Web payment is not supported on this platform.');
  }

  @override
  Future<bool> handlePayment({
    required String clientSecret,
    required String publicKey,
    required PaymentOrder order,
    required Function onSuccess,
    required Function onError,
  }) async {
    throw UnsupportedError('Web payment is not supported on this platform.');
  }

  @override
  Future<void> checkPaymentResult() async {
    throw UnsupportedError('Web payment is not supported on this platform.');
  }

  @override
  Future<void> dispose() async {
    // No-op
  }
}
