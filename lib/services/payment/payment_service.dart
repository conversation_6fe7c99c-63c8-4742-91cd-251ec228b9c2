export 'payment_interface.dart';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'payment_interface.dart';
import 'mobile_payment_service.dart';
import 'web_payment_service_stub.dart'
    if (dart.library.html) 'web_payment_service.dart';

/// 获取平台对应的支付服务实现
PaymentService getPaymentService() {
  if (kIsWeb) {
    return WebPaymentService();
  } else {
    return MobilePaymentService();
  }
}
