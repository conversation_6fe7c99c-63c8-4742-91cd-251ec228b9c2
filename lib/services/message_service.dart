import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import 'provider/base.dart';
import 'req/message_req.dart';

/// 消息服务
/// 管理未读消息数量和状态
class MessageService extends ChangeNotifier {
  static final MessageService _instance = MessageService._internal();
  factory MessageService() => _instance;
  MessageService._internal();

  static const String _unreadCountKey = 'unread_message_count';
  static const String _tag = 'MessageService';

  int _unreadCount = 0;
  bool _isInitialized = false;

  /// 获取未读消息数量
  int get unreadCount => _unreadCount;

  /// 是否有未读消息
  bool get hasUnreadMessages => _unreadCount > 0;

  /// 初始化服务
  Future<void> initialize([APIBase? api]) async {
    if (_isInitialized) return;

    try {
      await _loadUnreadCount();
      _isInitialized = true;
      AppLogger.info(
          'MessageService initialized with $_unreadCount unread messages',
          _tag);

      // 如果提供了API实例，从服务器同步未读数量
      if (api != null) {
        await syncUnreadCountFromServer(api);
      }
    } catch (e) {
      AppLogger.error('Failed to initialize MessageService: $e', _tag);
    }
  }

  /// 从本地存储加载未读消息数量
  Future<void> _loadUnreadCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _unreadCount = prefs.getInt(_unreadCountKey) ?? 0;
      notifyListeners();
    } catch (e) {
      AppLogger.error('Failed to load unread count: $e', _tag);
    }
  }

  /// 保存未读消息数量到本地存储
  Future<void> _saveUnreadCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_unreadCountKey, _unreadCount);
    } catch (e) {
      AppLogger.error('Failed to save unread count: $e', _tag);
    }
  }

  /// 设置未读消息数量
  Future<void> setUnreadCount(int count) async {
    if (_unreadCount != count) {
      _unreadCount = count;
      await _saveUnreadCount();
      notifyListeners();
      AppLogger.info('Unread count updated to $_unreadCount', _tag);
    }
  }

  /// 增加未读消息数量
  Future<void> incrementUnreadCount([int increment = 1]) async {
    await setUnreadCount(_unreadCount + increment);
  }

  /// 减少未读消息数量
  Future<void> decrementUnreadCount([int decrement = 1]) async {
    final newCount =
        (_unreadCount - decrement).clamp(0, double.infinity).toInt();
    await setUnreadCount(newCount);
  }

  /// 清除所有未读消息
  Future<void> clearUnreadCount() async {
    await setUnreadCount(0);
  }

  /// 标记消息为已读
  Future<void> markMessageAsRead(int messageId, APIBase api) async {
    try {
      MarkMessageReadRequest request = MarkMessageReadRequest(id: messageId);
      MarkMessageReadResponse response = await api.markMessageAsRead(request);
      AppLogger.info('Message marked as read successfully: ${response.msg}', _tag);
    } catch (e) {
      AppLogger.error('Failed to mark message as read: $e', _tag);
    }
  }

  /// 标记所有消息为已读
  Future<void> markAllMessagesAsRead(APIBase api) async {
    try {
      MarkAllMessagesReadRequest request = MarkAllMessagesReadRequest();
      MarkAllMessagesReadResponse response = await api.markAllMessagesAsRead(request);
      AppLogger.info('All messages marked as read successfully: ${response.msg}', _tag);
    } catch (e) {
      AppLogger.error('Failed to mark all messages as read: $e', _tag);
    }
  }

  /// 删除消息
  Future<void> deleteMessage(String messageId, [APIBase? api]) async {
    // TODO: 实现具体的消息删除逻辑
    // 这里可以调用API删除服务器端的消息
    await decrementUnreadCount();
    AppLogger.info('Message $messageId deleted', _tag);

    // 操作后同步服务器状态
    if (api != null) {
      await syncUnreadCountFromServer(api);
    }
  }

  /// 从服务器同步未读消息数量
  Future<void> syncUnreadCountFromServer(APIBase api) async {
    try {
      final request = MessageStatusRequest();
      final response = await api.getMessageStatus(request);
      await setUnreadCount(response.unread);
      AppLogger.info('Synced unread count from server: $_unreadCount', _tag);
    } catch (e) {
      AppLogger.error('Failed to sync unread count from server: $e', _tag);
      // 最后手段使用模拟数据
      await Future.delayed(const Duration(milliseconds: 500));
      await setUnreadCount(3);
      AppLogger.info('Used fallback mock unread count: $_unreadCount', _tag);
    }
  }

  /// 处理新消息通知
  Future<void> handleNewMessage(Map<String, dynamic> messageData) async {
    try {
      // 增加未读消息数量
      await incrementUnreadCount();

      // TODO: 可以在这里添加本地通知或其他处理逻辑
      AppLogger.info('New message received, unread count: $_unreadCount', _tag);
    } catch (e) {
      AppLogger.error('Failed to handle new message: $e', _tag);
    }
  }

  /// 重置服务状态（用于测试或登出时）
  Future<void> reset() async {
    _unreadCount = 0;
    _isInitialized = false;
    await _saveUnreadCount();
    notifyListeners();
    AppLogger.info('MessageService reset', _tag);
  }
}
