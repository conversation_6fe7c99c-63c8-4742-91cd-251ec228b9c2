import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/provider/api.dart';
import '../utils/logger.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

class FirebaseMessagingService {
  static final FirebaseMessaging _firebaseMessaging =
      FirebaseMessaging.instance;
  static const String _tokenKey = 'fcm_token';
  static const String _registeredKey = 'device_registered';
  static const String _tag = 'FCM';

  static Future<void> initialize() async {
    try {
      // 1. 请求推送权限
      NotificationSettings settings =
          await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        announcement: false,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
      );
      AppLogger.info(
          'Notification permission status: ${settings.authorizationStatus}',
          _tag);

      // 2. 只有授权后才注册回调和尝试获取token
      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        // 注册token刷新回调
        _firebaseMessaging.onTokenRefresh.listen((newToken) async {
          AppLogger.info('FCM Token refreshed (回调)', _tag);
          await _saveTokenLocally(newToken);
          await _registerDeviceWithServer(newToken);
        });
        // 初始化时尝试获取一次token（非阻塞）
        _firebaseMessaging.getToken().then((token) async {
          if (token != null) {
            AppLogger.info('FCM Token obtained (init)', _tag);
            await _saveTokenLocally(token);
            await _registerDeviceWithServer(token);
          }
        });
      } else {
        AppLogger.info('用户未授权推送权限，不获取token', _tag);
      }

      // 3. 注册消息监听
      RemoteMessage? initialMessage =
          await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        _handleMessage(initialMessage);
      }
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
      FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);
      FirebaseMessaging.onBackgroundMessage(
          _firebaseMessagingBackgroundHandler);
    } catch (e) {
      await AppLogger.errorWithFile(
          'Failed to initialize Firebase Messaging: $e', _tag);
    }
  }

  static Future<void> _saveTokenLocally(String token) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_tokenKey, token);
    } catch (e) {
      AppLogger.error('Failed to save token locally: $e', _tag);
    }
  }

  static Future<String?> getStoredToken() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      return prefs.getString(_tokenKey);
    } catch (e) {
      AppLogger.error('Failed to get stored token: $e', _tag);
      return null;
    }
  }

  static Future<void> _registerDeviceWithServer(String token) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      bool isRegistered = prefs.getBool(_registeredKey) ?? false;
      String? lastToken = prefs.getString(_tokenKey);

      // Only register if not registered before or token changed
      if (!isRegistered || lastToken != token) {
        String deviceType;
        if (kIsWeb) {
          deviceType = 'web';
        } else {
          deviceType = Platform.isIOS
              ? 'ios'
              : Platform.isAndroid
                  ? 'android'
                  : 'other';
        }

        AppLogger.info(
            'Device token ready for registration: type=$deviceType', _tag);

        // Mark as registered for now - actual registration happens in registerDeviceWithAPI
        await prefs.setBool(_registeredKey, true);
      }
    } catch (e) {
      AppLogger.error('Failed to register device with server: $e', _tag);
    }
  }

  static Future<bool> registerDeviceWithAPI(API api) async {
    try {
      String? token = await getStoredToken();
      if (token != null) {
        String deviceType;
        if (kIsWeb) {
          deviceType = 'other';
        } else {
          deviceType = Platform.isIOS
              ? 'ios'
              : Platform.isAndroid
                  ? 'android'
                  : 'other';
        }

        Map<String, dynamic> response = await api.postDeviceToken(
          devicetoken: token,
          devicetype: deviceType,
        );

        if (response['code'] == 0) {
          SharedPreferences prefs = await SharedPreferences.getInstance();
          await prefs.setBool(_registeredKey, true);
          AppLogger.info('Device registered successfully with server', _tag);
          return true;
        } else {
          AppLogger.error(
              'Failed to register device with server: $response', _tag);
          return false;
        }
      }
      return false;
    } catch (e) {
      AppLogger.error('Failed to register device with API: $e', _tag);
      return false;
    }
  }

  static void _handleForegroundMessage(RemoteMessage message) {
    AppLogger.info('Received foreground message: ${message.messageId}', _tag);
    _handleMessage(message);
  }

  static void _handleMessage(RemoteMessage message) {
    AppLogger.info('Handling message: ${message.messageId}', _tag);
    AppLogger.debug('Message data: ${message.data}', _tag);

    if (message.notification != null) {
      AppLogger.info(
          'Message notification: ${message.notification!.title} - ${message.notification!.body}',
          _tag);
    }

    // Handle navigation or custom actions based on message data
    _processMessageData(message.data);
  }

  static void _processMessageData(Map<String, dynamic> data) {
    // Process custom data from the notification
    if (data.containsKey('type')) {
      String type = data['type'];
      switch (type) {
        case 'booking':
          // Navigate to booking page
          AppLogger.info('Navigate to booking page', _tag);
          break;
        case 'promotion':
          // Navigate to promotion page
          AppLogger.info('Navigate to promotion page', _tag);
          break;
        case 'general':
        default:
          // Default action
          AppLogger.info('General notification received', _tag);
          break;
      }
    }
  }

  static Future<void> clearDeviceRegistration() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.remove(_registeredKey);
      await prefs.remove(_tokenKey);
      AppLogger.info('Device registration cleared', _tag);
    } catch (e) {
      AppLogger.error('Failed to clear device registration: $e', _tag);
    }
  }
}

// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  AppLogger.info('Handling background message: ${message.messageId}', 'FCM_BG');
}
