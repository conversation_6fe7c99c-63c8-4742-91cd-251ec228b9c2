import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:golf/services/firebase_messaging_service.dart';
import 'package:golf/services/provider/auth_notifier.dart';
import 'package:golf/services/req/auth_req.dart';
import 'package:golf/services/provider/auth_storage.dart';
import 'package:golf/services/req/booking_req.dart';
import 'package:golf/services/req/course_req.dart';
import 'package:golf/services/req/email_req.dart';
import 'package:golf/services/req/hot_deal_req.dart';
import 'package:golf/services/req/notify_req.dart';
import 'package:golf/services/req/payment_req.dart';
import 'package:golf/services/req/platform_req.dart';
import 'package:golf/services/req/point_req.dart';
import 'package:golf/services/req/coupon_req.dart';
import 'package:golf/services/req/message_req.dart';
import 'package:golf/services/req/user.dart';
import 'package:jwt_decode/jwt_decode.dart';

abstract class APIBase {
  Future<bool> isLoggedIn();
  void logout();
  // public, login
  Future<LoginResponse> login(LoginRequest request);
  // public, login
  Future<RegisterResponse> register(RegisterRequest request);
  // public, get course list
  Future<CourseResponse> getCourseList(ListCourseRequest request);
  Future<CourseResponse> favoriteCourse(ListCourseRequest request);
  Future<bool> updateFavorite(String courseId, bool favorite);

  // public, get course list
  Future<Course> getCourse(String id);

  Future<PlatformResponse> getPlatformList();
  // sumit reservation request
  Future<bool> submitReservation(BookingRequest request);
  // send email to verify that email is yours
  Future<SendEmailResponse> sendEmail(SendEmailRequest request);
  // verify email
  Future<VerifyEmailData> verifyEmail(VerifyEmailRequest request);
  // reset password with token
  Future<TokenData> resetPassword(ResetPasswordRequest request);

  Future<BookingListResponse> getPlans(BookingListRequest request);
  Future<BookingDetailResponse> getPlanDetail(BookingDetailRequest request);
  Future<bool> deletePlan(DeleteBookingRequest request);
  Future<bool> deleteNotification(DeleteNotificationRequest request);

  Future<bool> linkAccountByCode(LinkAccountByCodeRequest request);
  Future<bool> linkAccountByPassword(LinkAccountByPasswordRequest request);

  Future<List<LinkedAccount>> getLinkedAccounts();
  Future<bool> changeEmail(ChangeEmailRequest request);
  Future<bool> deleteAccount(DeleteAccountRequest request);
  Future<bool> changePassword(ChangePasswordRequest request);
  Future<bool> bindSendEmail(BindSendEmailRequest request);
  Future<ShareBookingDetail> getSharedBooking(
      ShareBookingDetailRequest request);
  Future<List<SimpleCourse>> getPlanCourses();
  Future<List<SimpleCourse>> getClubCourses(ClubCoursesRequest request);

  // 获取notification列表
  Future<NotificationListResponse> getNotifications(
      NotificationListRequest request);
  // 获取notification关联的course
  Future<List<SimpleCourse>> getNotificationCourse();
  // 获取用户详情
  Future<UserDetail> getUserDetail();
  // 获取积分历史
  Future<PointHistoryResponse> getPointHistory(PointHistoryRequest request);
  // 获取积分规则
  Future<PointRuleResponse> getPointRule();
  // 搜索
  Future<bool> checkEmail(CheckEmailRequest request);

  // 解绑俱乐部
  Future<bool> unbindClub(UnbindClubRequest request);
  Future<RedeemPointsResponse> redeemPoints(String code);

  // 获取版本号
  Future<String> getVersion();

  // 添加支付意图创建方法
  Future<PaymentIntentResponse> createPaymentIntent(
      double amount, String currency);

  Future<PaymentPackageListResponse> getPaymentPackages(
      PaymentPackageListRequest request);

  Future<CreatePaymentOrderResponse> createPaymentOrder(
      CreatePaymentOrderRequest request);

  /// 获取订单详情
  Future<PaymentOrderDetailResponse> getPaymentOrderDetail(
      PaymentOrderDetailRequest request);

  // 热门优惠分析
  Future<HotDealAnalysisResponse> hotDealAnalysis(
      HotDealAnalysisRequest request);

  // 优惠码相关接口
  Future<CouponStatusResponse> getCouponStatus();
  Future<AddCouponResponse> addCoupon(AddCouponRequest request);
  Future<CouponListResponse> getCouponList(CouponListRequest request);

  // 获取预订日志列表
  Future<PlanLogListResponse> getPlanLogList(PlanLogListRequest request);

  // 消息相关接口
  Future<MessageListResponse> getMessageList(MessageListRequest request);
  Future<MarkMessageReadResponse> markMessageAsRead(
      MarkMessageReadRequest request);
  Future<MarkAllMessagesReadResponse> markAllMessagesAsRead(
      MarkAllMessagesReadRequest request);
  Future<MessageStatusResponse> getMessageStatus(MessageStatusRequest request);
}

LoginUser userFromToken(String jwt) {
  final Map<String, dynamic> decodedToken = Jwt.parseJwt(jwt);
  return LoginUser(
    email: decodedToken['email'],
    useId: decodedToken['user_id'],
    expiredAt: DateTime.fromMillisecondsSinceEpoch(decodedToken['exp'] * 1000),
  );
}

class DefaultApi implements APIBase {
  final AuthStorage authStorage;
  final AuthNotifier authNotifier;

  DefaultApi(this.authNotifier, this.authStorage);

  @override
  Future<bool> isLoggedIn() async {
    var user = authNotifier.getUser();
    if (user != null && user.expiresIn > 0) {
      return true;
    }
    String? jwt = await authStorage.getJWT();
    if (jwt != null) {
      var user = userFromToken(jwt);
      if (user.expiresIn <= 0) {
        return false;
      }
      // Set user if not already set
      authNotifier.setLoggedIn(true, user: user);
    }

    return jwt != null;
  }

  @override
  void logout() {
    authStorage.removeJWT();
    authNotifier.setLoggedIn(false);
    // Clear device registration when user logs out
    if (!kIsWeb) {
      FirebaseMessagingService.clearDeviceRegistration();
    }
  }

  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}
