import 'package:flutter/material.dart';

enum PageState {
  home,
  homeNotify,
  plans,
  notifications,
  points,
  linkedAccount,
  settings,
  map,
  search,
  versionInfo,
  messageCenter,
}

class PageStateNotifier with ChangeNotifier {
  PageState _currentState;

  PageStateNotifier({PageState initialState = PageState.home})
      : _currentState = initialState;

  PageState get currentState => _currentState;

  void updateState(PageState newState) {
    _currentState = newState;
    notifyListeners();
  }
}
