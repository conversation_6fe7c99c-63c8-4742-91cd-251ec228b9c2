import 'package:golf/services/provider/auth_notifier.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthStorage {
  Future<String?> getJWT() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth');
  }

  Future<LoginUser> setJWT(String jwt) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('auth', jwt);
    return LoginUser.fromToken(jwt);
  }

  Future<void> removeJWT() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.remove('auth');
  }
}
