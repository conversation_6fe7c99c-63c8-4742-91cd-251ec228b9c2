import 'package:flutter/foundation.dart';
import 'package:jwt_decode/jwt_decode.dart';
import '../firebase_messaging_service.dart';
import '../../utils/logger.dart';
import 'api.dart';

class LoginUser {
  final String useId;
  final String email;
  final DateTime? expiredAt;
  int get expiresIn => expiredAt!.difference(DateTime.now()).inSeconds;

  LoginUser({required this.email, required this.useId, this.expiredAt});

  // factory LoginUser.fromJson(Map<String, dynamic> json) {
  factory LoginUser.fromToken(String jwt) {
    final Map<String, dynamic> decodedToken = Jwt.parseJwt(jwt);
    return LoginUser(
      email: decodedToken['email'],
      useId: decodedToken['user_id'],
      expiredAt:
          DateTime.fromMillisecondsSinceEpoch(decodedToken['exp'] * 1000),
    );
  }
}

class AuthNotifier extends ChangeNotifier {
  LoginUser? _user;
  API? _api;

  void setAPI(API api) {
    _api = api;
  }

  void setLoggedIn(bool ok, {LoginUser? user}) {
    if (ok) {
      var pre = _user;

      if (pre == null || pre.useId != user!.useId) {
        _user = user;
        // login - register device token if user is new or different
        _registerDeviceToken();
        // Web端登录后注册token
        // if (kIsWeb) {
        //   FirebaseMessagingService.registerWebTokenAfterLogin(_api!);
        // }
        notifyListeners();
      }

      // Save JWT to storage
    } else {
      if (_user != null) {
        // logout
        _user = null;
        notifyListeners();
      }
    }
  }

  void _registerDeviceToken() {
    if (_api != null && !kIsWeb) {
      // Register device token in background without blocking login
      FirebaseMessagingService.registerDeviceWithAPI(_api!).catchError((error) {
        AppLogger.error('Failed to register device token: $error', 'AUTH');
      });
    }
  }

  bool get isLoggedIn => _user != null;

  getUser() => _user;
}
