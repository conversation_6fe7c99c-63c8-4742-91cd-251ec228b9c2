// ignore_for_file: unnecessary_string_escapes

import 'dart:convert';

import 'package:golf/utils/logger.dart';
import 'package:golf/services/exceptions.dart';
import 'package:golf/services/provider/auth_notifier.dart';
import 'package:golf/services/req/auth_req.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/booking_req.dart';
import 'package:golf/services/req/course_req.dart';
import 'package:golf/services/req/email_req.dart';
import 'package:golf/services/req/hot_deal_req.dart';
import 'package:golf/services/req/notify_req.dart';
import 'package:golf/services/req/payment_req.dart';
import 'package:golf/services/req/point_req.dart';
import 'package:golf/services/req/coupon_req.dart';
import 'package:golf/services/req/message_req.dart';
import 'package:golf/services/req/req.dart';
import 'package:golf/services/req/search.dart';
import 'package:golf/services/req/user.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';
import 'package:flutter/services.dart' show rootBundle;

const String mockToken =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkxFRUxJVSJ9.********************************************************************************************************.7a8b1ef00ee78bfdd36a02fdf8e932a5';

class MockAPI extends Mock implements APIBase {
  final DefaultApi apiBase;

  MockAPI(this.apiBase);

  @override
  Future<bool> isLoggedIn() async {
    return apiBase.isLoggedIn();
  }

  @override
  void logout() {
    apiBase.logout();
  }

  @override
  Future<LoginResponse> login(LoginRequest request) async {
    await Future.delayed(const Duration(seconds: 2)); // Add this line
    var mockData = await loadMockData("fixtures/login_ok.json");

    final response = http.Response(mockData, 200);
    await checkResponse(response);
    final data = jsonDecode(response.body);
    final loginResponse = LoginResponse.fromJson(data);

    final token = loginResponse.data.token;
    await apiBase.authStorage.setJWT(token);
    apiBase.authNotifier.setLoggedIn(true, user: LoginUser.fromToken(token));
    return loginResponse;
  }

  @override
  Future<RegisterResponse> register(RegisterRequest request) async {
    await Future.delayed(const Duration(seconds: 2)); // Add this line
    apiBase.authStorage.setJWT(mockToken);
    apiBase.authNotifier
        .setLoggedIn(true, user: LoginUser.fromToken(mockToken));
    return RegisterResponse(
        code: 200, msg: 'success', data: TokenData(token: mockToken));
  }

  @override
  Future<bool> submitReservation(BookingRequest request) async {
    // I want a static list to reserve this request
    // Print the parameters
    await Future.delayed(const Duration(seconds: 2)); // Add this line
    return true;
  }

  @override
  Future<CourseResponse> getCourseList(ListCourseRequest request) async {
    // get http://api.golfteetime.vip/course/list
    AppLogger.info("call api getCourseList", "API");
    var mockData = await loadMockData("fixtures/course_list.json");

    return CourseResponse.fromJson(jsonDecode(mockData));
  }

  Future<http.Response> checkResponse(http.Response response) async {
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final baseResponse = BaseResponse.fromJson(data);
      final responseCode = ResponseCodeExtension.fromInt(baseResponse.code);
      if (responseCode != ResponseCode.success) {
        if (responseCode == ResponseCode.authExpired) {
          apiBase.logout();
        } else {
          throw ApiException(baseResponse.msg, responseCode.value);
        }
      }

      return response;
    } else {
      throw ApiException('unexptected statusCode: ${response.statusCode}',
          ResponseCode.network.value);
    }
  }

  @override
  Future<BookingListResponse> getPlans(BookingListRequest request) async {
    AppLogger.info('API: getPlans', 'API');
    await Future.delayed(const Duration(seconds: 1));
    final response = await rootBundle.loadString('fixtures/plans.json');
    return BookingListResponse.fromJson(jsonDecode(response));
  }

  @override
  Future<BookingDetailResponse> getPlanDetail(
      BookingDetailRequest request) async {
    AppLogger.info('API: getPlanDetail', 'API');
    await Future.delayed(const Duration(seconds: 1));
    final response = await rootBundle.loadString('fixtures/plan_detail.json');
    return BookingDetailResponse.fromJson(jsonDecode(response));
  }

  @override
  Future<SendEmailResponse> sendEmail(SendEmailRequest request) async {
    await Future.delayed(const Duration(seconds: 2)); // Add this line
    return SendEmailResponse(
        code: 200, msg: 'success', data: TokenData(token: mockToken));
  }

  @override
  Future<TokenData> resetPassword(ResetPasswordRequest request) async {
    await Future.delayed(const Duration(seconds: 2));
    apiBase.authStorage.setJWT(mockToken);
    apiBase.authNotifier
        .setLoggedIn(true, user: LoginUser.fromToken(mockToken));
    return TokenData(token: mockToken);
  }

  @override
  Future<VerifyEmailData> verifyEmail(VerifyEmailRequest request) async {
    await Future.delayed(const Duration(seconds: 2));
    return VerifyEmailData(token: mockToken);
  }

  @override
  Future<bool> linkAccountByCode(LinkAccountByCodeRequest request) async {
    // I want a static list to reserve this request
    // Print the parameters
    await Future.delayed(const Duration(seconds: 2)); // Add this line
    return true;
  }

  @override
  Future<bool> changeEmail(ChangeEmailRequest request) async {
    // I want a static list to reserve this request
    // Print the parameters
    await Future.delayed(const Duration(seconds: 2)); // Add this line
    return true;
  }

  @override
  Future<bool> deleteAccount(DeleteAccountRequest request) async {
    await Future.delayed(const Duration(seconds: 2)); // Add this line
    return true;
  }

  @override
  Future<bool> changePassword(ChangePasswordRequest request) async {
    await Future.delayed(const Duration(seconds: 2)); // Add this line
    return true;
  }

  @override
  Future<bool> deletePlan(DeleteBookingRequest request) async {
    AppLogger.info("call api deletePlan", "API");
    await Future.delayed(const Duration(seconds: 2));
    return true;
  }

  @override
  Future<List<SimpleCourse>> getPlanCourses() async {
    AppLogger.info("call api getPlanCourses", "API");
    await Future.delayed(const Duration(seconds: 1)); // Add this line
    // load mock data from file fixtures/plan_courses.json

    // var mockData = await rootBundle.loadString('fixtures/plan_courses.json');
    var mockData = await loadMockData("fixtures/plans_token_expired.json");

    final response = http.Response(mockData, 200);
    await checkResponse(response);
    List<SimpleCourse> items =
        PlanCourseListResponse.fromJson(jsonDecode(response.body)).items;
    items.insert(
        0,
        SimpleCourse(
            id: '0', name: 'All Courses', clubId: '0')); // Add 'All Courses'
    return items;
  }

  @override
  Future<List<SimpleCourse>> getNotificationCourse() async {
    await Future.delayed(const Duration(seconds: 1)); // Add this line
    // load mock data from file fixtures/plan_courses.json

    var filePath = 'fixtures/plans_token_expired.json';
    var mockData = await rootBundle.loadString(filePath);

    final response = http.Response(mockData, 200);
    await checkResponse(response);
    List<SimpleCourse> items =
        PlanCourseListResponse.fromJson(jsonDecode(response.body)).items;
    items.insert(
        0,
        SimpleCourse(
            id: '0', name: 'All Courses', clubId: '0')); // Add 'All Courses'
    return items;
  }

  @override
  Future<List<LinkedAccount>> getLinkedAccounts() async {
    var filePath = 'fixtures/linked_account_empty.json';
    var mockData = await rootBundle.loadString(filePath);

    final response = http.Response(mockData, 200);
    await checkResponse(response);
    List<LinkedAccount> items =
        LinkedAccountResponse.fromJson(jsonDecode(response.body)).items;
    return items;
  }

  @override
  Future<NotificationListResponse> getNotifications(
      NotificationListRequest request) async {
    // var filePath = 'fixtures/linked_account_empty.json';
    var filePath = 'fixtures/plans_token_expired.json';
    var mockData = await rootBundle.loadString(filePath);

    final response = http.Response(mockData, 200);
    await checkResponse(response);
    var items = NotificationListResponse.fromJson(jsonDecode(response.body));
    return items;
  }

  @override
  Future<UserDetail> getUserDetail() async {
    // var mockData = await rootBundle.loadString('fixtures/point_history.json');
    var mockData = await loadMockData("fixtures/plans_token_expired.json");

    final response = http.Response(mockData, 200);
    await checkResponse(response);
    var item = UserDetailResponse.fromJson(jsonDecode(response.body));
    return item.user;
  }

  @override
  Future<PointHistoryResponse> getPointHistory(
      PointHistoryRequest request) async {
    var filePath = 'fixtures/point_history.json';
    var mockData = await rootBundle.loadString(filePath);

    final response = http.Response(mockData, 200);
    await checkResponse(response);
    var item = PointHistoryResponse.fromJson(jsonDecode(response.body));
    return item;
  }

  @override
  Future<PointRuleResponse> getPointRule() async {
    var filePath = 'fixtures/point_rule.json';
    var mockData = await rootBundle.loadString(filePath);

    final response = http.Response(mockData, 200);
    await checkResponse(response);
    var item = PointRuleResponse.fromJson(jsonDecode(response.body));
    return item;
  }

  Future<SearchCourseResponse> searchCourse(SearchCourseRequest request) async {
    var filePath = 'fixtures/search.json';
    var mockData = await rootBundle.loadString(filePath);

    final response = http.Response(mockData, 200);
    await checkResponse(response);
    var item = SearchCourseResponse.fromJson(jsonDecode(response.body));
    return item;
  }

  @override
  Future<PaymentIntentResponse> createPaymentIntent(
      double amount, String currency) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(seconds: 1));

    // 返回模拟的支付意图数据
    return PaymentIntentResponse(
      code: 200,
      msg: 'success',
      clientSecret: 'pi_mock_secret_${DateTime.now().millisecondsSinceEpoch}',
    );
  }

  @override
  Future<PaymentPackageListResponse> getPaymentPackages(
      PaymentPackageListRequest request) async {
    await Future.delayed(const Duration(seconds: 1));

    var mockData = await loadMockData("fixtures/payment_packages.json");
    final response = http.Response(mockData, 200);
    await checkResponse(response);
    return PaymentPackageListResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<HotDealAnalysisResponse> hotDealAnalysis(
      HotDealAnalysisRequest request) async {
    AppLogger.info("call api hotDealAnalysis", "API");
    await Future.delayed(const Duration(seconds: 1));

    // 模拟不同的响应场景
    String mockResponse;

    // 根据球场ID模拟不同场景
    if (request.courseId == 'no_history') {
      // 没有历史数据的情况
      mockResponse = '''
      {
        "code": 0,
        "msg": "success",
        "data": {
          "recommend": "No booking history for this course. Try another course.",
          "analysis": {
            "data_status": "NO_HOT_DEAL_HISTORY",
            "adjustments": []
          }
        }
      }
      ''';
    } else if (request.players < 2) {
      // 玩家数量不足的情况
      mockResponse = '''
      {
        "code": 0,
        "msg": "success",
        "data": {
          "recommend": "Selected players (${request.players}) less than minimum required (2).",
          "analysis": {
            "data_status": "PLAYERS_INSUFFICIENT",
            "player_requirements": {
              "min_required_players": 2,
              "user_selected_players": ${request.players}
            },
            "adjustments": [
              {
                "type": "PLAYERS",
                "status": "INSUFFICIENT",
                "suggestion": "Increase to at least 2 players"
              }
            ]
          }
        }
      }
      ''';
    } else if (request.budget != null && request.budget! < 50.0) {
      // 预算不足的情况
      mockResponse = '''
      {
        "code": 0,
        "msg": "success",
        "data": {
          "recommend": "Your budget (\$${request.budget}) is below the historical minimum price. History: min \$50.00, max \$120.00, avg \$85.00.",
          "analysis": {
            "data_status": "BUDGET_INSUFFICIENT",
            "price_analysis": {
              "min_price": 50.0,
              "max_price": 120.0,
              "avg_price": 85.0,
              "user_budget": ${request.budget}
            },
            "adjustments": [
              {
                "type": "BUDGET",
                "status": "INSUFFICIENT",
                "suggestion": "Increase to at least \$50.00"
              }
            ]
          }
        }
      }
      ''';
    } else {
      // 正常情况，推荐用户预订
      mockResponse = '''
      {
        "code": 0,
        "msg": "success",
        "data": {
          "recommend": "Based on history, your selected dates and time range have a high chance of booking. Be ready to book as soon as available.",
          "analysis": {
            "data_status": "HAS_HOT_DEAL",
            "adjustments": []
          }
        }
      }
      ''';
    }

    final response = http.Response(mockResponse, 200);
    await checkResponse(response);
    return HotDealAnalysisResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<CouponStatusResponse> getCouponStatus() async {
    await Future.delayed(const Duration(seconds: 1));

    const mockResponse = '''
    {
      "code": 0,
      "msg": "success",
      "data": {
        "1": "Valid",
        "2": "Pending",
        "3": "Invalid"
      }
    }
    ''';

    final response = http.Response(mockResponse, 200);
    await checkResponse(response);
    return CouponStatusResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<AddCouponResponse> addCoupon(AddCouponRequest request) async {
    await Future.delayed(const Duration(seconds: 2));

    // 模拟不同的验证结果
    if (request.code.toLowerCase() == 'invalid') {
      throw ApiException('This promo code is invalid or expired',
          ResponseCode.invalidParam.value);
    }

    const mockResponse = '''
    {
      "code": 0,
      "msg": "success",
      "data": {
        "id": "12345"
      }
    }
    ''';

    final response = http.Response(mockResponse, 200);
    await checkResponse(response);
    return AddCouponResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<CouponListResponse> getCouponList(CouponListRequest request) async {
    await Future.delayed(const Duration(seconds: 1));

    const mockResponse = '''
    {
      "code": 0,
      "msg": "success",
      "data": {
        "items": [
          {
            "id": "1",
            "code": "GOLF20",
            "status": "1",
            "status_text": "Valid",
            "expiry_date": "2024-12-31 23:59:59",
            "insert_time": "2024-01-01 10:00:00",
            "update_time": "2024-01-01 10:00:00"
          },
          {
            "id": "2",
            "code": "SUMMER30",
            "status": "2",
            "status_text": "Pending",
            "expiry_date": "2024-08-31 23:59:59",
            "insert_time": "2024-01-15 15:30:00",
            "update_time": "2024-01-15 15:30:00"
          },
          {
            "id": "3",
            "code": "EXPIRED10",
            "status": "3",
            "status_text": "Invalid",
            "expiry_date": "2024-01-31 23:59:59",
            "insert_time": "2024-01-01 09:00:00",
            "update_time": "2024-02-01 00:00:00"
          }
        ],
        "paginator": {
          "prev": 0,
          "next": 0,
          "total": 3
        }
      }
    }
    ''';

    final response = http.Response(mockResponse, 200);
    await checkResponse(response);
    return CouponListResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<PlanLogListResponse> getPlanLogList(PlanLogListRequest request) async {
    AppLogger.info('API: getPlanLogList', 'API');
    await Future.delayed(const Duration(seconds: 1));
    return PlanLogListResponse(
      code: 200,
      msg: 'success',
      items: [
        PlanLogItem(
          id: '1',
          userId: '2',
          planId: request.planId,
          planDates: '2025-07-31',
          responseTeetime: null,
          responseHotdeal: null,
          responsePlayer: '[1,"used",2,3,4]',
          responsePrice: '',
          message: 'Oops! Not enough player slots for your chosen time',
          level: '2',
          insertTime: '2025-07-23 17:00:05',
          executeStartTime: '2025-07-23 14:10:04',
          executeEndTime: '2025-07-23 15:40:04',
        ),
        PlanLogItem(
          id: '2',
          userId: '2',
          planId: request.planId,
          planDates: '2025-07-31',
          responseTeetime: null,
          responseHotdeal: null,
          responsePlayer: '[1,2,3,4]',
          responsePrice: '52.00',
          message: 'Budget 45.00 lower than price 52.00',
          level: '2',
          insertTime: '2025-07-23 16:30:00',
          executeStartTime: '2025-07-23 16:00:00',
          executeEndTime: '2025-07-23 16:30:00',
        ),
      ],
      paginator: Paginator(prev: 0, next: 0, total: 2),
    );
  }

  // 消息相关接口的mock实现
  @override
  Future<MessageListResponse> getMessageList(MessageListRequest request) async {
    AppLogger.info('API: getMessageList', 'API');
    await Future.delayed(const Duration(milliseconds: 500));

    // 创建mock消息数据
    final mockMessages = [
      MessageItem(
        id: '1',
        userId: '1',
        title: 'Tee Time Reminder',
        body: 'Your tee time is at 10:30 AM',
        datatype: '0',
        data: '{"course_name": "Pebble Beach Golf Links"}',
        sendstatus: '1',
        readstatus: '0',
        status: '1',
        insertTime: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      MessageItem(
        id: '2',
        userId: '1',
        title: 'Booking Confirmed',
        body: 'Your booking has been confirmed for tomorrow',
        datatype: '0',
        data: '{"booking_id": "BK123456"}',
        sendstatus: '1',
        readstatus: '1',
        status: '1',
        insertTime: DateTime.now().subtract(const Duration(days: 1)),
      ),
      MessageItem(
        id: '3',
        userId: '1',
        title: 'Special Offer',
        body: 'Get 20% off your next booking this weekend',
        datatype: '0',
        data: '{"discount_code": "WEEKEND20"}',
        sendstatus: '1',
        readstatus: '0',
        status: '1',
        insertTime: DateTime.now().subtract(const Duration(days: 2)),
      ),
      MessageItem(
        id: '4',
        userId: '1',
        title: 'Weather Alert',
        body: 'Rain expected tomorrow. Consider rescheduling your tee time.',
        datatype: '0',
        data: '{"weather_condition": "rain"}',
        sendstatus: '1',
        readstatus: '0',
        status: '1',
        insertTime: DateTime.now().subtract(const Duration(hours: 6)),
      ),
      MessageItem(
        id: '5',
        userId: '1',
        title: 'Course Maintenance',
        body: 'Hole 7 will be under maintenance from 8 AM to 10 AM',
        datatype: '0',
        data: '{"hole_number": "7"}',
        sendstatus: '1',
        readstatus: '1',
        status: '1',
        insertTime: DateTime.now().subtract(const Duration(days: 3)),
      ),
      MessageItem(
        id: '6',
        userId: '1',
        title: 'Plan Execution Result',
        body: 'Your tee time booking plan has completed execution',
        datatype: '1',
        data: '{"plan_id": 1032}',
        sendstatus: '1',
        readstatus: '0',
        status: '1',
        insertTime: DateTime.now().subtract(const Duration(hours: 1)),
      ),
    ];

    // 根据请求参数过滤消息
    List<MessageItem> filteredMessages = mockMessages;

    if (request.readstatus != null) {
      filteredMessages = mockMessages.where((msg) => msg.readstatus == request.readstatus.toString()).toList();
    }

    // 计算未读消息数量
    final unreadCount = mockMessages.where((msg) => !msg.isRead).length;

    return MessageListResponse(
      code: 200,
      msg: 'success',
      items: filteredMessages,
      paginator: Paginator(prev: 0, next: 0, total: filteredMessages.length),
      unreadCount: unreadCount,
    );
  }


  @override
  Future<MarkMessageReadResponse> markMessageAsRead(
      MarkMessageReadRequest request) async {
    AppLogger.info(
        'API: markMessageAsRead for message ${request.id}', 'API');
    await Future.delayed(const Duration(milliseconds: 300));

    return MarkMessageReadResponse(
      code: 200,
      msg: 'Message marked as read successfully',
      data: {'message_id': request.id},
    );
  }

  @override
  Future<MarkAllMessagesReadResponse> markAllMessagesAsRead(
      MarkAllMessagesReadRequest request) async {
    AppLogger.info('API: markAllMessagesAsRead', 'API');
    await Future.delayed(const Duration(milliseconds: 500));

    return MarkAllMessagesReadResponse(
      code: 200,
      msg: 'All messages marked as read successfully',
      data: [],
    );
  }

  @override
  Future<MessageStatusResponse> getMessageStatus(MessageStatusRequest request) async {
    AppLogger.info('API: getMessageStatus', 'API');
    await Future.delayed(const Duration(milliseconds: 300));

    return MessageStatusResponse(
      code: 200,
      msg: 'success',
      unread: 3, // Mock unread count
      read: 10,
      unsend: 2,
      send: 8,
    );
  }

}

// a function to load mock data from file
Future<String> loadMockData(String filePath) async {
  return await rootBundle.loadString(filePath);
}

class MockApi {
  static Future<Map<String, dynamic>> loadJson(String path) async {
    final jsonStr = await rootBundle.loadString(path);
    // 强制异步操作，避免缓存
    await Future.delayed(const Duration(milliseconds: 1));
    return jsonDecode(jsonStr) as Map<String, dynamic>;
  }
}
