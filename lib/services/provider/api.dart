import 'dart:convert';
import 'dart:async'; // For TimeoutException
import 'dart:io'; // For SocketException
import 'package:golf/services/provider/auth_notifier.dart';
import 'package:golf/services/req/auth_req.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/booking_req.dart';
import 'package:golf/services/req/course_req.dart';
import 'package:golf/services/exceptions.dart';
import 'package:golf/services/req/email_req.dart';
import 'package:golf/services/req/hot_deal_req.dart';
import 'package:golf/services/req/notify_req.dart';
import 'package:golf/services/req/payment_req.dart';
import 'package:golf/services/req/platform_req.dart';
import 'package:golf/services/req/point_req.dart';
import 'package:golf/services/req/coupon_req.dart';
import 'package:golf/services/req/message_req.dart';
import 'package:golf/services/req/req.dart';
import 'package:golf/services/req/user.dart';
import 'package:http/http.dart' as http;
import 'package:retry/retry.dart'; // Import retry package
import 'package:flutter/services.dart' show rootBundle;
import 'package:golf/utils/logger.dart';

const bool envDebug = false;
var baseurl =
    envDebug ? 'https://api.golfteetime.vip' : 'https://api.teetimebot.vip';
var siteurl =
    envDebug ? 'https://www.golfteetime.vip' : 'https://www.teetimebot.vip';
// 热门优惠分析API的独立地址
const String hotDealAnalysisUrl = 'https://sp.teetimebot.vip';

class API extends APIBase {
  final DefaultApi apiBase;
  // Define retry options once
  final _retryOptions = const RetryOptions(
    maxAttempts: 3, // Max 3 attempts
    delayFactor:
        Duration(milliseconds: 200), // Delay increases by 200ms each time
    maxDelay: Duration(seconds: 2), // Max delay of 2 seconds
  );

  API(this.apiBase);

  @override
  Future<bool> isLoggedIn() async {
    return apiBase.isLoggedIn();
  }

  @override
  void logout() {
    apiBase.logout();
  }

  Future<String> getToken(bool authRequired) async {
    String? jwt = await apiBase.authStorage.getJWT();
    if (jwt == null) {
      apiBase.authNotifier.setLoggedIn(false);

      if (authRequired) {
        throw ApiException(
            'session expired, please login.', ResponseCode.authExpired.value);
      }
      return "";
    }
    var user = apiBase.authNotifier.getUser();
    if (user != null) {
      if (user!.expiredAt == null || user.expiredAt!.isBefore(DateTime.now())) {
        apiBase.authNotifier.setLoggedIn(false);
      }
      // 还有一天过期
      if (user.expiredAt!
          // .add(const Duration(minutes: -2))
          .add(const Duration(days: -1))
          .isBefore(DateTime.now())) {
        return await refreshToken(jwt);
      }
    }
    return jwt;
  }

  bool isRefreshing = false;
  Future<String> refreshToken(String jwt) async {
    if (isRefreshing) {
      return jwt;
    }
    isRefreshing = true;
    var url = 'user/prelong';
    try {
      final response = await _retryOptions.retry(
        () => http.get(
          Uri.parse('$baseurl/$url'),
          headers: {'Authorization': jwt},
        ).timeout(const Duration(seconds: 10)), // Add timeout
        retryIf: (e) => e is SocketException || e is TimeoutException,
      );
      final data = jsonDecode(response.body);
      final loginResponse = LoginResponse.fromJson(data);
      final token = loginResponse.data.token;
      await apiBase.authStorage.setJWT(token);
      apiBase.authNotifier.setLoggedIn(true, user: LoginUser.fromToken(token));
      return token;
    } finally {
      isRefreshing = false;
    }
  }

  Future<http.Response> get(String url, bool authRequired,
      {Map<String, String>? params}) async {
    var jwt = await getToken(authRequired); // Get token first
    Uri uri = Uri.parse('$baseurl/$url');
    if (params != null && params.isNotEmpty) {
      uri = uri.replace(queryParameters: params);
    }

    // Use retry for the http.get call
    return _retryOptions.retry(
      () => http.get(
        uri,
        headers: {'Authorization': jwt},
      ).timeout(const Duration(seconds: 15)), // Add timeout
      // Retry only on network-related issues like DNS lookup or timeout
      retryIf: (e) => e is SocketException || e is TimeoutException,
    );
  }

  Future<http.Response> post(String url, bool authRequired,
      {Map<String, String>? headers, Object? body}) async {
    var jwt = await getToken(authRequired); // Get token first

    // Use retry for the http.post call
    return _retryOptions.retry(
      () => http
          .post(
            Uri.parse('$baseurl/$url'),
            headers: {
              'Authorization': jwt,
              'Content-Type': 'application/json',
              ...?headers, // Merge custom headers
            },
            body: jsonEncode(body), // Encode body here
          )
          .timeout(const Duration(seconds: 15)), // Add timeout
      // Retry only on network-related issues like DNS lookup or timeout
      retryIf: (e) => e is SocketException || e is TimeoutException,
    );
  }

  // 专门用于热门优惠分析API的POST请求方法
  Future<http.Response> postToHotDealAnalysis(String url, bool authRequired,
      {Map<String, String>? headers, Object? body}) async {
    var jwt = await getToken(authRequired); // Get token first

    // Use retry for the http.post call
    return _retryOptions.retry(
      () => http
          .post(
            Uri.parse('$hotDealAnalysisUrl/$url'),
            headers: {
              if (authRequired) 'Authorization': jwt,
              'Content-Type': 'application/json',
              ...?headers, // Merge custom headers
            },
            body: jsonEncode(body), // Encode body here
          )
          .timeout(const Duration(seconds: 15)), // Add timeout
      // Retry only on network-related issues like DNS lookup or timeout
      retryIf: (e) => e is SocketException || e is TimeoutException,
    );
  }

  Future<http.Response> checkResponse(http.Response response) async {
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      final baseResponse = BaseResponse.fromJson(data);
      final responseCode = ResponseCodeExtension.fromInt(baseResponse.code);
      if (responseCode == ResponseCode.authExpired) {
        apiBase.logout();
      }
      if (responseCode != ResponseCode.success) {
        throw ApiException(baseResponse.msg, responseCode.value);
      }

      return response;
    } else {
      throw ApiException('unexptected statusCode: ${response.statusCode}',
          ResponseCode.network.value);
    }
  }

  @override
  Future<LoginResponse> login(LoginRequest request) async {
    // validate email
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(request.email)) {
      throw ApiException('Invalid email');
    }
    // this is a cross domain request
    final response = await http.post(
      Uri.parse('$baseurl/user/login'),
      body: jsonEncode(request.toJson()),
      headers: {
        'Content-Type': 'application/json',
      },
    );
    await checkResponse(response);

    final data = jsonDecode(response.body);
    final loginResponse = LoginResponse.fromJson(data);

    final token = loginResponse.data.token;
    await apiBase.authStorage.setJWT(token);
    apiBase.authNotifier.setLoggedIn(true, user: LoginUser.fromToken(token));
    return loginResponse;
  }

  @override
  Future<BookingListResponse> getPlans(BookingListRequest request) async {
    final response = await get('plan/list', true, params: request.toJson());
    await checkResponse(response);
    return BookingListResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<bool> submitReservation(BookingRequest request) async {
    // post http://api.golfteetime.vip/plan/post
    var path = "plan/post";
    if (request.type == BookingType.notification) {
      path = "notify/post";
    }
    return post(
      path,
      true,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      return true;
    });
  }

  @override
  Future<CourseResponse> getCourseList(ListCourseRequest request) async {
    final response = await post('course/list', false, body: request.toJson());
    await checkResponse(response);
    return CourseResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<RegisterResponse> register(RegisterRequest request) async {
    return post(
      'user/register',
      false,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      final data = jsonDecode(response.body);
      final apiResponse = RegisterResponse.fromJson(data);

      final token = apiResponse.data.token;
      await apiBase.authStorage.setJWT(token);
      apiBase.authNotifier.setLoggedIn(true, user: LoginUser.fromToken(token));
      return apiResponse;
    });
  }

  @override
  Future<SendEmailResponse> sendEmail(SendEmailRequest request) {
    String path = "";
    bool authRequired = false;
    switch (request.type) {
      case EmailType.register:
        path = 'user/registersendmail';
        break;
      case EmailType.reset:
        path = 'user/resetpasswordsendmail';
        break;

      case EmailType.changeEmail:
        path = 'user/updateemailsendmail';
        authRequired = true;
        break;
      case EmailType.deleteAccount:
        path = 'user/deletesendmail';
        authRequired = true;
        break;
    }
    return post(
      path,
      authRequired,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      final data = jsonDecode(response.body);
      return SendEmailResponse.fromJson(data);
    });
  }

  @override
  Future<TokenData> resetPassword(ResetPasswordRequest request) {
    return post(
      'user/resetpassword',
      false,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      final data = jsonDecode(response.body);
      ResetPasswordResponse res = ResetPasswordResponse.fromJson(data);
      final token = res.data.token;
      await apiBase.authStorage.setJWT(token);
      apiBase.authNotifier.setLoggedIn(true, user: LoginUser.fromToken(token));
      return res.data;
    });
  }

  @override
  Future<VerifyEmailData> verifyEmail(VerifyEmailRequest request) {
    // TODO: implement verifyEmail
    throw UnimplementedError();
  }

  @override
  Future<bool> linkAccountByCode(LinkAccountByCodeRequest request) {
    return post(
      'platform/checkcode',
      true,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      return true;
    });
  }

  @override
  Future<bool> linkAccountByPassword(LinkAccountByPasswordRequest request) {
    return post(
      'platform/bindByPassword',
      true,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      return true;
    });
  }

  @override
  Future<bool> changeEmail(ChangeEmailRequest request) {
    return post(
      'user/updateemail',
      true,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      return true;
    });
  }

  @override
  Future<bool> deleteAccount(DeleteAccountRequest request) {
    return post(
      'user/delete',
      true,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      logout();
      return true;
    });
  }

  @override
  Future<bool> changePassword(ChangePasswordRequest request) {
    return post(
      'user/updatepassword',
      true,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      return true;
    });
  }

  @override
  Future<bool> bindSendEmail(BindSendEmailRequest request) {
    return post(
      'platform/sendcode',
      true,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      return true;
    });
  }

  @override
  Future<bool> deletePlan(DeleteBookingRequest request) {
    return post(
      'plan/delete',
      true,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      return true;
    });
  }

  @override
  Future<ShareBookingDetail> getSharedBooking(
      ShareBookingDetailRequest request) async {
    final response = await get('plan/share', false, params: request.toJson());
    await checkResponse(response);
    return ShareBookingDetailResponse.fromJson(jsonDecode(response.body)).item;
  }

  @override
  Future<List<SimpleCourse>> getPlanCourses() async {
    final response = await get('plan/listcourse', true);
    await checkResponse(response);
    List<SimpleCourse> items =
        PlanCourseListResponse.fromJson(jsonDecode(response.body)).items;
    items.insert(
        0,
        SimpleCourse(
            id: '0', name: 'All Courses', clubId: '0')); // Add 'All Courses'
    return items;
  }

  @override
  Future<List<LinkedAccount>> getLinkedAccounts() {
    return get('platform/listaccount', true).then((response) async {
      await checkResponse(response);
      List<LinkedAccount> items =
          LinkedAccountResponse.fromJson(jsonDecode(response.body)).items;
      return items;
    });
  }

  @override
  Future<List<SimpleCourse>> getClubCourses(ClubCoursesRequest request) {
    return get('platform/listcourse', false, params: request.toJson())
        .then((response) async {
      await checkResponse(response);
      List<SimpleCourse> items =
          ClubCoursesResponse.fromJson(jsonDecode(response.body)).items;
      return items;
    });
  }

  @override
  Future<PointHistoryResponse> getPointHistory(PointHistoryRequest request) {
    return get('user/pointslist', true, params: request.toJson())
        .then((response) async {
      await checkResponse(response);
      var item = PointHistoryResponse.fromJson(jsonDecode(response.body));
      return item;
    });
  }

  @override
  Future<PointRuleResponse> getPointRule() {
    return get(
      'user/pointsrules',
      true,
    ).then((response) async {
      await checkResponse(response);
      var item = PointRuleResponse.fromJson(jsonDecode(response.body));
      return item;
    });
  }

  @override
  Future<NotificationListResponse> getNotifications(
      NotificationListRequest request) async {
    final response = await get('notify/list', true, params: request.toJson());
    await checkResponse(response);
    return NotificationListResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<List<SimpleCourse>> getNotificationCourse() async {
    final response = await get('notify/listcourse', true);
    await checkResponse(response);
    List<SimpleCourse> items =
        PlanCourseListResponse.fromJson(jsonDecode(response.body)).items;
    items.insert(
        0,
        SimpleCourse(
            id: '0', name: 'All Courses', clubId: '0')); // Add 'All Courses'
    return items;
  }

  @override
  Future<bool> deleteNotification(DeleteNotificationRequest request) {
    return post(
      'notify/delete',
      true,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      return true;
    });
  }

  @override
  Future<UserDetail> getUserDetail() {
    return get(
      'user/get',
      true,
    ).then((response) async {
      await checkResponse(response);
      var result = UserDetailResponse.fromJson(jsonDecode(response.body)).user;
      return result;
    });
  }

  @override
  Future<bool> checkEmail(CheckEmailRequest request) {
    return post(
      'user/checkEmail',
      false,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      var item = CheckEmailResponse.fromJson(jsonDecode(response.body));
      return item.exist;
    });
  }

  @override
  Future<bool> unbindClub(UnbindClubRequest request) {
    return post(
      'account/delete',
      false,
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      return true;
    });
  }

  @override
  Future<PlatformResponse> getPlatformList() async {
    final response = await get('platform/list', false);
    await checkResponse(response);
    return PlatformResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<Course> getCourse(String id) async {
    final response = await get('course/get?id=$id', false);
    await checkResponse(response);
    return CourseDetailResponse.fromJson(jsonDecode(response.body)).course;
  }

  @override
  Future<CourseResponse> favoriteCourse(ListCourseRequest request) async {
    final response = await post('favorite/list', false, body: request.toJson());
    await checkResponse(response);
    return FavoriteListResponse.fromJson(jsonDecode(response.body))
        .toCourseResponse();
  }

  @override
  Future<bool> updateFavorite(String courseId, bool favorite) async {
    http.Response response;
    if (favorite) {
      response = await post('favorite/post', true, body: {
        'course_id': courseId,
      });
    } else {
      response = await post('favorite/deleteby', true, body: {
        'course_id': courseId,
      });
    }
    await checkResponse(response);
    return true;
  }

  @override
  Future<RedeemPointsResponse> redeemPoints(String code) {
    return post(
      'pointscard/use',
      true,
      body: RedeemPointsRequest(code: code).toJson(),
    ).then((response) async {
      await checkResponse(response);
      return RedeemPointsResponse.fromJson(jsonDecode(response.body));
    });
  }

  @override
  Future<String> getVersion() async {
    final response = http.Response(
      await rootBundle.loadString('fixtures/version.json'),
      200,
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Date': DateTime.now().toUtc().toString(),
      },
    );
    await checkResponse(response);
    final data = jsonDecode(response.body);
    return data['data']['version'] as String;
  }

  // 新增设备注册相关方法
  Future<Map<String, dynamic>> postDeviceToken({
    required String devicetoken,
    required String devicetype,
  }) async {
    try {
      final response = await post(
        'device/post',
        true,
        body: {
          'devicetoken': devicetoken,
          'devicetype': devicetype,
        },
      );
      await checkResponse(response);
      return jsonDecode(response.body);
    } catch (e) {
      throw Exception('Failed to register device: $e');
    }
  }

  @override
  Future<PaymentIntentResponse> createPaymentIntent(
      double amount, String currency) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(seconds: 1));

    // 返回模拟的支付意图数据
    return PaymentIntentResponse(
      code: 200,
      msg: 'success',
      clientSecret: 'pi_mock_secret_${DateTime.now().millisecondsSinceEpoch}',
    );
  }

  @override
  Future<PaymentPackageListResponse> getPaymentPackages(
      PaymentPackageListRequest request) async {
    final response = await post(
      'paymentpackage/list',
      true,
      body: request.toJson(),
    );

    await checkResponse(response);
    return PaymentPackageListResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<CreatePaymentOrderResponse> createPaymentOrder(
      CreatePaymentOrderRequest request) async {
    final response = await post(
      'paymentorder/post',
      true,
      body: request.toJson(),
    );

    await checkResponse(response);
    return CreatePaymentOrderResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<PaymentOrderDetailResponse> getPaymentOrderDetail(
      PaymentOrderDetailRequest request) async {
    try {
      AppLogger.info('获取订单详情: ${request.orderId}', 'API');
      final response = await post(
        'paymentorder/get',
        true,
        body: request.toJson(),
      );

      await checkResponse(response);

      final jsonData = jsonDecode(response.body);
      AppLogger.info('订单详情API响应: $jsonData', 'API');

      return PaymentOrderDetailResponse.fromJson(jsonData);
    } catch (e) {
      AppLogger.error('获取订单详情失败: $e', 'API');
      rethrow;
    }
  }

  @override
  Future<BookingDetailResponse> getPlanDetail(
      BookingDetailRequest request) async {
    final response = await get('plan/get', true, params: request.toJson());
    await checkResponse(response);
    return BookingDetailResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<HotDealAnalysisResponse> hotDealAnalysis(
      HotDealAnalysisRequest request) {
    return postToHotDealAnalysis(
      'api/hot-deal-analysis',
      false, // 不需要认证，因为是分析功能
      body: request.toJson(),
    ).then((response) async {
      await checkResponse(response);
      return HotDealAnalysisResponse.fromJson(jsonDecode(response.body));
    });
  }

  @override
  Future<CouponStatusResponse> getCouponStatus() async {
    final response = await get('coupons/liststatus', false);
    await checkResponse(response);
    return CouponStatusResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<AddCouponResponse> addCoupon(AddCouponRequest request) async {
    final response = await post(
      'coupons/post',
      true,
      body: request.toJson(),
    );
    await checkResponse(response);
    return AddCouponResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<CouponListResponse> getCouponList(CouponListRequest request) async {
    final response = await get('coupons/list', true, params: request.toJson());
    await checkResponse(response);
    return CouponListResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<PlanLogListResponse> getPlanLogList(PlanLogListRequest request) async {
    final response = await get('planlog/list', true, params: request.toJson());
    await checkResponse(response);
    return PlanLogListResponse.fromJson(jsonDecode(response.body));
  }

  // 消息相关接口实现
  @override
  Future<MessageListResponse> getMessageList(MessageListRequest request) async {
    final params = request.toJson().map((key, value) => MapEntry(key, value.toString()));
    final response = await get('message/list', true, params: params);
    await checkResponse(response);
    return MessageListResponse.fromJson(jsonDecode(response.body));
  }


  @override
  Future<MarkMessageReadResponse> markMessageAsRead(
      MarkMessageReadRequest request) async {
    final response =
        await post('message/read', true, body: request.toJson());
    await checkResponse(response);
    return MarkMessageReadResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<MarkAllMessagesReadResponse> markAllMessagesAsRead(
      MarkAllMessagesReadRequest request) async {
    final response =
        await post('message/readall', true, body: request.toJson());
    await checkResponse(response);
    return MarkAllMessagesReadResponse.fromJson(jsonDecode(response.body));
  }

  @override
  Future<MessageStatusResponse> getMessageStatus(MessageStatusRequest request) async {
    final response = await post('message/count', true, body: request.toJson());
    await checkResponse(response);
    return MessageStatusResponse.fromJson(jsonDecode(response.body));
  }
}

// 修改 loadMockData 方法，添加错误处理
Future<String> loadMockData(String filePath) async {
  try {
    final data = await rootBundle.loadString(filePath);
    AppLogger.debug("Loaded mock data: $data", 'API');
    return data;
  } catch (e) {
    AppLogger.error("Error loading mock data: $e", 'API');
    // 返回一个有效的 JSON 字符串作为后备
    return '{"code":0,"msg":"ok","data":{"version":"0.0.0"}}';
  }
}
