// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'dart:async';
import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:golf/auth_guard_observer.dart';
import 'package:golf/config.dart';
import 'package:golf/utils/logger.dart';

import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/booking_req.dart';
import 'package:golf/services/req/req.dart' as req;
import 'package:golf/services/req/course_req.dart';
import 'package:golf/services/req/hot_deal_req.dart';
import 'package:golf/utils/exception.dart';
import 'package:golf/widgets/golf_dialog.dart';
import 'package:golf/widgets/golf_toast.dart';
import 'package:intl/intl.dart';
// KeyboardActions 已移至 home.dart

import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:time_range_picker/time_range_picker.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';
import 'package:visibility_detector/visibility_detector.dart';

final today = DateUtils.dateOnly(DateTime.now());
typedef NotifyParentCallback = void Function(BookingType type);
const secondaryColor = Color(0xFF5FCE86);

class HomeFormScreen extends StatefulWidget {
  final bool isLoggedIn;
  final NotifyParentCallback notifyParent;
  final bool isBooking;
  final FocusNode? customPlayersFocusNode; // 添加 FocusNode 参数
  final FocusNode? budgetFocusNode; // 添加 FocusNode 参数

  const HomeFormScreen(
      {super.key,
      required this.isLoggedIn,
      required this.notifyParent,
      required this.isBooking,
      this.customPlayersFocusNode,
      this.budgetFocusNode});

  @override
  _HomeFormScreenState createState() => _HomeFormScreenState();
}

const String botTip1 = "Tee time Booking Bot";
const String botTip2 = "TeeTimeBot will automatically book tee time for you";
const String waitTip1 = "Waitlist Notification";
const String waitTip2 =
    "TeeTimeBot will send you an email notification once your tee time is available.";

class _HomeFormScreenState extends State<HomeFormScreen>
    with SingleTickerProviderStateMixin {
  SelectedCourseItem? selectedCourse;
  Course? currentCourse;
  List<Price> prices = [];
  Price? selectedPrice;
  TimeOfDay startTime = const TimeOfDay(hour: 7, minute: 00);
  TimeOfDay endTime = const TimeOfDay(hour: 18, minute: 20);

  int hours = 24;

  DateTime startDate = today.add(const Duration(days: 1));
  DateTime endDate = today.add(const Duration(days: 61));
  TimeRange selectedTime = TimeRange(
      startTime: const TimeOfDay(hour: 11, minute: 30),
      endTime: const TimeOfDay(hour: 12, minute: 30));
  String tip1 = botTip1;
  String tip2 = botTip2;
  int players = 4;
  List<DateTime?> _multiDatePickerValueWithDefaultValue = [];
  late TabController _tabController;

  late bool isBooking;
  bool _isChecked = false;
  late CalendarDatePicker2Config config;
  double budget = 0.0;
  final TextEditingController _budgetController = TextEditingController();
  bool enableBudget = false;
  bool hotDealsOnly = true;
  int? customPlayers; // 用于存储自定义玩家数量

  int? cachedHours; // 用于缓存小时数
  bool cancelIfNotEnough = false; // 添加新的状态变量

  final TextEditingController _customPlayersController =
      TextEditingController(); // 添加控制器

  // Focus nodes for keyboard actions
  late final FocusNode _customPlayersFocusNode;
  late final FocusNode _budgetFocusNode;

  // 热门优惠分析相关状态
  // 这些字段在分析过程中被赋值，虽然目前没有在UI中读取，但保留用于未来功能
  HotDealAnalysisData? _hotDealAnalysis;
  bool _isAnalyzing = false;

  // 防抖定时器
  Timer? _debounceTimer;

  // 1. 在 State 类中添加 GlobalKey 和高亮状态
  final GlobalKey _playersTipKey = GlobalKey();
  final GlobalKey _budgetTipKey = GlobalKey();
  final GlobalKey _datesTipKey = GlobalKey();
  final GlobalKey _timeTipKey = GlobalKey();
  final GlobalKey _courseTipKey = GlobalKey();
  bool _highlightPlayers = false;
  bool _highlightBudget = false;
  bool _highlightDates = false;
  bool _highlightTime = false;
  bool _highlightCourse = false;

  // 2. 在 build 方法中获取 adjustment 信息
  String? adjustmentType;
  String? adjustmentSuggestion;
  String? adjustmentRecommend;

  bool _isAnalysisTipVisible = true;

  OverlayEntry? _topSuggestionOverlayEntry;
  bool _lastShowTopSuggestionBar = false;

  late BuildContext _rootContext;
  bool inHomeUI = true;

  @override
  void initState() {
    super.initState();
    // 延迟一帧，确保 context 可用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _rootContext = context;
    });
    // 初始化 FocusNode，使用传入的或创建新的
    _customPlayersFocusNode = widget.customPlayersFocusNode ?? FocusNode();
    _budgetFocusNode = widget.budgetFocusNode ?? FocusNode();
    _budgetFocusNode.addListener(() {
      if (!_budgetFocusNode.hasFocus) {
        if (hotDealsOnly && enableBudget) {
          _debouncedPerformHotDealAnalysis();
        }
      }
    });

    _loadCachedHours(); // 加载缓存的小时数
    AppLogger.info(
        "HomeFormScreen(${widget.key}): initState, isBooking: ${widget.isBooking}",
        'UI');
    // _courseListFuture =
    // Provider.of<APIBase>(context, listen: false).getCourseList();
    isBooking = widget.isBooking;
    AppLogger.info("isBooking: $isBooking", 'UI');
    config = CalendarDatePicker2Config(
      calendarType: CalendarDatePicker2Type.multi,
      selectedDayHighlightColor: AppColors.primaryColor, // 使用主色调
      selectedDayTextStyle:
          const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
      todayTextStyle: const TextStyle(
          color: AppColors.primaryColor, fontWeight: FontWeight.bold),
      dayTextStyle: const TextStyle(color: Colors.black87),
      disabledDayTextStyle: const TextStyle(color: Colors.grey),
      weekdayLabelTextStyle: const TextStyle(
          color: AppColors.greyColor, fontWeight: FontWeight.bold),
      yearTextStyle: const TextStyle(
          color: AppColors.primaryColor, fontWeight: FontWeight.bold),
      firstDate: startDate,
      lastDate: endDate,
    );

    // 初始化 TabController，这里的 length 为 2，因为有两个 Tab
    _tabController =
        TabController(length: 2, vsync: this, initialIndex: isBooking ? 0 : 1);
    // 监听 TabController 的标签变化
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        // 当标签页切换完成时，可以在这里知道当前选中的是哪个标签
        setState(() {
          isBooking = _tabController.index == 0;
          _multiDatePickerValueWithDefaultValue = [];
          if (isBooking) {
            tip1 = botTip1;
            tip2 = botTip2;
          } else {
            tip1 = waitTip1;
            tip2 = waitTip2;
          }
          if (currentCourse != null) {
            if (isBooking) {
              config = CalendarDatePicker2Config(
                calendarType: CalendarDatePicker2Type.multi,
                selectedDayHighlightColor: AppColors.primaryColor,
                selectedDayTextStyle: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.bold),
                todayTextStyle: const TextStyle(
                    color: AppColors.primaryColor, fontWeight: FontWeight.bold),
                dayTextStyle: const TextStyle(color: Colors.black87),
                disabledDayTextStyle: const TextStyle(color: Colors.grey),
                weekdayLabelTextStyle: const TextStyle(
                    color: AppColors.greyColor, fontWeight: FontWeight.bold),
                yearTextStyle: const TextStyle(
                    color: AppColors.primaryColor, fontWeight: FontWeight.bold),
                firstDate: currentCourse!.bookStartDate,
                lastDate: currentCourse!.bookEndDate,
              );
            } else {
              config = CalendarDatePicker2Config(
                calendarType: CalendarDatePicker2Type.multi,
                selectedDayHighlightColor: AppColors.primaryColor,
                selectedDayTextStyle: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.bold),
                todayTextStyle: const TextStyle(
                    color: AppColors.primaryColor, fontWeight: FontWeight.bold),
                dayTextStyle: const TextStyle(color: Colors.black87),
                disabledDayTextStyle: const TextStyle(color: Colors.grey),
                weekdayLabelTextStyle: const TextStyle(
                    color: AppColors.greyColor, fontWeight: FontWeight.bold),
                yearTextStyle: const TextStyle(
                    color: AppColors.primaryColor, fontWeight: FontWeight.bold),
                firstDate: currentCourse!.notifyStartDate,
                lastDate: currentCourse!.notifyEndDate,
              );
            }
          }
        });
      }
    });

    // 检查是否有克隆数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args =
          ModalRoute.of(context)?.settings.arguments as RouteArguments?;
      if (args != null && args.data.containsKey('clonePlan')) {
        final plan = args.data['clonePlan'] as Plan;
        _applyClonedPlan(plan);
      }
    });

    _customPlayersController.addListener(() {
      setState(() {
        customPlayers = int.tryParse(_customPlayersController.text);
      });
      // 自定义玩家数量变化时使用防抖重新分析
      if (hotDealsOnly) {
        _debouncedPerformHotDealAnalysis();
      }
    });
  }

  @override
  void dispose() {
    _removeTopSuggestionOverlay();
    AppLogger.info("HomeFormScreen(${widget.key}): dispose", 'UI');
    _tabController.dispose();
    _budgetController.dispose();
    _customPlayersController.dispose(); // 释放控制器
    _debounceTimer?.cancel(); // 取消防抖定时器

    // 只在内部创建的 FocusNode 才需要释放
    if (widget.customPlayersFocusNode == null) {
      _customPlayersFocusNode.dispose();
    }
    if (widget.budgetFocusNode == null) {
      _budgetFocusNode.dispose();
    }

    super.dispose();
  }

  // KeyboardActions 配置已移至 home.dart

// 加载缓存的小时数
  Future<void> _loadCachedHours() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      cachedHours = prefs.getInt('cachedHours'); // 从本地获取缓存的小时数
    });
  }

  // 缓存小时数到本地
  Future<void> _cacheHours(int hours) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('cachedHours', hours); // 将小时数存储到本地
  }

  @override
  void didUpdateWidget(covariant HomeFormScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    AppLogger.info(
        "HomeFormScreen: didUpdateWidget,now isBooking: ${widget.isBooking} old isBooking: ${oldWidget.isBooking}",
        'UI');
    //
    if (oldWidget.isLoggedIn != widget.isLoggedIn) {
      if (selectedCourse != null) {
        Provider.of<APIBase>(context, listen: false)
            .getCourse(selectedCourse!.id)
            .then((course) => {
                  setState(() {
                    currentCourse = course;
                  })
                });
      }
    }
  }

  Widget _buildDefaultMultiDatePickerWithValue() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 10),
        CalendarDatePicker2(
          config: config,
          value: _multiDatePickerValueWithDefaultValue,
          onValueChanged: (dates) {
            setState(() => _multiDatePickerValueWithDefaultValue = dates);
            // 日期变化时使用防抖重新分析
            if (hotDealsOnly) {
              _debouncedPerformHotDealAnalysis();
            }
          },
        ),
        const SizedBox(height: 10),
        if (adjustmentType == 'DATES' || adjustmentType == 'DATE')
          AnalysisTip(
              recommend: adjustmentRecommend,
              suggestion: adjustmentSuggestion,
              highlight: _highlightDates,
              onVisibilityChanged: (visible) {
                setState(() {
                  _isAnalysisTipVisible = visible;
                });
              }),
      ],
    );
  }

  String formatTimeOfDay(TimeOfDay timeOfDay) {
    final now = DateTime.now();
    final dt = DateTime(
        now.year, now.month, now.day, timeOfDay.hour, timeOfDay.minute);
    final format =
        DateFormat('HH:mm'); // you can change format to your own format
    return format.format(dt);
  }

  onCourseSelected(SelectedCourseItem course) {
    if (selectedCourse?.id == course.id) {
      return;
    }
    Provider.of<APIBase>(context, listen: false)
        .getCourse(course.id)
        .then((courseData) {
      setState(() {
        currentCourse = courseData;
        // 创建包含 outer_id 的 SelectedCourseItem
        selectedCourse = SelectedCourseItem(
          id: course.id,
          name: course.name,
          outerId: courseData.outerId, // 使用从详细数据中获取的 outer_id
        );

        // 使用缓存的小时数，如果没有缓存则使用 course.defaultExpiredHours
        hours = cachedHours ?? courseData.defaultExpiredHours;

        prices = courseData.prices;
        if (prices.isNotEmpty) {
          selectedPrice = prices.first;
        }

        startTime = courseData.startTime;
        endTime = courseData.endTime;
        config = CalendarDatePicker2Config(
          calendarType: CalendarDatePicker2Type.multi,
          selectedDayHighlightColor: AppColors.primaryColor,
          selectedDayTextStyle:
              const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          todayTextStyle: const TextStyle(
              color: AppColors.primaryColor, fontWeight: FontWeight.bold),
          dayTextStyle: const TextStyle(color: Colors.black87),
          disabledDayTextStyle: const TextStyle(color: Colors.grey),
          weekdayLabelTextStyle: const TextStyle(
              color: AppColors.greyColor, fontWeight: FontWeight.bold),
          yearTextStyle: const TextStyle(
              color: AppColors.primaryColor, fontWeight: FontWeight.bold),
          firstDate:
              isBooking ? courseData.bookStartDate : courseData.notifyStartDate,
          lastDate:
              isBooking ? courseData.bookEndDate : courseData.notifyEndDate,
        );
        AppLogger.info(
            'onChanged: After setState:isBooking:$isBooking selectedCourse:$currentCourse, prices:$prices, selectedPrice:$selectedPrice,startDate:$startDate,startTime:$startTime, endDate:$endDate, endTime:$endTime, config:$config',
            'UI');

        // 球场变化时重新分析
        if (hotDealsOnly) {
          AppLogger.info('球场选择完成，触发热门优惠分析 (hotDealsOnly: $hotDealsOnly)', 'UI');
          _performHotDealAnalysis();
        } else {
          AppLogger.info('球场选择完成，但未启用热门优惠 (hotDealsOnly: $hotDealsOnly)', 'UI');
        }
      });
    });
  }

  handleSubmitBooking(BookingType type,
      {bool showTopSuggestionBar = false}) async {
    // Get the API instance from the context
    final api = Provider.of<APIBase>(context, listen: false);
    if (!widget.isLoggedIn && mounted) {
      inHomeUI = false;
      _removeTopSuggestionOverlay();
      await Navigator.pushNamed(context, '/user/sign_in').then((value) {
        inHomeUI = true;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _updateTopSuggestionOverlay(showTopSuggestionBar);
        });
      });
    } else {
      if (selectedCourse == null) {
        showToast(
          context,
          msg: "Please select a course",
        );

        return;
      }
      // if not binded, show the linke page
      if (type == BookingType.reservation && !currentCourse!.binded) {
        inHomeUI = false;
        _removeTopSuggestionOverlay();
        await Navigator.pushNamed(context, '/user/link_account',
            arguments: RouteArguments(requiresAuth: true, data: {
              'selectedCourse': currentCourse!.platform,
            })).then((value) {
          inHomeUI = true;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _updateTopSuggestionOverlay(showTopSuggestionBar);
          });
          AppLogger.info("HomeFormScreen: push callback", 'UI');
          if (currentCourse != null && !currentCourse!.binded) {
            Provider.of<APIBase>(context, listen: false)
                .getCourse(selectedCourse!.id)
                .then((course) {
              setState(() {
                currentCourse = course;
              });
            });
          }
        });
        return;
      }
      var confirmed = true;
      // if not binded, show the linke page
      if (type == BookingType.reservation &&
          hours < currentCourse!.defaultExpiredHours &&
          !_isChecked) {
// alert 二次确认
        confirmed = await showDialog<bool>(
              context: context,
              builder: (BuildContext context) {
                return AlertDialog(
                  title: const Text('confirm'),
                  content: Text(
                      'Tee times cannot be canceled within ${currentCourse!.defaultExpiredHours} hours of the reservation time.  You have selected a time that is within $hours hours of the reservation. Please confirm.'),
                  actions: <Widget>[
                    TextButton(
                      child: const Text('Cancel'),
                      onPressed: () {
                        Navigator.of(context).pop(false);
                      },
                    ),
                    TextButton(
                      child: const Text('Ok'),
                      onPressed: () {
                        Navigator.of(context).pop(true);
                      },
                    ),
                  ],
                );
              },
            ) ??
            false; // 如果对话框关闭，认返回 false
      }
      if (!confirmed) {
        return;
      }

      if (_multiDatePickerValueWithDefaultValue.isEmpty) {
        showToast(
          context,
          msg: "Please select a date",
        );
        return;
      }
      // Submit the form
      try {
        showLoadingDialog(context);
        int totalPlayers = players; // 默认使用选择的玩家数量
        if (customPlayers != null) {
          totalPlayers = customPlayers!; // 如果有自定义输入，则使用自定义数量
        }
        await api.submitReservation(BookingRequest(
            courseId: selectedCourse!.id,
            timeRange: req.TimeRange(
                start: formatTimeOfDay(selectedTime.startTime),
                end: formatTimeOfDay(selectedTime.endTime)),
            dates: _multiDatePickerValueWithDefaultValue
                .map((e) => e != null
                    ? DateUtils.dateOnly(e).toString().substring(0, 10)
                    : null)
                .toList()
                .cast<String>(),
            players: totalPlayers, // 使用 totalPlayers
            price: selectedPrice?.value,
            type: type,
            expiredHours: hours,
            maxPrice: budget,
            hotOnly: hotDealsOnly,
            cancelIfNotEnough: cancelIfNotEnough));

        widget.notifyParent(type);
      } catch (e) {
        showError(e as Exception, context);
      } finally {
        Navigator.pop(context);
      }
    }
  }

  // 防抖执行热门优惠分析
  void _debouncedPerformHotDealAnalysis() {
    // 取消之前的定时器
    _debounceTimer?.cancel();

    // 设置新的定时器，500毫秒后执行
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performHotDealAnalysis();
    });
  }

  // 热门优惠分析方法
  Future<void> _performHotDealAnalysis() async {
    AppLogger.info(
        '开始热门优惠分析 - selectedCourse: ${selectedCourse?.name}, hotDealsOnly: $hotDealsOnly',
        'ANALYSIS');

    // 检查必要条件 - 只检查球场和 hotDealsOnly 开关
    if (selectedCourse == null || !hotDealsOnly) {
      AppLogger.info(
          '热门优惠分析条件不满足 - selectedCourse是否为空: ${selectedCourse == null}, hotDealsOnly: $hotDealsOnly',
          'ANALYSIS');
      setState(() {
        _hotDealAnalysis = null;
        _isAnalyzing = false;
        // 清空 adjustment 相关状态
        adjustmentType = null;
        adjustmentRecommend = null;
        adjustmentSuggestion = null;
        _highlightPlayers = false;
        _highlightBudget = false;
        _highlightDates = false;
        _highlightTime = false;
        _highlightCourse = false;
      });
      return;
    }

    AppLogger.info('热门优惠分析条件满足，开始执行分析', 'ANALYSIS');
    setState(() {
      _isAnalyzing = true;
    });

    try {
      final api = Provider.of<APIBase>(context, listen: false);

      // 获取玩家数量
      int totalPlayers = players;
      // 自定义数量不做判断。因为一定会出问题。
      // if (customPlayers != null) {
      // totalPlayers = customPlayers!;
      // }

      // 使用 outer_id 作为 courseId，如果没有 outer_id 则回退到 id
      String courseIdForAnalysis =
          selectedCourse!.outerId ?? selectedCourse!.id;

      // 添加调试日志
      AppLogger.info(
          '热门优惠分析: 使用球场ID = $courseIdForAnalysis (outer_id: ${selectedCourse!.outerId}, id: ${selectedCourse!.id})',
          'ANALYSIS');

      // 处理日期：如果没有选择日期，使用空数组
      List<String> analysisDates = [];
      if (_multiDatePickerValueWithDefaultValue.isNotEmpty) {
        analysisDates = _multiDatePickerValueWithDefaultValue
            .map((e) => e != null
                ? DateUtils.dateOnly(e).toString().substring(0, 10)
                : null)
            .toList()
            .cast<String>();
      }

      // 构建请求
      final request = HotDealAnalysisRequest(
        courseId: courseIdForAnalysis, // 使用 outer_id 而不是内部 id
        courseName: selectedCourse!.name,
        startTime: formatTimeOfDay(selectedTime.startTime),
        endTime: formatTimeOfDay(selectedTime.endTime),
        players: totalPlayers,
        dates: analysisDates, // 允许空日期数组
        budget: enableBudget && budget > 0 ? budget : null,
      );

      final response = await api.hotDealAnalysis(request);

      setState(() {
        _hotDealAnalysis = response.analysisData;
        _isAnalyzing = false;

        // 处理分析结果，设置 adjustment 相关状态
        if (response.analysisData.analysis.adjustments.isNotEmpty) {
          // 获取第一个调整建议
          final adjustment = response.analysisData.analysis.adjustments.first;
          adjustmentType = adjustment.type;
          adjustmentRecommend = response.analysisData.recommend;
          adjustmentSuggestion = adjustment.suggestion;

          AppLogger.info(
              '热门优惠分析结果: type=${adjustment.type}, recommend=${response.analysisData.recommend}, suggestion=${adjustment.suggestion}',
              'ANALYSIS');

          // 根据 adjustmentType 设置高亮状态
          _highlightPlayers = adjustmentType == 'PLAYERS';
          _highlightBudget = adjustmentType == 'BUDGET';
          _highlightDates = adjustmentType == 'DATES';
          _highlightTime = adjustmentType == 'TIME';
          _highlightCourse = adjustmentType == 'COURSE';
        } else {
          // 没有调整建议时清空状态
          adjustmentType = null;
          adjustmentRecommend = null;
          adjustmentSuggestion = null;
          _highlightPlayers = false;
          _highlightBudget = false;
          _highlightDates = false;
          _highlightTime = false;
          _highlightCourse = false;
        }
      });
    } catch (e) {
      AppLogger.error('热门优惠分析失败: $e', 'ANALYSIS');
      setState(() {
        _hotDealAnalysis = null;
        _isAnalyzing = false;
        // 清空 adjustment 相关状态
        adjustmentType = null;
        adjustmentRecommend = null;
        adjustmentSuggestion = null;
        _highlightPlayers = false;
        _highlightBudget = false;
        _highlightDates = false;
        _highlightTime = false;
        _highlightCourse = false;
      });
      // 可以选择显示错误提示，但不影响主要功能
    }
  }

  // 添加应用克隆数据的方法
  void _applyClonedPlan(Plan plan) async {
    try {
      setState(() {
        // 设置玩家数量
        int numPlayers = int.parse(plan.players);
        if (numPlayers > 4) {
          players = -1;
          customPlayers = numPlayers;
          _customPlayersController.text = numPlayers.toString();
        } else {
          players = numPlayers;
        }

        // 设置时间范围
        final startTimeParts = plan.timeTange.start.split(':');
        final endTimeParts = plan.timeTange.end.split(':');
        selectedTime = TimeRange(
          startTime: TimeOfDay(
            hour: int.parse(startTimeParts[0]),
            minute: int.parse(startTimeParts[1]),
          ),
          endTime: TimeOfDay(
            hour: int.parse(endTimeParts[0]),
            minute: int.parse(endTimeParts[1]),
          ),
        );

        // 设置预算和其他选项
        hotDealsOnly = plan.hotOnly;
        cancelIfNotEnough = !plan.allowSplit;
        if (plan.maxPrice != null) {
          budget = double.tryParse(plan.maxPrice!) ?? 0.0;
          if (budget > 0) {
            enableBudget = true;
            _budgetController.text = budget.toString();
          }
        }

        // 设置过期时间
        if (plan.expiredHours > 0) {
          hours = plan.expiredHours;
          _cacheHours(hours);
        }
      });

      // 只传递 id，让 onCourseSelected 处理其他数据获取
      onCourseSelected(SelectedCourseItem(
        id: plan.courseId,
        name: plan.courseName,
        // 注意：这里暂时不传递 outerId，让 onCourseSelected 从详细数据中获取
      ));
    } catch (e) {
      if (!mounted) return;
      showError(e as Exception, context);
    }
  }

  // 构建热门优惠分析的方法
  Widget buildHotDealAnalysis() {
    return Container(); // 空实现，实际功能在后面的 build 方法中
  }

  /// 构建顶部建议条 OverlayEntry
  OverlayEntry _buildTopSuggestionBar(VoidCallback? onTap) {
    return OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top,
        left: 0,
        right: 0,
        height: 40,
        child: Material(
          color: Colors.transparent,
          child: GestureDetector(
            onTap: onTap,
            child: Container(
              color: Colors.orange,
              child: const Row(
                children: [
                  Icon(Icons.warning, color: Colors.white),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'There is a suggestion for your selection. Click to view.',
                      style: TextStyle(
                          color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 显示 OverlayEntry，点击事件通过 callback 传递
  void _showTopSuggestionOverlay() {
    if (_topSuggestionOverlayEntry == null) {
      _topSuggestionOverlayEntry = _buildTopSuggestionBar(_onSuggestionTap);
      Overlay.of(_rootContext).insert(_topSuggestionOverlayEntry!);
    }
  }

  /// 顶部建议条点击回调，由主页面负责滚动到对应 key
  void _onSuggestionTap() {
    switch (adjustmentType) {
      case 'DATES':
      case 'DATE':
        if (_datesTipKey.currentContext != null) {
          Scrollable.ensureVisible(
            _datesTipKey.currentContext!,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
        break;
      case 'PLAYERS':
        if (_playersTipKey.currentContext != null) {
          Scrollable.ensureVisible(
            _playersTipKey.currentContext!,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
        break;
      case 'BUDGET':
        if (_budgetTipKey.currentContext != null) {
          Scrollable.ensureVisible(
            _budgetTipKey.currentContext!,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
        break;
      case 'COURSE':
        if (_courseTipKey.currentContext != null) {
          Scrollable.ensureVisible(
            _courseTipKey.currentContext!,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
        break;
      case 'TIME':
        if (_timeTipKey.currentContext != null) {
          Scrollable.ensureVisible(
            _timeTipKey.currentContext!,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
        break;
      default:
        break;
    }
  }

  /// 移除 OverlayEntry
  void _removeTopSuggestionOverlay() {
    _topSuggestionOverlayEntry?.remove();
    _topSuggestionOverlayEntry = null;
  }

  /// 根据 showTopSuggestionBar 状态动态插入/移除 OverlayEntry
  void _updateTopSuggestionOverlay(bool showTopSuggestionBar) {
    if (showTopSuggestionBar) {
      _showTopSuggestionOverlay();
    } else {
      _removeTopSuggestionOverlay();
    }
    _lastShowTopSuggestionBar = showTopSuggestionBar;
  }

  @override
  Widget build(BuildContext context) {
    AppLogger.info("HomeFormScreen: build", 'UI');

    final bool showTopSuggestionBar = inHomeUI &&
        adjustmentType != null &&
        adjustmentSuggestion != null &&
        adjustmentSuggestion!.isNotEmpty &&
        !_isAnalysisTipVisible;

    Widget content = Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                tip1,
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              Text(
                tip2,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.normal,
                  color: Color(0xFF666666), // Use this color
                ),
              ),
              const SizedBox(height: 12),
              const Divider(
                color: Color(0xFFFD9C20),
                height: 1,
              ),
              // 添加 TabBar 并设置颜色
              TabBar(
                controller: _tabController,
                labelColor: AppColors.primaryColor, // 选中标签的颜色
                unselectedLabelColor: Colors.grey, // 未选中标签的颜色
                indicatorColor: secondaryColor, // 指示器颜色
                indicatorWeight: 3, // 指示器粗细
                tabs: const [
                  Tab(text: 'Tee time Bot'),
                  Tab(text: 'Waitlist'),
                ],
              ),
              // const SizedBox(height: 16),
              // Container(
              //   decoration: BoxDecoration(
              //     border: Border.all(
              //       color: const Color(0xFF0984F9), // Use this color
              //     ),
              //     borderRadius: BorderRadius.circular(4.0),
              //   ),
              //   child: Padding(
              //     padding: const EdgeInsets.symmetric(horizontal: 8.0),
              //     child: InputDecorator(
              //       decoration: const InputDecoration(
              //         labelText: 'Which course do you prefer?',
              //         labelStyle:
              //             TextStyle(fontSize: 18.0), // Use this font size
              //         border: InputBorder.none,
              //       ),
              //       child: buildCourseList(context),
              //     ),
              //   ),
              // ),
              const SizedBox(height: 16),
              GestureDetector(
                onTap: () async {
                  inHomeUI = false;
                  _removeTopSuggestionOverlay(); // 进入新页面前移除
                  final course = await Navigator.pushNamed(context, '/search');
                  inHomeUI = true;
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _updateTopSuggestionOverlay(showTopSuggestionBar);
                  });
                  if (course != null) {
                    onCourseSelected(
                        (course as SearchCourseItem).toSelectedCourseItem());
                  }
                },
                child: Container(
                  key: _courseTipKey,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: secondaryColor, // Use this color
                    ),
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'Which course do you prefer?',
                        labelStyle:
                            TextStyle(fontSize: 18.0), // Use this font size
                        border: InputBorder.none,
                      ),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: Text(
                              selectedCourse?.name ?? 'Select a course',
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              softWrap: false,
                            ),
                          ),
                          const Icon(Icons.arrow_forward_ios),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              // COURSE 类型的分析提示
              if (adjustmentType == 'COURSE')
                AnalysisTip(
                  recommend: adjustmentRecommend,
                  suggestion: adjustmentSuggestion,
                  highlight: _highlightCourse,
                  onVisibilityChanged: (visible) {
                    setState(() {
                      _isAnalysisTipVisible = visible;
                    });
                  },
                ),

              const SizedBox(height: 16),
              Container(
                key: _datesTipKey,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: secondaryColor, // Use this color
                  ),
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'What date do you want to play?',
                      labelStyle:
                          TextStyle(fontSize: 18.0), // Use this font size
                      border: InputBorder.none,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(height: 10),
                        _buildDefaultMultiDatePickerWithValue(),
                        const SizedBox(height: 10),
                      ],
                    ),
                  ),
                ),
              ),
              if (selectedCourse != null) const SizedBox(height: 16),
              if (selectedCourse != null)
                Container(
                  key: _timeTipKey,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: secondaryColor, // Use this color
                    ),
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 8),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'What time to play?',
                        labelStyle:
                            TextStyle(fontSize: 18.0), // Use this font size
                        border: InputBorder.none,
                      ),
                      child: Column(
                        // 居中

                        children: <Widget>[
                          Padding(
                            padding: const EdgeInsets.only(top: 10),
                            child: Center(
                              // ignore: missing_required_param
                              child: SfRangeSlider(
                                min: DateTime(2025, 01, 01, startTime.hour,
                                    startTime.minute, 0),
                                max: DateTime(2025, 01, 01, endTime.hour,
                                    endTime.minute, 0),
                                dateFormat: DateFormat.Hm(),
                                dateIntervalType: DateIntervalType.minutes,
                                activeColor: AppColors.primaryColor,
                                values: SfRangeValues(
                                    DateTime(
                                        2025,
                                        01,
                                        01,
                                        selectedTime.startTime.hour,
                                        selectedTime.startTime.minute,
                                        0),
                                    DateTime(
                                        2025,
                                        01,
                                        01,
                                        selectedTime.endTime.hour,
                                        selectedTime.endTime.minute,
                                        0)),
                                showLabels: true,
                                onChanged: (values) {
                                  setState(() {
                                    selectedTime = TimeRange(
                                        startTime: TimeOfDay(
                                            hour: values.start.hour,
                                            minute: values.start.minute),
                                        endTime: TimeOfDay(
                                            hour: values.end.hour,
                                            minute: values.end.minute));
                                  });
                                  // 时间变化时使用防抖重新分析
                                  if (hotDealsOnly) {
                                    _debouncedPerformHotDealAnalysis();
                                  }
                                },
                              ),
                            ),
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text(
                                'Earliest',
                                style: TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(width: 8), // 添加间距
                              OutlinedButton(
                                style: OutlinedButton.styleFrom(
                                  side: const BorderSide(
                                      color: AppColors.primaryColor),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                                onPressed: () async {
                                  // 存下当前的时间，以便取消时恢复
                                  final preTime = selectedTime.startTime;
                                  await showCupertinoModalPopup<TimeOfDay>(
                                    context: context,
                                    builder: (BuildContext context) {
                                      return Container(
                                        height: 255,
                                        color: Colors.white,
                                        child: Column(
                                          children: [
                                            SizedBox(
                                              height: 200,
                                              child: CupertinoDatePicker(
                                                mode: CupertinoDatePickerMode
                                                    .time,
                                                use24hFormat: true,
                                                minimumDate: DateTime(
                                                    2025,
                                                    1,
                                                    1,
                                                    startTime.hour,
                                                    startTime
                                                        .minute), // 设置最小时间为 8:00 AM
                                                maximumDate: DateTime(
                                                    2025,
                                                    1,
                                                    1,
                                                    endTime.hour,
                                                    endTime
                                                        .minute), // 设置最大时间为 6:00 PM
                                                initialDateTime: DateTime(
                                                  2025,
                                                  1,
                                                  1,
                                                  selectedTime.startTime.hour,
                                                  selectedTime.startTime.minute,
                                                ),
                                                onDateTimeChanged:
                                                    (DateTime newDateTime) {
                                                  setState(() {
                                                    final newStartTime =
                                                        TimeOfDay(
                                                      hour: newDateTime.hour,
                                                      minute:
                                                          newDateTime.minute,
                                                    );

                                                    // 如果开始时间大于结束时间，则将结束时间设置为开始时间+30分钟
                                                    final newEndTime =
                                                        newStartTime.isAfter(
                                                                selectedTime
                                                                    .endTime)
                                                            ? TimeOfDay(
                                                                hour: newStartTime
                                                                        .hour +
                                                                    ((newStartTime.minute +
                                                                            30) ~/
                                                                        60),
                                                                minute: (newStartTime
                                                                            .minute +
                                                                        30) %
                                                                    60,
                                                              )
                                                            : selectedTime
                                                                .endTime;

                                                    selectedTime = TimeRange(
                                                      startTime: newStartTime,
                                                      endTime: newEndTime,
                                                    );
                                                  });
                                                  if (hotDealsOnly) {
                                                    _debouncedPerformHotDealAnalysis();
                                                  }
                                                },
                                              ),
                                            ),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceEvenly,
                                              children: [
                                                CupertinoButton(
                                                  child: const Text('Cancel'),
                                                  onPressed: () {
                                                    setState(() {
                                                      selectedTime = TimeRange(
                                                          startTime: preTime,
                                                          endTime: selectedTime
                                                              .endTime);
                                                    });
                                                    Navigator.of(context).pop();
                                                  },
                                                ),
                                                CupertinoButton(
                                                  child: const Text('Ok'),
                                                  onPressed: () {
                                                    Navigator.of(context).pop(
                                                        selectedTime.startTime);
                                                  },
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                },
                                child: Text(
                                  DateFormat.Hm().format(DateTime(
                                      2025,
                                      01,
                                      01,
                                      selectedTime.startTime.hour,
                                      selectedTime.startTime.minute,
                                      0)),
                                  style: const TextStyle(
                                    color: AppColors.primaryColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          // 结束时间
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text(
                                'Latest  ', // 为了对齐，补个空格
                                style: TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(width: 8), // 添加间距
                              OutlinedButton(
                                style: OutlinedButton.styleFrom(
                                  side: const BorderSide(
                                      color: AppColors.primaryColor),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                ),
                                onPressed: () async {
                                  // 存下当前的时间，以便取消时恢复
                                  final preTime = selectedTime.endTime;
                                  await showCupertinoModalPopup<TimeOfDay>(
                                    context: context,
                                    builder: (BuildContext context) {
                                      return Container(
                                        height: 255,
                                        color: Colors.white,
                                        child: Column(
                                          children: [
                                            SizedBox(
                                              height: 200,
                                              child: CupertinoDatePicker(
                                                mode: CupertinoDatePickerMode
                                                    .time,
                                                use24hFormat: true,
                                                minimumDate: DateTime(
                                                    2025,
                                                    1,
                                                    1,
                                                    startTime.hour,
                                                    startTime
                                                        .minute), // 设置最小时间为 8:00 AM
                                                maximumDate: DateTime(
                                                    2025,
                                                    1,
                                                    1,
                                                    endTime.hour,
                                                    endTime
                                                        .minute), // 设置最大时间为 6:00 PM
                                                initialDateTime: DateTime(
                                                  2025,
                                                  1,
                                                  1,
                                                  selectedTime.endTime.hour,
                                                  selectedTime.endTime.minute,
                                                ),
                                                onDateTimeChanged:
                                                    (DateTime newDateTime) {
                                                  setState(() {
                                                    final newEndTime =
                                                        TimeOfDay(
                                                      hour: newDateTime.hour,
                                                      minute:
                                                          newDateTime.minute,
                                                    );

                                                    // 如果开始时间大于结束时间，将开始时间设置为结束时间-30分钟
                                                    TimeOfDay newStartTime =
                                                        selectedTime.startTime;
                                                    if (selectedTime.startTime
                                                                .hour >
                                                            newEndTime.hour ||
                                                        (selectedTime.startTime
                                                                    .hour ==
                                                                newEndTime
                                                                    .hour &&
                                                            selectedTime
                                                                    .startTime
                                                                    .minute >
                                                                newEndTime
                                                                    .minute)) {
                                                      // 计算结束时间-30分钟
                                                      int totalMinutes =
                                                          newEndTime.hour * 60 +
                                                              newEndTime
                                                                  .minute -
                                                              30;
                                                      if (totalMinutes < 0) {
                                                        totalMinutes +=
                                                            24 * 60; // 处理跨天的情况
                                                      }
                                                      newStartTime = TimeOfDay(
                                                        hour:
                                                            totalMinutes ~/ 60,
                                                        minute:
                                                            totalMinutes % 60,
                                                      );
                                                    }

                                                    selectedTime = TimeRange(
                                                      startTime: newStartTime,
                                                      endTime: newEndTime,
                                                    );
                                                  });
                                                  if (hotDealsOnly) {
                                                    _debouncedPerformHotDealAnalysis();
                                                  }
                                                },
                                              ),
                                            ),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceEvenly,
                                              children: [
                                                CupertinoButton(
                                                  child: const Text('Cancel'),
                                                  onPressed: () {
                                                    setState(() {
                                                      selectedTime = TimeRange(
                                                          endTime: preTime,
                                                          startTime:
                                                              selectedTime
                                                                  .startTime);
                                                    });
                                                    Navigator.of(context).pop();
                                                  },
                                                ),
                                                CupertinoButton(
                                                  child: const Text('Ok'),
                                                  onPressed: () {
                                                    Navigator.of(context).pop(
                                                        selectedTime.endTime);
                                                  },
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                },
                                child: Text(
                                  DateFormat.Hm().format(DateTime(
                                      2025,
                                      01,
                                      01,
                                      selectedTime.endTime.hour,
                                      selectedTime.endTime.minute,
                                      0)),
                                  style: const TextStyle(
                                    color: AppColors.primaryColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              // TIME 类型的分析提示
              if (adjustmentType == 'TIME')
                AnalysisTip(
                  recommend: adjustmentRecommend,
                  suggestion: adjustmentSuggestion,
                  highlight: _highlightTime,
                  onVisibilityChanged: (visible) {
                    setState(() {
                      _isAnalysisTipVisible = visible;
                    });
                  },
                ),

              const SizedBox(height: 16),
              Container(
                key: _playersTipKey,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: secondaryColor, // Use this color
                  ),
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: InputDecorator(
                    decoration: const InputDecoration(
                      labelText: 'Players',
                      labelStyle:
                          TextStyle(fontSize: 16.0), // Use this font size
                      border: InputBorder.none,
                    ),
                    child: Column(
                      children: [
                        DropdownButtonHideUnderline(
                          child: DropdownButton<int>(
                            isExpanded: true,
                            value: players,
                            onChanged: (int? newValue) {
                              setState(() {
                                players = newValue!;
                                // 如果选择了 Custom，清空 customPlayers
                                if (newValue == -1) {
                                  customPlayers = null; // Reset custom input
                                }
                              });
                              // 玩家数量变化时重新分析（下拉选择不需要防抖）
                              if (hotDealsOnly) {
                                _performHotDealAnalysis();
                              }
                            },
                            items: (currentCourse?.allowGroup == true
                                    ? <int>[1, 2, 3, 4, -1]
                                    : <int>[1, 2, 3, 4]) // 只有允许组队时才显示 Custom 选项
                                .map<DropdownMenuItem<int>>((int value) {
                              return DropdownMenuItem<int>(
                                value: value,
                                child: Text(
                                  value == -1 ? 'Custom' : value.toString(),
                                  style:
                                      const TextStyle(color: Color(0xFF109600)),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                        if (players == -1) // 如果选择了 Custom，显示输入框
                          Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: _customPlayersController,
                                  focusNode: _customPlayersFocusNode,
                                  keyboardType: TextInputType.number,
                                  decoration: const InputDecoration(
                                    hintText: 'Enter number of players',
                                    border: InputBorder.none,
                                  ),
                                  textInputAction: TextInputAction.done,
                                  onEditingComplete: () {
                                    _customPlayersFocusNode.unfocus();
                                  },
                                ),
                              ),
                            ],
                          ),
                        if (customPlayers != null && customPlayers! > 4)
                          Column(
                            children: [
                              const SizedBox(height: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade50,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text(
                                      'Cancel if not enough tee time',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    Switch(
                                      value: cancelIfNotEnough,
                                      activeColor: AppColors.primaryColor,
                                      onChanged: (bool value) {
                                        setState(() {
                                          cancelIfNotEnough = value;
                                        });
                                      },
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                ),
              ),
              // PLAYERS 类型的分析提示
              if (adjustmentType == 'PLAYERS')
                AnalysisTip(
                  recommend: adjustmentRecommend,
                  suggestion: adjustmentSuggestion,
                  highlight: _highlightPlayers,
                  onVisibilityChanged: (visible) {
                    setState(() {
                      _isAnalysisTipVisible = visible;
                    });
                  },
                ),

              const SizedBox(height: 16),
              if (selectedCourse != null && prices.isNotEmpty)
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: secondaryColor,
                    ),
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'Price',
                        labelStyle: TextStyle(fontSize: 16.0),
                        border: InputBorder.none,
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          isExpanded: true,
                          value: selectedPrice?.value,
                          onChanged: (String? newValue) {
                            setState(() {
                              selectedPrice = prices.firstWhere(
                                  (element) => element.value == newValue);
                            });
                          },
                          items: prices
                              .map<DropdownMenuItem<String>>((Price price) {
                            return DropdownMenuItem<String>(
                              value: price.value,
                              child: Text(
                                price.label,
                                style:
                                    const TextStyle(color: Color(0xFF109600)),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
                ),

              const SizedBox(height: 16),

              if (isBooking &&
                  currentCourse != null &&
                  currentCourse!.platform.id == '4')
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: secondaryColor,
                    ),
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16.0, vertical: 12.0),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'What is your green fee budget?',
                        labelStyle: TextStyle(
                          fontSize: 16.0,
                          height: 2.5,
                        ),
                        border: InputBorder.none,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 8),
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: AppColors.primaryColor.withAlpha(13),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  color: AppColors.primaryColor.withAlpha(13)),
                            ),
                            child: RichText(
                              text: const TextSpan(
                                style: TextStyle(
                                  color: AppColors.greyColor,
                                  fontSize: 14,
                                  height: 1.5,
                                ),
                                children: [
                                  TextSpan(
                                    text: 'Set a budget to:\n',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  TextSpan(
                                      text:
                                          '• Book courses within your limit.\n'),
                                  TextSpan(
                                      text:
                                          '• Auto-apply coupons and compare prices for TeeOff/GolfNow.\n'),
                                  TextSpan(
                                      text: '• Prioritize balance payment.'),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              // Manage Promo Codes 按钮 - 仅在登录状态下显示
              if (isBooking &&
                  currentCourse != null &&
                  widget.isLoggedIn &&
                  currentCourse!.platform.id == '4')
                const SizedBox(height: 16),
              if (isBooking &&
                  currentCourse != null &&
                  widget.isLoggedIn &&
                  currentCourse!.platform.id == '4')
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      inHomeUI = false;
                      _removeTopSuggestionOverlay();
                      await Navigator.pushNamed(context, '/promo_code_manager');
                      inHomeUI = true;
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _updateTopSuggestionOverlay(showTopSuggestionBar);
                      });
                    },
                    icon: const Icon(Icons.local_offer, size: 18),
                    label: const Text('Manage Promo Codes'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange.shade50,
                      foregroundColor: Colors.orange.shade700,
                      elevation: 0,
                      side: BorderSide(color: Colors.orange.shade200),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),

              // Hot Deals Only 独立表单块
              if (isBooking &&
                  currentCourse != null &&
                  currentCourse!.platform.id == '4')
                const SizedBox(height: 16),
              if (isBooking &&
                  currentCourse != null &&
                  currentCourse!.platform.id == '4')
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Hot Deals Only',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Switch(
                        value: hotDealsOnly,
                        activeColor: AppColors.primaryColor,
                        onChanged: (bool value) {
                          setState(() {
                            hotDealsOnly = value;
                          });
                          if (value) {
                            _performHotDealAnalysis();
                          } else {
                            setState(() {
                              _hotDealAnalysis = null;
                              _isAnalyzing = false;
                              // 清空 adjustment 相关状态
                              adjustmentType = null;
                              adjustmentRecommend = null;
                              adjustmentSuggestion = null;
                              _highlightPlayers = false;
                              _highlightBudget = false;
                              _highlightDates = false;
                              _highlightTime = false;
                              _highlightCourse = false;
                            });
                          }
                        },
                      ),
                    ],
                  ),
                ),
              // Set Maximum Price 独立表单块
              if (isBooking && currentCourse != null) const SizedBox(height: 8),
              if (isBooking && currentCourse != null)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Set Maximum Price',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Switch(
                            value: enableBudget,
                            activeColor: AppColors.primaryColor,
                            onChanged: (bool value) {
                              setState(() {
                                enableBudget = value;
                                if (!value) budget = 0.0;
                              });
                              if (hotDealsOnly) {
                                _performHotDealAnalysis();
                              }
                            },
                          ),
                        ],
                      ),
                      if (enableBudget) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            const Text('\$',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            Expanded(
                              child: TextField(
                                key: _budgetTipKey,
                                controller: _budgetController,
                                focusNode: _budgetFocusNode,
                                keyboardType: TextInputType.number,
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  hintText: '0',
                                  contentPadding:
                                      EdgeInsets.symmetric(horizontal: 8),
                                ),
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: AppColors.primaryColor,
                                  fontWeight: FontWeight.w500,
                                ),
                                textInputAction: TextInputAction.done,
                                // 失去焦点时再触发 _debouncedPerformHotDealAnalysis
                                onEditingComplete: () {
                                  _budgetFocusNode.unfocus();
                                  if (hotDealsOnly && enableBudget) {
                                    _debouncedPerformHotDealAnalysis();
                                  }
                                },
                                onTapOutside: (event) {},
                                // 监听输入变化，仅更新 budget，不触发分析。
                                onChanged: (value) {
                                  setState(() {
                                    budget = double.tryParse(value) ?? 0.0;
                                  });
                                  // 不在 onChanged 里触发分析
                                },
                              ),
                            ),
                            const Text('per player',
                                style: TextStyle(color: Colors.grey)),
                          ],
                        ),
                        if (budget > 0)
                          Padding(
                            padding: const EdgeInsets.only(top: 4, left: 4),
                            child: Text(
                              'Total: \$${(budget * (players == -1 ? (customPlayers ?? 1) : players)).toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.green,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        // AnalysisTip
                        AnalysisTip(
                          recommend: adjustmentType == 'BUDGET'
                              ? adjustmentRecommend
                              : null,
                          suggestion: adjustmentType == 'BUDGET'
                              ? adjustmentSuggestion
                              : null,
                          highlight: _highlightBudget,
                          onVisibilityChanged: (visible) {
                            setState(() {
                              _isAnalysisTipVisible = visible;
                            });
                          },
                        ),
                      ],
                    ],
                  ),
                ),

              const SizedBox(height: 16),
              if (isBooking && selectedCourse != null)
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: secondaryColor, // Use this color
                    ),
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 8.0, vertical: 8.0),
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText:
                            'When should we stop looking for a tee time?',
                        labelStyle:
                            TextStyle(fontSize: 18.0), // Use this font size
                        border: InputBorder.none,
                      ),
                      child: Column(
                        // 居中

                        children: <Widget>[
                          Padding(
                            padding: const EdgeInsets.only(top: 10),
                            child: Center(
                              // ignore: missing_required_param
                              child: SfSlider(
                                min: currentCourse!.minExpiredHours.toDouble(),
                                max: currentCourse!.maxExpiredHours.toDouble(),
                                activeColor: AppColors.primaryColor,
                                value: hours.toDouble(),
                                stepSize: 1,
                                showTicks: true,
                                showDividers: true,
                                minorTicksPerInterval: 6,
                                showLabels: true,
                                numberFormat: NumberFormat('##h'),
                                onChanged: (value) {
                                  setState(() {
                                    hours = value.toInt();
                                    _cacheHours(hours);
                                  });
                                },
                              ),
                            ),
                          ),
                          OutlinedButton(
                              onPressed: null,
                              child: Text(
                                '$hours hours prior',
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              )),
                          const SizedBox(
                            height: 8,
                          ),
                          Row(
                            children: [
                              if (selectedCourse != null &&
                                  hours < currentCourse!.defaultExpiredHours)
                                Checkbox(
                                  value: _isChecked, // 复选的状态
                                  activeColor: AppColors.primaryColor,
                                  onChanged: (bool? value) {
                                    setState(() {
                                      _isChecked = value!;
                                    });
                                  },
                                ),
                              Expanded(
                                  child: Text(
                                "Tee times cannot be canceled within  ${currentCourse!.defaultExpiredHours} hours at this course",
                                softWrap: true,
                              )),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ),

              const SizedBox(height: 30),

              SizedBox(
                height: 40,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // 这放置第一个选项卡的内容
                    SizedBox(
                      width: MediaQuery.of(context)
                          .size
                          .width, // 100% of screen width
                      child: ElevatedButton(
                        style: ButtonStyle(
                          backgroundColor: WidgetStateProperty.all<Color>(
                            Theme.of(context)
                                .primaryColor, // Use the primary color
                          ),
                          foregroundColor: WidgetStateProperty.all<Color>(
                              Colors.white), // White text
                        ),
                        child: const Text('Set My Schedule!'),
                        onPressed: () async {
                          handleSubmitBooking(BookingType.reservation,
                              showTopSuggestionBar: showTopSuggestionBar);
                        },
                      ),
                    ),
                    // 这里放置第二个选项卡的内容

                    SizedBox(
                      width: MediaQuery.of(context)
                          .size
                          .width, // 100% of screen width
                      child: ElevatedButton(
                        style: ButtonStyle(
                          backgroundColor: WidgetStateProperty.all<Color>(
                            Theme.of(context)
                                .primaryColor, // Use the primary color
                          ),
                          foregroundColor: WidgetStateProperty.all<Color>(
                              Colors.white), // White text
                        ),
                        child: const Text('Join Waitlist'),
                        onPressed: () async {
                          handleSubmitBooking(BookingType.notification,
                              showTopSuggestionBar: showTopSuggestionBar);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );

    // 动态插入/移除 OverlayEntry
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_lastShowTopSuggestionBar != showTopSuggestionBar) {
        _updateTopSuggestionOverlay(showTopSuggestionBar);
      }
    });

    // 直接返回主内容，不再用 Stack/Positioned 渲染 topSuggestionBar
    return content;
  }
}

/// 表单项下方的分析提示组件
class AnalysisTip extends StatefulWidget {
  final String? recommend;
  final String? suggestion;
  final bool highlight;
  final VoidCallback? onTap;
  final ValueChanged<bool>? onVisibilityChanged;
  const AnalysisTip({
    super.key,
    this.recommend,
    this.suggestion,
    this.highlight = false,
    this.onTap,
    this.onVisibilityChanged,
  });

  @override
  State<AnalysisTip> createState() => _AnalysisTipState();
}

class _AnalysisTipState extends State<AnalysisTip> {
  @override
  Widget build(BuildContext context) {
    if ((widget.recommend == null || widget.recommend!.isEmpty) &&
        (widget.suggestion == null || widget.suggestion!.isEmpty)) {
      return const SizedBox.shrink();
    }
    return VisibilityDetector(
      key: widget.key ?? UniqueKey(),
      onVisibilityChanged: (info) {
        widget.onVisibilityChanged?.call(info.visibleFraction > 0);
      },
      child: GestureDetector(
        onTap: widget.onTap,
        child: Container(
          margin: const EdgeInsets.only(top: 6, bottom: 2),
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
          decoration: BoxDecoration(
            color: widget.highlight
                ? Colors.yellow.shade100
                : Colors.grey.shade100,
            border: widget.highlight
                ? Border.all(color: Colors.orange, width: 2)
                : Border.all(color: Colors.grey.shade300, width: 1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.suggestion != null && widget.suggestion!.isNotEmpty)
                Row(
                  children: [
                    const Icon(Icons.warning, color: Colors.orange, size: 16),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        widget.suggestion!,
                        style: const TextStyle(
                          color: Colors.orange,
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              if (widget.recommend != null && widget.recommend!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    widget.recommend!,
                    style: const TextStyle(
                      color: Colors.black87,
                      fontSize: 13,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
