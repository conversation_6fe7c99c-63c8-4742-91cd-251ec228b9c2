import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/booking_req.dart';
import 'package:golf/widgets/golf_appbar.dart';
import 'package:provider/provider.dart';

class ReservationScreen extends StatefulWidget {
  final String id;
  const ReservationScreen({super.key, required this.id});

  @override
  _ReservationScreenState createState() => _ReservationScreenState();
}

class _ReservationScreenState extends State<ReservationScreen> {
  late Future<ShareBookingDetail> detailFuture;

  @override
  void initState() {
    super.initState();
    detailFuture = Provider.of<APIBase>(context, listen: false)
        .getSharedBooking(ShareBookingDetailRequest(id: widget.id));
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<ShareBookingDetail>(
      future: detailFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(
              color: AppColors.primaryColor,
            ),
          );
        }
        if (snapshot.hasError) {
          return Center(
            child: Text('Error occurred,{$snapshot.error}'),
          );
        }
        final ShareBookingDetail detail = snapshot.data!;
        return

            // fetch data of reservation by id.
            Scaffold(
          appBar: const GolfAppBar(
            title: 'Reservation Info',
          ),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                const Text(
                  'Reservation Info',
                  style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                const Divider(
                  color: AppColors.primaryColor,
                  height: 1,
                ),
                const SizedBox(height: 16),
                const Text(
                    'I have booked a tee time through Tee time bot and now I am sharing it with you.',
                    style: TextStyle(color: AppColors.greyColor, fontSize: 13)),
                const SizedBox(height: 16.0),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: <Widget>[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            const Text(
                              'Course',
                              style: TextStyle(fontSize: 14),
                            ),
                            Text(
                              detail.courseName,
                              style: const TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8.0),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            const Text(
                              'Tee Time',
                              style: TextStyle(fontSize: 14),
                            ),
                            Text(
                              '${detail.reservationDate} ${detail.teetime}',
                              style: const TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8.0),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            const Text(
                              'Player',
                              style: TextStyle(fontSize: 14),
                            ),
                            Text(
                              detail.players,
                              style: const TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16.0),
                Center(
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: ElevatedButton(
                      style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.all(
                            AppColors.primaryColor), // 设置背景色为蓝色
                        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                                AppBorderRadius.small), // 设置圆角
                          ),
                        ),
                      ),
                      onPressed: () {
                        final api =
                            Provider.of<APIBase>(context, listen: false);
                        api.isLoggedIn().then((hasLogin) {
                          // 如果登录了就跳首页，否则跳注册页(带上邀请人id )
                          if (hasLogin) {
                            Navigator.pushNamed(context, '/');
                          } else {
                            Navigator.pushNamed(
                                context, '/user/sign_up/${detail.userId}');
                          }
                        });
                      },
                      child: const Text(
                        'Book for me',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
