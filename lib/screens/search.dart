import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:golf/config.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/services/provider/auth_notifier.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/course_req.dart';
import 'package:golf/services/req/platform_req.dart';
import 'package:golf/services/req/req.dart';
import 'package:golf/services/req/search.dart';
import 'package:golf/utils/exception.dart';

import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:collection/collection.dart';

class LocationGuideDialog extends StatelessWidget {
  const LocationGuideDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        color: Theme.of(context).primaryColor,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        'Location Services',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            Flexible(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildGuideSection(context, 'iPhone - Safari', [
                        _buildMethod(
                            'Method 1:',
                            [
                              'Open "Settings"',
                              'Tap "Privacy & Security"',
                              'Tap "Location Services"',
                              'Make sure "Location Services" is turned on',
                              'Scroll down and find "Safari Websites"',
                              'Select "While Using the App" or "Ask Next Time"',
                              'Reopen Safari and refresh the page',
                            ],
                            context),
                        _buildMethod(
                            'Method 2:',
                            [
                              'Open "Settings"',
                              'Scroll down and tap "Safari"',
                              'Scroll down to find "Website Settings"',
                              'Tap "Location"',
                              'Select "Ask" or "Allow"',
                            ],
                            context),
                      ]),
                      _buildGuideSection(context, 'iPhone - Chrome', [
                        _buildMethod(
                            null,
                            [
                              'Open "Settings"',
                              'Scroll down and find "Chrome"',
                              'Tap "Location"',
                              'Select "While Using the App" or "Ask Next Time"',
                              'Make sure "Location Services" is also enabled in "Settings > Privacy & Security > Location Services"',
                            ],
                            context),
                      ]),
                      _buildGuideSection(context, 'Android - Chrome', [
                        _buildMethod(
                            null,
                            [
                              'Open "Settings"',
                              'Tap "Apps" or "Application Manager"',
                              'Find and tap "Chrome"',
                              'Tap "Permissions"',
                              'Tap "Location"',
                              'Select "Allow" or "Allow only while using the app"',
                            ],
                            context),
                      ]),
                      _buildGuideSection(
                          context, 'Android - Samsung Internet', [
                        _buildMethod(
                            null,
                            [
                              'Open "Settings"',
                              'Tap "Apps"',
                              'Find and tap "Samsung Internet"',
                              'Tap "Permissions"',
                              'Tap "Location"',
                              'Select "Allow" or "Allow only while using the app"',
                            ],
                            context),
                      ]),
                      Container(
                        margin: const EdgeInsets.only(top: 20),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.orange.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.info_outline,
                                    color: Colors.orange[700], size: 20),
                                const SizedBox(width: 8),
                                const Text(
                                  'Additional Notes',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            _buildBulletPoint(
                                'Make sure your device\'s location service is turned on'),
                            _buildBulletPoint(
                                'After changing settings, please refresh the page'),
                            _buildBulletPoint(
                                'Some browsers may require you to close and reopen them'),
                            _buildBulletPoint(
                                'If still not working, try clearing your browser cache'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuideSection(
      BuildContext context, String title, List<Widget> methods) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                title.contains('iPhone')
                    ? Icons.phone_iphone
                    : Icons.phone_android,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...methods,
        ],
      ),
    );
  }

  Widget _buildMethod(
      String? methodTitle, List<String> steps, BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (methodTitle != null) ...[
          const SizedBox(height: 8),
          Text(
            methodTitle,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
        const SizedBox(height: 8),
        ...steps.asMap().entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.only(left: 16, bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 20,
                  height: 20,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Center(
                    child: Text(
                      '${entry.key + 1}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    entry.value,
                    style: const TextStyle(
                      fontSize: 14,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6, right: 8),
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: Colors.orange[700],
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 14,
                height: 1.4,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  _SearchPageState createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  Timer? _debounce;

  List<SearchCourseItem> _searchResults = [];
  int selecteIndex = 0;

  final FocusNode _focusNode = FocusNode();
  // 定义一个 GlobalKey
  final GlobalKey _textFieldKey = GlobalKey();
  OverlayEntry? _overlayEntry;
  List<String> _searchHistory = []; //  搜索历史
  bool _isLoadingMore = false;
  Paginator paginator = Paginator(prev: 0, next: 0, total: -1);
  int currentPage = 1;
  LatLng? location;
  String? locationError;
  bool isLoggedIn = false;
  final int pageSzie = 10;
  late AuthNotifier authNotifier;
  late APIBase api;
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  // 新增筛选相关变量
  String selectedHoles = '18'; // 'any', '9', '18'
  Platform? selectedPlatform;
  List<Platform> platforms = [
    Platform(
      id: "1",
      name: "Direct Booking",
      website: "",
      linkType: LinkType.code,
      hasToken: false,
    ),
    Platform(
      id: "2",
      name: "GolfNow / TeeOff",
      website: "",
      linkType: LinkType.code,
      hasToken: false,
    ),
  ];

  // int _searchCount = 0;
  bool isInitState = true;
  bool isSettingsLoaded = false;

  @override
  void initState() {
    super.initState();
    _initializeData();
    _loadSearchHistory();
    selecteIndex = 0;
    _scrollController.addListener(_onScroll);
    authNotifier = context.read<AuthNotifier>();
    authNotifier.addListener(onAuthChanged);
    api = Provider.of<APIBase>(context, listen: false);
    //load polular courses
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        if (!_isOverlayShown) {
          _showOverlay();
          _isOverlayShown = true;
        }
      } else {
        // 延迟 200 毫秒再移除 Overlay
        Future.delayed(const Duration(milliseconds: 200), () {
          // 只有在不是搜索操作时才移除 overlay
          if (!_isScrolling) {
            _removeOverlay();
          }
        });
      }
    });
    AppLogger.info('search initState, isLoggedIn: $isLoggedIn', 'UI');
    // 初始化 TabController
    _tabController = TabController(length: 3, vsync: this);
  }

  Future<void> _initializeData() async {
    await onAuthChanged();
    await _loadSettings();

    // 初始化搜索
    await getLocation();
    if (isLoggedIn) {
      final response = await api.getCourseList(FavoriteCourseRequest(
              page: 1,
              size: pageSzie,
              location: location,
              holes: selectedHoles == 'any' ? null : int.parse(selectedHoles),
              platformId: selectedPlatform?.id)
          .toListCourseRequest());

      setState(() {
        _searchResults = response.items;
        paginator = response.paginator;
        if (response.items.isNotEmpty) {
          selecteIndex = 2;
          _tabController.index = 2;
        } else {
          callnearby();
        }
      });
    } else {
      callnearby();
    }
  }

  Future<void> onAuthChanged() async {
    AppLogger.info('search onAuthChanged', 'UI');
    final api = Provider.of<APIBase>(context, listen: false);

    final value = await api.isLoggedIn();
    AppLogger.info('search isLoggedIn: $value', 'UI');
    if (isLoggedIn != value) {
      setState(() {
        isLoggedIn = value;
      });
    }
  }

  bool _isOverlayShown = false;
  bool _isScrolling = false;

  // 添加保存和加载设置的方法
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('selectedHoles', selectedHoles);
    await prefs.setString('selectedPlatformId', selectedPlatform?.id ?? '');
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    String platformId = prefs.getString('selectedPlatformId') ?? '';

    setState(() {
      selectedHoles = prefs.getString('selectedHoles') ?? '18';
      if (platformId.isNotEmpty && platforms.isNotEmpty) {
        selectedPlatform = platforms.firstWhereOrNull(
          (p) => p.id == platformId,
        );
      }
    });
  }

  Future<void> getLocation() async {
    try {
      final position = await _determinePosition();
      location =
          LatLng(latitude: position.latitude, longitude: position.longitude);
    } catch (err) {
      locationError =
          'Location failed, please check if location permissions are enabled.';
    }
  }

  callnearby() {
    api
        .getCourseList(NearByCourseRequest(
                page: 1,
                size: pageSzie,
                location: location,
                holes: selectedHoles == 'any' ? null : int.parse(selectedHoles),
                platformId: selectedPlatform?.id)
            .toListCourseRequest())
        .then((response) {
      setState(() {
        _searchResults = response.items;
        paginator = response.paginator;
      });
      // 回顶部
      _scrollController.jumpTo(0);
    });
  }

  Future<Position> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;

    // 检查位置服务是否启用
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      // 如果位置服务未启用，则返回一个错误或请求用户启用位置服务
      return Future.error('Location services are disabled.');
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        // 如果权限被拒绝，则返回一个错误
        return Future.error('Location permissions are denied');
      }
    }

    if (permission == LocationPermission.deniedForever) {
      // 如果权限被永久拒绝，则回一个错误，并引导用户到设置中手动开启权限
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    // 当所有的权限检查通过后，获取并返回当前位置
    return await Geolocator.getCurrentPosition();
  }

  Future<void> _loadSearchHistory() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _searchHistory = prefs.getStringList('searchHistory') ?? [];
      _searchHistory = _searchHistory.map((e) => e.trim()).toList();
    });
  }

  Future<void> _saveSearchHistory() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.setStringList('searchHistory', _searchHistory);
  }

  void _showOverlay() {
    final overlay = Overlay.of(context);
    _overlayEntry = _createOverlayEntry();
    overlay.insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _isOverlayShown = false;
  }

  OverlayEntry _createOverlayEntry() {
    RenderBox renderBox =
        _textFieldKey.currentContext!.findRenderObject() as RenderBox;
    var size = renderBox.size;
    var offset = renderBox.localToGlobal(Offset.zero);

    double rowHeight = 56.0;
    double overlayHeight = rowHeight * 4;

    return OverlayEntry(
      builder: (context) {
        return Positioned(
          left: 0, // 从左边开始
          top: offset.dy + size.height, // 从搜索框底部开始
          right: 0, // 延伸到右边
          height: MediaQuery.of(context).size.height -
              (offset.dy + size.height), // 剩余的屏幕高度
          child: Material(
            color: Colors.transparent,
            child: Stack(
              children: [
                // 处理点击空白区域
                Positioned.fill(
                  child: GestureDetector(
                    onTap: _removeOverlay,
                    child: Container(
                      color: Colors.transparent,
                    ),
                  ),
                ),
                // 搜索历史列表
                Positioned(
                  left: offset.dx,
                  top: 0, // 从顶部开始
                  width: size.width,
                  height: overlayHeight,
                  child: Material(
                    elevation: 4.0,
                    child: NotificationListener<ScrollNotification>(
                      onNotification: (ScrollNotification notification) {
                        if (notification is ScrollStartNotification) {
                          _isScrolling = true;
                        } else if (notification is ScrollEndNotification) {
                          _isScrolling = false;
                        }
                        return true;
                      },
                      child: ListView.builder(
                        padding: EdgeInsets.zero,
                        itemCount: _searchHistory.length,
                        itemExtent: rowHeight,
                        itemBuilder: (context, index) {
                          return ListTile(
                            title: Text(_searchHistory[index]),
                            onTap: () {
                              _searchController.text = _searchHistory[index];
                              _onSearchChanged(_searchHistory[index]);
                              _removeOverlay();
                            },
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    authNotifier.removeListener(onAuthChanged);
    _debounce?.cancel();
    _searchController.dispose();
    _scrollController.dispose(); // 不要忘记释放资源
    _tabController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String query) {
    if (_focusNode.hasFocus) {
      _focusNode.unfocus(); // 失去焦点
    }
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      _performSearch(query);
    });
  }

  void _performSearch(String query) {
    //
    final api = Provider.of<APIBase>(context, listen: false);
    currentPage = 1;
    api
        .getCourseList(SearchCourseRequest(
                keyword: query,
                location: location,
                pageNum: currentPage,
                pageSize: pageSzie,
                holes: selectedHoles == 'any' ? null : int.parse(selectedHoles),
                platformId: selectedPlatform?.id)
            .toListCourseRequest())
        .then((response) {
      setState(() {
        _searchResults = response.items;
        paginator = response.paginator;
        // _searchCount = response.paginator.total;
        selecteIndex = 0;
        _tabController.index = 0;
        // trim query
        query = query.trim();
        // 将搜索词添加到历史记录中
        if (!_searchHistory.contains(query) && query.isNotEmpty) {
          setState(() {
            _searchHistory.add(query);
            // 只保留 10 条历史记录
            if (_searchHistory.length > 10) {
              _searchHistory.removeAt(0);
            }
            _saveSearchHistory();
          });
        }
      });
    });
  }

  void _onScroll() {
    if (!_isLoadingMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent) {
      _loadMoreData();
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore) {
      return;
    }
    // 没更多页了。也应该返回
    if (currentPage > paginator.total) {
      return;
    }
    if (_searchResults.isEmpty) {
      return;
    }
    setState(() {
      _isLoadingMore = true; //show loading
    });

    var page = currentPage + 1;
    bool? used = selecteIndex == 1 ? true : null;
    bool? favorite = selecteIndex == 2 ? true : null;
    try {
      var response = await api.getCourseList(SearchCourseRequest(
              keyword: _searchController.text,
              location: location,
              pageNum: currentPage,
              pageSize: pageSzie,
              used: used,
              isFavorite: favorite,
              holes: selectedHoles == 'any' ? null : int.parse(selectedHoles),
              platformId: selectedPlatform?.id)
          .toListCourseRequest());

      setState(() {
        _isLoadingMore = false;
        paginator = response.paginator;
        currentPage = page;
        if (response.items.isNotEmpty) {
          // 需要滤重
          var newItems = response.items.where((element) {
            return !_searchResults.any((item) => item.id == element.id);
          }).toList();
          _searchResults.addAll(newItems);
        }
      });
    } catch (e) {
      showError(e as Exception, context);
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  Widget buildEmpty(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Image.asset('assets/imgs/plan_empty.png'), // 图片路径根据实际情况修改
          const SizedBox(height: 10),
          const Text(
            "no results",
            style: TextStyle(
                color: AppColors.greyColor,
                fontSize: 14,
                fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  Widget buildListView(BuildContext context) {
    if (_searchController.text.isNotEmpty && paginator.total == 0) {
      return buildEmpty(context);
    }

    return ListView.separated(
      physics: const AlwaysScrollableScrollPhysics(),
      controller: _scrollController,
      itemCount: _searchResults.length + 1, // 加1是为了在列表底部添加一个加载提示
      separatorBuilder: (BuildContext context, int index) =>
          const SizedBox(height: 2), // 在这里定义间距
      itemBuilder: (context, index) {
        if (index == _searchResults.length) {
          // 到达列表底部
          if (_searchResults.isNotEmpty && _isLoadingMore) {
            // 检查_data是否不为空
            return const Center(
                child: CircularProgressIndicator(
              color: AppColors.primaryColor,
            )); // 显示加载指示器
          } else if (paginator.total == 0) {
            return buildEmpty(context);
          } else {
            return const SizedBox(); // 不显示加载指示器
          }
        }
        SearchCourseItem course = _searchResults[index];
        return ListItem(
          course: course,
          isLoggedIn: isLoggedIn,
          locationOk: location != null,
        );
      },
    );
  }

  void _handleTabSelection(int index) async {
    setState(() {
      selecteIndex = index;
    });
    await getLocation();
    switch (index) {
      case 0:
        callnearby();
        break;
      case 1:
        if (!isLoggedIn) {
          // 跳转登录页
          Navigator.pushNamed(context, '/user/sign_in').then((result) {
            if (result == true) {
              api
                  .getCourseList(UsedCourseRequest(
                          page: 1,
                          size: pageSzie,
                          location: location,
                          holes: selectedHoles == 'any'
                              ? null
                              : int.parse(selectedHoles),
                          platformId: selectedPlatform?.id)
                      .toListCourseRequest())
                  .then((response) {
                setState(() {
                  _searchResults = response.items;
                  paginator = response.paginator;
                });
                // 回顶部
                _scrollController.jumpTo(0);
              });
            } else {
              setState(() {
                selecteIndex = 0; // 或者设置为其他默认的 Tab 索引
                _tabController.index = 0;
              });
            }
          });
          return;
        } else {
          api
              .getCourseList(UsedCourseRequest(
                      page: 1,
                      size: pageSzie,
                      location: location,
                      holes: selectedHoles == 'any'
                          ? null
                          : int.parse(selectedHoles),
                      platformId: selectedPlatform?.id)
                  .toListCourseRequest())
              .then((response) {
            setState(() {
              _searchResults = response.items;
              paginator = response.paginator;
            });
            // 回顶部
            _scrollController.jumpTo(0);
          });
        }

        break;
      case 2:
        if (!isLoggedIn) {
          // 跳转登录页
          Navigator.pushNamed(context, '/user/sign_in').then((result) {
            if (result == true) {
              api
                  .getCourseList(FavoriteCourseRequest(
                          page: 1,
                          size: pageSzie,
                          location: location,
                          holes: selectedHoles == 'any'
                              ? null
                              : int.parse(selectedHoles),
                          platformId: selectedPlatform?.id)
                      .toListCourseRequest())
                  .then((response) {
                setState(() {
                  _searchResults = response.items;
                  paginator = response.paginator;
                });
                // 回顶部
                _scrollController.jumpTo(0);
              });
            } else {
              setState(() {
                selecteIndex = 0; // 或者设置为其默认的 Tab 索引
                _tabController.index = 0;
              });
            }
          });
        } else {
          api
              .getCourseList(FavoriteCourseRequest(
                      page: 1,
                      size: pageSzie,
                      location: location,
                      holes: selectedHoles == 'any'
                          ? null
                          : int.parse(selectedHoles),
                      platformId: selectedPlatform?.id)
                  .toListCourseRequest())
              .then((response) {
            setState(() {
              _searchResults = response.items;
              paginator = response.paginator;
            });
            // 回顶部
            _scrollController.jumpTo(0);
          });
        }

        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕宽度
    final screenWidth = MediaQuery.of(context).size.width;
    // 计算文本高度
    final textPainter = TextPainter(
      text: TextSpan(
        text: locationError ?? '',
        style: const TextStyle(color: Colors.red, fontSize: 16),
      ),
      textDirection: TextDirection.ltr,
      maxLines: null,
    )..layout(maxWidth: screenWidth - 32); // 减去左右内边距
    final textHeight = textPainter.size.height + 10;
    var tabs = <Tab>[
      const Tab(text: 'Nearby'),
      const Tab(text: 'Played'),
      const Tab(text: 'Favorites')
    ];
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: <Widget>[
            Expanded(
              child: CupertinoTextField(
                key: _textFieldKey, // 将 GlobalKey 赋给 TextField
                controller: _searchController,
                focusNode: _focusNode,
                placeholder: 'Search course',
                onChanged: (value) => {
                  setState(() {
                    // 触发搜索操作
                    // _onSearchChanged(_searchController.text);
                  })
                },
                prefix: const Padding(
                  padding: EdgeInsets.fromLTRB(9.0, 6.0, 9.0, 6.0),
                  child: Icon(Icons.search),
                ),
                suffix: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_searchController.text.isNotEmpty)
                      SizedBox(
                        height: 36.0, // 设置高度
                        child: IconButton(
                          icon: const Icon(
                            Icons.clear,
                            color: Colors.grey,
                          ),
                          onPressed: () {
                            _searchController.clear(); // 清除文本框内容
                            _onSearchChanged(''); // 触发搜索变化
                            setState(() {}); // 刷新界面
                          },
                        ),
                      ),
                    SizedBox(
                      height: 36.0, // 设置 CupertinoTextField 相同的高度
                      child: TextButton(
                        style: TextButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(8.0), // 右上角圆角
                              bottomRight: Radius.circular(8.0), // 下角圆角
                            ),
                          ),
                        ),
                        child: const Text(
                          'Search',
                          style: TextStyle(
                            color: Colors.white, // 设置按钮文字颜色
                          ),
                        ),
                        onPressed: () {
                          // 触发搜索操作
                          _onSearchChanged(_searchController.text);
                        },
                      ),
                    ),
                  ],
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(
                    color: Colors.grey.withValues(alpha: 0.5),
                    width: 1.0,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 8, right: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            TabBar(
              controller: _tabController,
              tabs: tabs,
              onTap: _handleTabSelection,
            ),
            Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: DropdownButton<String>(
                      value: selectedHoles,
                      underline: Container(),
                      icon: const Icon(Icons.arrow_drop_down,
                          color: AppColors.primaryColor),
                      style: const TextStyle(
                        color: Colors.black87,
                        fontSize: 14,
                      ),
                      items: ['any', '9', '18'].map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text('$value holes'),
                        );
                      }).toList(),
                      onChanged: (String? newValue) {
                        setState(() {
                          selectedHoles = newValue!;
                          _updateSearchResults();
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: DropdownButton<String?>(
                      value: selectedPlatform?.id == ''
                          ? null
                          : selectedPlatform?.id,
                      hint: const Text('Platform'),
                      underline: Container(),
                      icon: const Icon(Icons.arrow_drop_down,
                          color: AppColors.primaryColor),
                      style: const TextStyle(
                        color: Colors.black87,
                        fontSize: 14,
                      ),
                      items: [
                        const DropdownMenuItem<String?>(
                          value: null,
                          child: Text('All Platforms'),
                        ),
                        ...platforms.map((Platform platform) {
                          return DropdownMenuItem<String?>(
                            value: platform.id,
                            child: Text(platform.name),
                          );
                        }),
                      ],
                      onChanged: (String? newValue) {
                        setState(() {
                          selectedPlatform = newValue == null
                              ? null
                              : platforms.firstWhereOrNull(
                                  (p) => p.id == newValue,
                                );
                          _updateSearchResults();
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: textHeight,
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Flexible(
                      child: Text(
                        locationError ?? '',
                        style: const TextStyle(color: Colors.red, fontSize: 16),
                        textAlign: TextAlign.center,
                        softWrap: true,
                        overflow: TextOverflow.visible,
                      ),
                    ),
                    if (locationError != null) ...[
                      const SizedBox(width: 8),
                      TextButton(
                        onPressed: () {
                          showDialog(
                            context: context,
                            builder: (BuildContext context) =>
                                const LocationGuideDialog(),
                          );
                        },
                        child: const Text(
                          'View Guide',
                          style: TextStyle(
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            Expanded(
              child: buildListView(context),
            ),
          ],
        ),
      ),
    );
  }

  void _updateSearchResults() {
    _saveSettings(); // 保存设置
    switch (selecteIndex) {
      case 0: // Nearby
        callnearby();
        break;
      case 1: // Used
        if (isLoggedIn) {
          api
              .getCourseList(UsedCourseRequest(
                      page: 1,
                      size: pageSzie,
                      location: location,
                      holes: selectedHoles == 'any'
                          ? null
                          : int.parse(selectedHoles),
                      platformId: selectedPlatform?.id)
                  .toListCourseRequest())
              .then((response) {
            setState(() {
              _searchResults = response.items;
              paginator = response.paginator;
              currentPage = 1;
            });
            _scrollController.jumpTo(0);
          });
        }
        break;
      case 2: // Favorites
        if (isLoggedIn) {
          api
              .getCourseList(FavoriteCourseRequest(
                      page: 1,
                      size: pageSzie,
                      location: location,
                      holes: selectedHoles == 'any'
                          ? null
                          : int.parse(selectedHoles),
                      platformId: selectedPlatform?.id)
                  .toListCourseRequest())
              .then((response) {
            setState(() {
              _searchResults = response.items;
              paginator = response.paginator;
              currentPage = 1;
            });
            _scrollController.jumpTo(0);
          });
        }
        break;
      default: // Search results
        if (_searchController.text.isNotEmpty) {
          _onSearchChanged(_searchController.text);
        }
    }
  }
}

class ListItem extends StatefulWidget {
  final SearchCourseItem course;
  final bool isLoggedIn;
  final bool locationOk;
  const ListItem(
      {super.key,
      required this.course,
      required this.isLoggedIn,
      required this.locationOk});

  @override
  _ListItemState createState() => _ListItemState();
}

class _ListItemState extends State<ListItem> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: () {
          Navigator.pop(context, widget.course); // 返回并携带选择的课程
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                height: 200, // 设置图片区域的高度
                child: Stack(
                  children: [
                    PageView.builder(
                      itemCount:
                          widget.course.images.length, // 假设每个课程有个 photos 列表
                      itemBuilder: (context, index) {
                        String imageUrl = widget.course.images[index];
                        return Image.network(
                          imageUrl,
                          width:
                              MediaQuery.of(context).size.width, // 设置图片的宽度为屏幕宽度
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(Icons.error);
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return const Center(
                                child: CircularProgressIndicator(
                                    color: AppColors.primaryColor));
                          },
                        );
                      },
                    ),
                    if (widget.isLoggedIn)
                      Positioned(
                        top: 10,
                        right: 10,
                        child: IconButton(
                          icon: Icon(
                            widget.course.isFavorite
                                ? Icons.favorite
                                : Icons.favorite_border,
                            color: widget.course.isFavorite
                                ? Colors.red
                                : Colors.white,
                          ),
                          onPressed: () {
// 收藏
                            var api =
                                Provider.of<APIBase>(context, listen: false);
                            api
                                .updateFavorite(
                                    widget.course.id, !widget.course.isFavorite)
                                .then((value) {
                              if (value) {
                                setState(() {
                                  widget.course.isFavorite =
                                      !widget.course.isFavorite;
                                });
                              }
                            });

                            // 这里可以添加逻辑来处理收藏状态的变化，例如更新数据库或发送网络请求
                          },
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 10), // 为图片和文本提供一些间隔
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('${widget.course.holes ?? ''} Holes'),
                  if (widget.course.platform?.name != null) // 添加平台名称
                    Text(
                      widget.course.platform!.name,
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                ],
              ),
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  widget.course.clubName,
                  style: const TextStyle(
                    fontSize: 16.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  widget.course.name,
                  style: const TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      '${widget.course.city}, ${widget.course.province}, ${widget.course.zip}',
                      style:
                          const TextStyle(fontSize: 16.0, color: Colors.grey),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  Text(
                    widget.locationOk
                        ? widget.course.distance ?? '-- miles'
                        : '-- miles',
                    style: const TextStyle(fontSize: 16.0, color: Colors.grey),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
