import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/screens/login_state.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/payment_req.dart';
import 'package:golf/utils/exception.dart';
import 'package:provider/provider.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:golf/services/provider/pagestate_provider.dart';
import 'package:golf/services/payment/payment_service.dart';

class PointStoreScreen extends StatefulWidget {
  const PointStoreScreen({super.key});

  @override
  _PointStoreScreenState createState() => _PointStoreScreenState();
}

class _PointStoreScreenState extends LoginRequiredState<PointStoreScreen> {
  bool _isLoading = false;
  int selectedPoints = 0;
  List<PaymentPackage> packages = [];
  final int pageSize = 20;
  int pointBalance = 0;
  bool _agreementChecked = false;
  late final PaymentService _paymentService;

  @override
  void initState() {
    super.initState();
    _paymentService = getPaymentService();
    _loadData();

    if (kIsWeb) {
      _paymentService.checkPaymentResult();
    }
  }

  Future<void> _loadData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final api = Provider.of<APIBase>(context, listen: false);

      // 获取用户详情，包含积分余额
      final userDetail = await api.getUserDetail();
      setState(() {
        pointBalance = int.parse(userDetail.points.toString());
      });

      // 获取积分包列表
      final response = await api
          .getPaymentPackages(PaymentPackageListRequest(page: 1, size: 100));

      setState(() {
        // 移除过滤条件，显示所有积分包
        packages = response.items;
        // 不自动选择第一个包，保持selectedPoints为0，用户必须主动选择
        AppLogger.info(
            'Retrieved ${packages.length} point packages', 'PAYMENT');
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showError(e as Exception, context);
    }
  }

  Future<void> _handleBuyPoints(PaymentPackage package) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final api = Provider.of<APIBase>(context, listen: false);
      AppLogger.info(
          'Creating payment order for package: ${package.id}', 'PAYMENT');

      // 创建支付订单
      final orderResponse = await api.createPaymentOrder(
        CreatePaymentOrderRequest(packageId: package.id),
      );
      AppLogger.info('Order created', 'PAYMENT');

      // 检查订单信息
      if (orderResponse.order.publicKey.isEmpty ||
          orderResponse.order.clientSecret.isEmpty) {
        AppLogger.error('Error: Invalid order information', 'PAYMENT');
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('Invalid payment information, please try again')),
        );
        return;
      }

      // 初始化支付服务
      await _paymentService.initialize(orderResponse.order.publicKey);

      // 处理支付
      final success = await _paymentService.handlePayment(
        clientSecret: orderResponse.order.clientSecret,
        publicKey: orderResponse.order.publicKey,
        order: orderResponse.order,
        onSuccess: () async {
          AppLogger.info('Payment successful', 'PAYMENT');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Payment successful!')),
          );
          await _loadData();
        },
        onError: (error) {
          AppLogger.error('Payment error: $error', 'PAYMENT');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Payment failed: $error')),
          );
        },
      );

      if (!success) {
        AppLogger.warning('Payment was not successful', 'PAYMENT');
      }
    } catch (e) {
      AppLogger.error('Error processing payment: $e', 'PAYMENT');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Payment processing error: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _paymentService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Points Recharge'),
        // backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () {
            // 设置抽屉状态为积分页，然后导航到首页
            Provider.of<PageStateNotifier>(context, listen: false)
                .updateState(PageState.points);
            Navigator.pushReplacementNamed(context, '/');
          },
        ),
      ),
      body: Stack(
        children: [
          Column(
            children: [
              // 顶部背景色和余额显示
              Container(
                color: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 20),
                width: double.infinity,
                child: Column(
                  children: [
                    Text(
                      '$pointBalance',
                      style: const TextStyle(
                        fontSize: 40,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      'Your Points Balance',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
              // 九宫格布局
              Expanded(
                child: Container(
                  color: AppColors.background,
                  child: packages.isEmpty && !_isLoading
                      ? const Center(
                          child: Text(
                            'No points packages available',
                            style: TextStyle(color: Colors.grey),
                          ),
                        )
                      : GridView.count(
                          crossAxisCount: 3,
                          padding: const EdgeInsets.all(16),
                          mainAxisSpacing: 16,
                          crossAxisSpacing: 16,
                          children: packages.map<Widget>((package) {
                            final isSelected = selectedPoints == package.points;
                            return InkWell(
                              onTap: () {
                                setState(() {
                                  selectedPoints = package.points;
                                });
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: isSelected
                                        ? AppColors.primaryColor
                                        : Colors.transparent,
                                    width: isSelected ? 2 : 0,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withValues(alpha: 0.2),
                                      spreadRadius: 1,
                                      blurRadius: 3,
                                      offset: const Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      '${package.points} points',
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black87,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      '\$${package.discountPrice.toStringAsFixed(2)}',
                                      style: const TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.primaryColor,
                                      ),
                                    ),
                                    if (package.discountRate < 1.0 &&
                                        package.originalPrice >
                                            package.discountPrice)
                                      Text(
                                        '\$${package.originalPrice.toStringAsFixed(2)}',
                                        style: const TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey,
                                          decoration:
                                              TextDecoration.lineThrough,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                ),
              ),
              // 底部说明和按钮
              Container(
                color: Colors.white,
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    const Text(
                      '——— Recharge Instructions ———',
                      style: TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 8),
                    const SizedBox(height: 16),
                    // 添加协议勾选
                    Row(
                      children: [
                        Checkbox(
                          value: _agreementChecked,
                          checkColor: AppColors.primaryColor,
                          onChanged: (value) {
                            setState(() {
                              _agreementChecked = value ?? false;
                            });
                          },
                        ),
                        const Text('I agree to the '),
                        TextButton(
                          onPressed: () {
                            // 显示协议内容
                            _showAgreementDialog(context);
                          },
                          child: const Text(
                            'Recharge Service Agreement',
                            style: TextStyle(
                              color: AppColors.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // 支付按钮
                    SizedBox(
                      width: double.infinity,
                      height: 50,
                      child: ElevatedButton(
                        onPressed: (_isLoading ||
                                packages.isEmpty ||
                                !_agreementChecked ||
                                selectedPoints == 0)
                            ? null
                            : () {
                                final selectedPackage = packages.firstWhere(
                                  (p) => p.points == selectedPoints,
                                  orElse: () => packages.first,
                                );
                                _handleBuyPoints(selectedPackage);
                              },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: packages.isEmpty
                            ? const Text(
                                'No packages available',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            : const Text(
                                'Pay Now',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withValues(alpha: 0.5),
              child: const Center(
                child: CircularProgressIndicator(
                  color: AppColors.primaryColor,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 显示协议对话框
  void _showAgreementDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.8,
            height: MediaQuery.of(context).size.height * 0.8,
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'Agreement',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ],
                ),
                const Divider(),
                const Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SectionTitle(title: '1. Parties'),
                        SectionContent(
                          content:
                              'This agreement is entered into between TeeTimeBot (hereinafter referred to as "the Application") and the user.',
                        ),
                        SectionTitle(title: '2. Service Content'),
                        SectionContent(
                          content:
                              '2.1 The Application provides golf course booking credit recharge services. Users can purchase credits for booking golf courses and paying related service fees.\n\n'
                              '2.2 Credits can only be used within the Application and cannot be transferred or converted to cash.\n\n'
                              '2.3 Specific credit usage rules and values are subject to actual display in the Application.',
                        ),
                        SectionTitle(title: '3. Recharge Rules'),
                        SectionContent(
                          content:
                              '3.1 Users can choose different credit packages for recharge.\n\n'
                              '3.2 Credits will be credited to the account immediately after successful payment.',
                        ),
                        SectionTitle(title: '4. Refund Policy'),
                        SectionContent(
                          content:
                              '4.1 Credits cannot be refunded once successfully recharged.\n\n'
                              '4.2 If recharge fails due to system errors, the payment will be returned through the original payment method.\n\n'
                              '4.3 The Application is not responsible for recharge errors caused by users themselves.',
                        ),
                        SectionTitle(title: '5. User Obligations'),
                        SectionContent(
                          content:
                              '5.1 Users shall properly maintain their account information. Users are responsible for any losses caused by account theft.\n\n'
                              '5.2 Users shall not use this service for any illegal activities.\n\n'
                              '5.3 Users shall ensure the legality of recharge funds.',
                        ),
                        SectionTitle(
                            title:
                                '6. Rights and Obligations of Service Provider'),
                        SectionContent(
                          content:
                              '6.1 The Application reserves the right to adjust credit prices and usage rules according to operational needs, with prior notice.\n\n'
                              '6.2 The Application commits to protecting users\' personal information and transaction security.\n\n'
                              '6.3 The Application reserves the right to take appropriate measures, including but not limited to suspending services or blocking accounts, if violations are found.',
                        ),
                        SectionTitle(title: '7. Disclaimer'),
                        SectionContent(
                          content:
                              '7.1 The Application is not liable for service interruptions caused by force majeure.\n\n'
                              '7.2 While the Application will make best efforts to ensure service security and stability, no guarantee is provided.',
                        ),
                        SectionTitle(title: '8. Agreement Modification'),
                        SectionContent(
                          content:
                              '8.1 The Application reserves the right to modify this agreement.\n\n'
                              '8.2 Modifications will be announced in the Application. Continued use of the service indicates acceptance of the modified agreement.',
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'I Understand',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

// 协议章节标题组件
class SectionTitle extends StatelessWidget {
  final String title;

  const SectionTitle({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

// 协议章节内容组件
class SectionContent extends StatelessWidget {
  final String content;

  const SectionContent({super.key, required this.content});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 8),
      child: Text(
        content,
        style: const TextStyle(
          fontSize: 16,
          height: 1.5,
        ),
      ),
    );
  }
}
