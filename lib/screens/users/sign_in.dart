// ignore_for_file: use_build_context_synchronously, deprecated_member_use

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/provider/pagestate_provider.dart';
import 'package:golf/services/req/auth_req.dart';

import 'package:golf/services/req/req.dart';
import 'package:golf/utils/exception.dart';
import 'package:golf/utils/validator.dart';
import 'package:golf/widgets/golf_dialog.dart';
import 'package:provider/provider.dart';
import '../../services/exceptions.dart';
import 'package:flutter/services.dart' show TextInput;

class SignInScreen extends StatelessWidget {
  const SignInScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sign In'),
      ),
      backgroundColor: Colors.white,
      body: const Padding(
        padding: EdgeInsets.all(8.0),
        child: SignInForm(),
      ),
    );
  }
}

class SignInForm extends StatefulWidget {
  const SignInForm({super.key});

  @override
  State<SignInForm> createState() => _SignInFormState();
}

class _SignInFormState extends State<SignInForm> {
  final _usernameTextController = TextEditingController();
  final _passwordTextController = TextEditingController();
  String? _formError;

  bool _obscureText = true;

  @override
  void initState() {
    super.initState();
    AppLogger.info('SignInForm initState', 'AUTH');
  }

  @override
  void dispose() {
    // Clean up the controller when the widget is disposed.
    _usernameTextController.dispose();
    _passwordTextController.dispose();
    AppLogger.info('SignInForm dispose', 'AUTH');
    super.dispose();
  }

  void handleLogin() async {
    String email = _usernameTextController.text;
    String password = _passwordTextController.text;
    if (email.isEmpty) {
      setState(() {
        _formError = 'Email is required';
      });
      return;
    } else if (!isValidEmail(email)) {
      setState(() {
        _formError = 'Email format is incorrect';
      });
      return;
    }
    if (password.isEmpty) {
      setState(() {
        _formError = 'password is required';
      });
      return;
    } else if (!isValidPassword(password)) {
      setState(() {
        _formError = passwordFormatError;
      });
      return;
    }
    try {
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);

      showLoadingDialog(context);

      await api.login(LoginRequest(email: email, password: password));
      TextInput.finishAutofillContext(shouldSave: true);
      // go back
      if (mounted) {
        Navigator.pop(context); // hide loading
        Navigator.pop(context, true); // navi back
      }
    } catch (e) {
      Navigator.pop(context); // hide loading
      if (e is ApiException) {
        switch (ResponseCodeExtension.fromInt(e.code)) {
          case ResponseCode.emailNotExisted:
            setState(() {
              _formError = e.message;
            });
            break;
          default:
            showError(e, context);
        }
      } else {
        showError(e as Exception, context);
      }
    }
  }

  bool canSubmit() {
    return _usernameTextController.text.isNotEmpty &&
        _passwordTextController.text.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        context.read<PageStateNotifier>().updateState(PageState.home);
        return true;
      },
      child: AutofillGroup(
        child: Form(
          child: Column(
            children: [
              const SizedBox(height: AppPadding.medium),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    "assets/imgs/logo.png",
                    width: 40, // 设置 logo 宽度
                    height: 40, // 设置 logo 高度
                    fit: BoxFit.contain, // 保持宽高比
                  ),
                  const SizedBox(width: 8), // 添加间距
                  const Text(
                    "TeeTimeBot",
                    style: TextStyle(
                        fontSize: 20.0,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor),
                  ),
                ],
              ),
              const Text(
                "Book your favorite tee times with TeeTimeBot",
                style: TextStyle(
                    fontSize: 14.0, // Set the font size here
                    color: AppColors.greyColor),
              ),
              const SizedBox(height: AppPadding.large),
              // const Text(
              //   'Sign in',
              //   style: TextStyle(
              //       fontSize: 28.0, // Set the font size here
              //       fontWeight: FontWeight.bold),
              // ),
              // const SizedBox(height: AppPadding.small),
              if (_formError != null)
                Text(
                  _formError!,
                  style: const TextStyle(
                    color: Colors.red,
                  ),
                ),
              SizedBox(
                height: AppHeight.input,
                child: CupertinoTextField(
                  controller: _usernameTextController,
                  placeholder: 'Your email',
                  clearButtonMode: OverlayVisibilityMode.editing,
                  autocorrect: false,
                  keyboardType: TextInputType.emailAddress,
                  textInputAction: TextInputAction.next,
                  autofillHints: const [
                    AutofillHints.username,
                    AutofillHints.email
                  ],
                  onEditingComplete: () {
                    // 删除这行，让自动填充完整完成
                    // TextInput.finishAutofillContext(),
                  },
                  onChanged: (value) {
                    setState(() {});
                  },
                ),
              ),
              const SizedBox(height: AppPadding.medium),
              SizedBox(
                height: AppHeight.input,
                child: CupertinoTextField(
                  controller: _passwordTextController,
                  placeholder: 'Password',
                  obscureText: _obscureText,
                  clearButtonMode: OverlayVisibilityMode.editing,
                  autocorrect: false,
                  textInputAction: TextInputAction.done,
                  autofillHints: const [AutofillHints.password],
                  onEditingComplete: () {
                    TextInput.finishAutofillContext(shouldSave: true);
                    handleLogin();
                  },
                  onChanged: (value) {
                    setState(() {
                      _formError =
                          value.isEmpty ? 'Password is required' : null;
                    });
                  },
                  suffix: GestureDetector(
                    onTap: () {
                      setState(() {
                        _obscureText =
                            !_obscureText; // Toggle the state of the _obscureText variable
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(
                          right:
                              5.0), // Add some padding to the right of the icon
                      child: Icon(
                        _obscureText
                            ? CupertinoIcons.eye
                            : CupertinoIcons.eye_slash,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: AppPadding.medium),
              SizedBox(
                width: double
                    .infinity, // Set the button width to 95% of the screen width
                child: CupertinoButton(
                  color: AppColors.primaryColor,
                  disabledColor: AppColors.disabledColor,
                  onPressed: canSubmit() ? handleLogin : null,
                  child: const Text(
                    'Sign In',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: AppPadding.large),
              // Terms & Conditions 和 Privacy Policy 居中显示
              Center(
                child: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    style: DefaultTextStyle.of(context).style,
                    children: <TextSpan>[
                      TextSpan(
                        text: 'Terms & Conditions',
                        style: const TextStyle(
                          color: AppColors.primaryColor,
                          fontSize: AppFontSizes.small,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Navigator.pushNamed(
                                context, '/terms_and_conditions');
                          },
                      ),
                      const TextSpan(
                        text: ' and ',
                        style: TextStyle(
                          color: AppColors.greyColor,
                          fontSize: AppFontSizes.small,
                        ),
                      ),
                      TextSpan(
                        text: 'Privacy Policy',
                        style: const TextStyle(
                          color: AppColors.primaryColor,
                          fontSize: AppFontSizes.small,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            Navigator.pushNamed(context, '/privacy_policy');
                          },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: AppPadding.large),
              // 底部链接区域
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  RichText(
                    text: TextSpan(
                      text: 'Need an account? ',
                      style: const TextStyle(
                        color: AppColors.greyColor,
                        fontSize: 16,
                      ),
                      children: <TextSpan>[
                        TextSpan(
                          text: 'Sign Up',
                          style: const TextStyle(
                            color: AppColors.primaryColor,
                            fontSize: 16,
                          ),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              Navigator.pushNamed(context, '/user/sign_up');
                            },
                        ),
                      ],
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.pushNamed(context, '/user/forgot_password');
                    },
                    child: const Text(
                      'Forgot password',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
