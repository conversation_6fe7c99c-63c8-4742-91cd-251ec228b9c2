// ignore_for_file: library_private_types_in_public_api, camel_case_types, use_build_context_synchronously

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

import 'package:golf/config.dart';
import 'package:golf/services/provider/base.dart';

import 'package:golf/services/req/auth_req.dart';
import 'package:golf/services/req/email_req.dart';
import 'package:golf/utils/exception.dart';
import 'package:golf/utils/validator.dart';
import 'package:golf/widgets/golf_appbar.dart';
import 'package:golf/widgets/golf_dialog.dart';
import 'package:golf/widgets/golf_toast.dart';
import 'package:provider/provider.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  _ForgotPasswordScreenState createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final PageController _pageController = PageController();
  final _formKey = GlobalKey<FormState>();
  final _emailTextController = TextEditingController();
  final _codeTextController = TextEditingController();
  int _countdownTime = 0;
  Timer? _timer;

  String? _emailError;
  String? _codeError;
  String? token;
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  String? _passwordError;
  String? _confirmPasswordError;
  bool _newPasswordObscureText = true;
  bool _confirmPasswordObscureText = true;

  void handleSendEmail() async {
    if (_countdownTime > 0) {
      return;
    }

    String email = _emailTextController.text;

    if (email.isEmpty) {
      setState(() {
        _emailError = 'Email is required';
      });
      return;
    } else {
      if (!isValidEmail(email)) {
        setState(() {
          _emailError = 'Email format is incorrect';
        });
        return;
      }
    }

    try {
      showLoadingDialog(context);
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);
      var exist = await api.checkEmail(CheckEmailRequest(email: email));
      if (!exist) {
        setState(() {
          _emailError = 'Email not exists, please use another one';
        });
        return;
      }
      SendEmailResponse response = await api
          .sendEmail(SendEmailRequest(email: email, type: EmailType.reset));
      token = response.data.token;
      _startCountdown();
      _nextPage();
    } catch (e) {
      showError(e as Exception, context);
    } finally {
      Navigator.pop(context); // Close the dialog
    }
  }

  void handleResetPassword() async {
    String password = _passwordController.text;
    String password2 = _confirmPasswordController.text;
    String code = _codeTextController.text;
    String email = _emailTextController.text;

    if (password.isEmpty) {
      setState(() {
        _passwordError = 'Password is required';
      });
      return;
    } else {
      if (!isValidPassword(password)) {
        setState(() {
          _passwordError = passwordFormatError;
        });
        return;
      }
    }
    if (password2 != password) {
      setState(() {
        _confirmPasswordError = 'Password does not match';
      });
      return;
    }

    try {
      showLoadingDialog(context);
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);
      await api.resetPassword(ResetPasswordRequest(
          password: password, token: token!, code: code, email: email));
      Navigator.pop(context); // Close the dialog
      showToast(
        context,
        msg: "password reset successfully!",
      );
      Future.delayed(const Duration(seconds: 2)).then((_) {
        Navigator.pushNamedAndRemoveUntil(
            context,
            '/',
            (route) =>
                false); // Navigate to home page and remove all previous routes
      });
    } catch (e) {
      Navigator.pop(context); // Close the dialog
      showError(e as Exception, context);
    }
  }

  void handleVerifyEmail() async {
    String email = _emailTextController.text;
    String code = _codeTextController.text;

    if (email.isEmpty) {
      setState(() {
        _emailError = 'Email is required';
      });
      return;
    } else {
      if (!isValidEmail(email)) {
        setState(() {
          _emailError = 'Email format is incorrect';
        });
        return;
      }
    }
    if (code.isEmpty) {
      setState(() {
        _codeError = 'Code is required';
      });
      return;
    }
    if (token == null) {
      setState(() {
        _codeError = 'Click send to get email code';
      });
      return;
    }
    _nextPage();
  }

  Widget buildError(BuildContext context, String text) {
    return Align(
      alignment: Alignment.topLeft,
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.red,
          fontSize: 12,
        ),
      ),
    );
  }

  void _startCountdown() {
    setState(() {
      _countdownTime = 60;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdownTime < 1) {
          _timer?.cancel();
        } else {
          _countdownTime -= 1;
        }
      });
    });
  }

  bool _canGoBack = true;
  _onPopInvoked(bool didPop) async {
    if (_canGoBack) {
      return;
    }
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _nextPage() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeIn,
    );
  }

  @override
  void initState() {
    super.initState();
    // 监听页面变化
    _pageController.addListener(() {
      final page = _pageController.page ?? 0.0;
      setState(() {
        _canGoBack = page == 0;
      });
    });
  }

  @override
  void dispose() {
    _emailTextController.dispose();
    _codeTextController.dispose();
    _passwordController.dispose();
    _pageController.dispose();
    _confirmPasswordController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  Widget buildChangeForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const SizedBox(height: 8.0),
        SizedBox(
          height: AppHeight.input, // Set the height of the text field
          child: CupertinoTextField(
            placeholder: 'Password: a-z, 0-9, 8-16 digits',
            controller: _passwordController,
            obscureText: _newPasswordObscureText,
            clearButtonMode: OverlayVisibilityMode.editing,
            suffix: GestureDetector(
              onTap: () {
                setState(() {
                  _newPasswordObscureText =
                      !_newPasswordObscureText; // Toggle the state of the _obscureText variable
                });
              },
              child: Padding(
                padding: const EdgeInsets.only(
                    right: 5.0), // Add some padding to the right of the icon
                child: Icon(
                  _newPasswordObscureText
                      ? CupertinoIcons.eye
                      : CupertinoIcons.eye_slash,
                ),
              ),
            ),
            onChanged: (value) {
              String? newError;
              if (value.isEmpty) {
                newError = 'Password is required';
              } else if (!isValidPassword(value)) {
                newError = passwordFormatError;
              }
              setState(() {
                _passwordError = newError;
              });
            },
          ),
        ),
        if (_passwordError != null) buildError(context, _passwordError!),
        const SizedBox(height: 8.0),
        SizedBox(
          height: AppHeight.input, // Set the height of the text field

          child: CupertinoTextField(
            placeholder: 'Password again',
            controller: _confirmPasswordController,
            obscureText: _confirmPasswordObscureText,
            clearButtonMode: OverlayVisibilityMode.editing,
            suffix: GestureDetector(
              onTap: () {
                setState(() {
                  _confirmPasswordObscureText =
                      !_confirmPasswordObscureText; // Toggle the state of the _obscureText variable
                });
              },
              child: Padding(
                padding: const EdgeInsets.only(
                    right: 5.0), // Add some padding to the right of the icon
                child: Icon(
                  _confirmPasswordObscureText
                      ? CupertinoIcons.eye
                      : CupertinoIcons.eye_slash,
                ),
              ),
            ),
            onChanged: (value) {
              String? newError;
              if (value.isEmpty) {
                newError = 'Enter new password again';
              } else if (value != _passwordController.text) {
                newError = 'Password does not match';
              }
              setState(() {
                _confirmPasswordError = newError;
              });
            },
          ),
        ),
        if (_confirmPasswordError != null)
          buildError(context, _confirmPasswordError!),
        const SizedBox(height: 16.0),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryColor, // Set the background color
            shape: RoundedRectangleBorder(
              borderRadius:
                  BorderRadius.circular(5.0), // Set the border radius to 5
            ),
            minimumSize:
                const Size(double.infinity, 50), // Set the width to 100%
          ),
          onPressed: (_passwordError != null ||
                  _confirmPasswordError != null ||
                  _passwordController.text.isEmpty ||
                  _confirmPasswordController.text.isEmpty)
              ? null
              : () {
                  handleResetPassword();
                },
          child: const Text(
            'Submit',
            style: TextStyle(color: Colors.white),
          ),
        ),
      ],
    );
  }

  Widget buildVerifyForm(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const SizedBox(height: 8.0),
          SizedBox(
            height: AppHeight.input, // Set the height of the text field
            child: CupertinoTextField(
              placeholder: 'Your email',
              controller: _emailTextController,
              readOnly: true,
            ),
          ),
          if (_emailError != null) buildError(context, _emailError!),
          const SizedBox(height: 8.0),
          Row(
            children: <Widget>[
              Expanded(
                child: SizedBox(
                  height: AppHeight.input, // Set the height of the text field

                  child: CupertinoTextField(
                    placeholder: 'Code',
                    controller: _codeTextController,
                    onChanged: (value) {
                      String? newError;
                      if (value.isEmpty) {
                        newError = 'Code is required';
                      }
                      setState(() {
                        _codeError = newError;
                      });
                    },
                  ),
                ),
              ),
            ],
          ),
          if (_codeError != null) buildError(context, _codeError!),
          const SizedBox(height: 16.0),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  AppColors.primaryColor, // Set the background color
              shape: RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(5.0), // Set the border radius to 5
              ),
              minimumSize:
                  const Size(double.infinity, 50), // Set the width to 100%
            ),
            onPressed: (_emailError != null ||
                    _codeError != null ||
                    _emailTextController.text.isEmpty ||
                    _codeTextController.text.isEmpty)
                ? null
                : () {
                    handleVerifyEmail();
                  },
            child: const Text(
              'Submit',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildSendForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const SizedBox(height: 8.0),
        SizedBox(
          height: AppHeight.input, // Set the height of the text field
          child: CupertinoTextField(
            placeholder: 'Your email',
            controller: _emailTextController,
            onChanged: (value) {
              String? newError;
              if (value.isEmpty) {
                newError = 'Email is required';
              } else if (!isValidEmail(value)) {
                newError = 'Email format is incorrect';
              }
              setState(() {
                _emailError = newError;
              });
            },
          ),
        ),
        if (_emailError != null) buildError(context, _emailError!),
        const Spacer(),
        SizedBox(
          width: MediaQuery.of(context).size.width,
          child: CupertinoButton(
            color: AppColors.primaryColor,
            onPressed: (_emailError != null ||
                    _countdownTime > 0 ||
                    _emailTextController.text.isEmpty)
                ? null
                : handleSendEmail,
            child: Text(
              _countdownTime > 0 ? '$_countdownTime s' : 'Send',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _canGoBack,
      onPopInvoked: _onPopInvoked,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: const GolfAppBar(
          title: 'Reset Password',
        ),
        body: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Container(
            color: Colors.white, // Set the background color to white
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Reset Password',
                    style:
                        TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  const Divider(
                    color: AppColors.primaryColor,
                    height: 1,
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: PageView(
                      controller: _pageController,
                      children: <Widget>[
                        buildSendForm(context),
                        buildVerifyForm(context),
                        buildChangeForm(context),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
