import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import 'package:golf/screens/login_state.dart';
import 'package:golf/screens/users/plan_log_list.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/booking_req.dart';
import 'package:golf/widgets/golf_appbar.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

class PlanDetailScreen extends StatefulWidget {
  final String planId;
  const PlanDetailScreen({super.key, required this.planId});

  @override
  _PlanDetailScreenState createState() => _PlanDetailScreenState();
}

class _PlanDetailScreenState extends LoginRequiredState<PlanDetailScreen> {
  late APIBase api;

  @override
  void initState() {
    super.initState();
    api = Provider.of<APIBase>(context, listen: false);
  }

  Widget _buildInfoCard({
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
            const Divider(height: 24),
            ...children,
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(BookingStatus status) {
    switch (status) {
      case BookingStatus.success:
        return const Color(0xFF078B54); // 绿色
      case BookingStatus.pending:
        return Colors.blue;
      case BookingStatus.failed:
        return const Color(0xFF666666); // 灰色
      default:
        return Colors.black;
    }
  }

  Widget _buildInfoRow(String label, String? value,
      {VoidCallback? onTap, Color? textColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120, // 固定标签宽度
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: onTap != null
                ? GestureDetector(
                    onTap: onTap,
                    child: Text(
                      value ?? 'N/A',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppColors.primaryColor,
                        decoration: TextDecoration.underline,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  )
                : Text(
                    value ?? 'N/A',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: textColor,
                    ),
                    textAlign: TextAlign.right,
                  ),
          ),
        ],
      ),
    );
  }

  String _getStatusText(BookingStatus status) {
    switch (status) {
      case BookingStatus.success:
        return 'Successful';
      case BookingStatus.pending:
        return 'Processing';
      case BookingStatus.failed:
        return 'Failed';
      default:
        return 'Unknown';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const GolfAppBar(
        title: 'Plan Detail',
      ),
      body: FutureBuilder<BookingDetailResponse>(
        future: api.getPlanDetail(BookingDetailRequest(id: widget.planId)),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
              child: CircularProgressIndicator(
                color: AppColors.primaryColor,
              ),
            );
          }

          if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, size: 48, color: Colors.red),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${snapshot.error}',
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          if (!snapshot.hasData) {
            return const Center(
              child: Text('No data available'),
            );
          }

          final plan = snapshot.data!.item;
          return SingleChildScrollView(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              color: Colors.white,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 20),
                    _buildInfoCard(
                      title: 'Course Information',
                      children: [
                        _buildInfoRow('Course Name', plan.courseName),
                        _buildInfoRow('Date', plan.reservationDate),
                        _buildInfoRow(
                            'Time',
                            plan.teetime?.isNotEmpty == true
                                ? plan.teetime
                                : '${plan.timeTange.start} - ${plan.timeTange.end}'),
                        _buildInfoRow('Players', '${plan.players} Players'),
                      ],
                    ),
                    _buildInfoCard(
                      title: 'Booking Details',
                      children: [
                        _buildInfoRow(
                          'Status',
                          _getStatusText(plan.status),
                          textColor: _getStatusColor(plan.status),
                        ),
                        // 在失败或处理中状态时显示 lastlog 信息
                        if (plan.lastlog != null) ...[
                          _buildInfoRow(
                            'Last Message',
                            plan.lastlog!.message,
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PlanLogListScreen(
                                    planId: plan.id,
                                  ),
                                ),
                              );
                            },
                          ),
                          if (plan.lastlog!.responseTeetime != null)
                            _buildInfoRow(
                              'Response Tee Time',
                              plan.lastlog!.responseTeetime,
                            ),
                          if (plan.lastlog!.responseHotdeal != null)
                            _buildInfoRow(
                              'Response Hot Deal',
                              plan.lastlog!.responseHotdeal,
                            ),
                          if (plan.lastlog!.responsePrice.isNotEmpty)
                            _buildInfoRow(
                              'Response Price',
                              plan.lastlog!.responsePrice,
                            ),
                          _buildInfoRow(
                            'Last Execution',
                            plan.lastlog!.insertTime,
                          ),
                        ],
                        if (plan.platform.id != '4')
                          _buildInfoRow('Price', plan.priceName),
                        if (plan.platform.id == '4') ...[
                          _buildInfoRow(
                              'Hot Deals Only', plan.hotOnly ? 'Yes' : 'No'),
                          _buildInfoRow(
                              'Maximum Price', plan.maxPrice?.toString()),
                          _buildInfoRow(
                              'End time', '${plan.expiredHours} hours prior'),
                        ],
                        _buildInfoRow('Create Time', plan.insertTime),
                        _buildInfoRow('Last Update', plan.updateTime),
                      ],
                    ),
                    if (plan.coursePhone != null || plan.courseWebsite != null)
                      _buildInfoCard(
                        title: 'Contact Information',
                        children: [
                          if (plan.coursePhone != null)
                            _buildInfoRow(
                              'Phone',
                              plan.coursePhone,
                              onTap: () {
                                final Uri telLaunchUri = Uri(
                                  scheme: 'tel',
                                  path: plan.coursePhone,
                                );
                                launchUrl(telLaunchUri);
                              },
                            ),
                          if (plan.courseWebsite != null)
                            _buildInfoRow(
                              'Website',
                              plan.courseWebsite,
                              onTap: () {
                                launchUrl(Uri.parse(plan.courseWebsite!));
                              },
                            ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
