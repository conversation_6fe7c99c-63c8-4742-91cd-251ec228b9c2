// ignore_for_file: use_build_context_synchronously

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import 'package:golf/services/provider/base.dart';

import 'package:golf/services/req/auth_req.dart';
import 'package:golf/services/req/email_req.dart';
import 'package:golf/services/req/req.dart';
import 'package:golf/utils/exception.dart';
import 'package:golf/utils/validator.dart';
import 'package:golf/widgets/golf_dialog.dart';
import 'package:golf/widgets/golf_toast.dart';
import 'package:provider/provider.dart';
import '../../services/exceptions.dart';

class SignUpScreen extends StatelessWidget {
  final String? invitor;
  const SignUpScreen({
    super.key,
    this.invitor,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sign Up'),
      ),
      backgroundColor: Colors.white,
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SignUpForm(invitor: invitor ?? ''),
      ),
    );
  }
}

class SignUpForm extends StatefulWidget {
  final String invitor;
  const SignUpForm({super.key, required this.invitor});

  @override
  State<SignUpForm> createState() => _SignUpFormState();
}

class _SignUpFormState extends State<SignUpForm> {
  final PageController _pageController = PageController();
  final _emailTextController = TextEditingController();
  final _passwordTextController = TextEditingController();
  final _codeTextController = TextEditingController();

  // 使用 ValueNotifier 管理错误状态
  final _emailError = ValueNotifier<String?>(null);
  final _passwordError = ValueNotifier<String?>(null);
  final _codeError = ValueNotifier<String?>(null);

  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _codeFocusNode = FocusNode();

  final _obscureText = ValueNotifier<bool>(true);
  String? token;

  bool _canGoBack = true;
  _onPopInvoked(bool didPop) async {
    if (_canGoBack) {
      return;
    }
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _nextPage() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeIn,
    );
  }

  @override
  void initState() {
    super.initState();
    // 监听页面变化
    _pageController.addListener(() {
      final page = _pageController.page ?? 0.0;
      setState(() {
        _canGoBack = page == 0;
      });
    });

    // 添加文本控制器监听
    _emailTextController.addListener(_validateEmail);
    _passwordTextController.addListener(_validatePassword);
    _codeTextController.addListener(_validateCode);
  }

  void _validateEmail() {
    String value = _emailTextController.text;
    String? newError;
    if (value.isEmpty) {
      newError = 'Email is required';
    } else if (!isValidEmail(value)) {
      newError = 'Email format is incorrect';
    }
    _emailError.value = newError;
  }

  void _validatePassword() {
    String value = _passwordTextController.text;
    String? newError;
    if (value.isEmpty) {
      newError = 'Set your password';
    } else if (!isValidPassword(value)) {
      newError = 'Password format is incorrect';
    }
    _passwordError.value = newError;
  }

  void _validateCode() {
    String value = _codeTextController.text;
    String? newError;
    if (value.isEmpty) {
      newError = 'Code is required';
    }
    _codeError.value = newError;
  }

  @override
  void dispose() {
    _emailTextController.removeListener(_validateEmail);
    _passwordTextController.removeListener(_validatePassword);
    _codeTextController.removeListener(_validateCode);

    _emailTextController.dispose();
    _passwordTextController.dispose();
    _codeTextController.dispose();

    _emailFocusNode.dispose();

    _emailError.dispose();
    _passwordError.dispose();
    _codeError.dispose();
    _obscureText.dispose();

    super.dispose();
  }

  void handleRegister() async {
    try {
      String email = _emailTextController.text;
      String password = _passwordTextController.text;
      String code = _codeTextController.text;
      if (email.isEmpty) {
        setState(() {
          _emailError.value = 'Email is required';
        });
        return;
      } else if (!isValidEmail(email)) {
        setState(() {
          _emailError.value = 'Email format is incorrect';
        });
        return;
      }
      if (password.isEmpty) {
        setState(() {
          _passwordError.value = 'Password is required';
        });
        return;
      } else {
        if (!isValidPassword(password)) {
          setState(() {
            _passwordError.value = passwordFormatError;
          });
          return;
        }
      }
      if (code.isEmpty) {
        setState(() {
          _codeError.value = 'Code is required';
        });
        return;
      }
      if (token == null) {
        setState(() {
          _codeError.value = 'Please send email first';
        });
        return;
      }

      showLoadingDialog(context);
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);

      await api.register(RegisterRequest(
          email: email,
          password: password,
          code: code,
          token: token!,
          invitor: widget.invitor.isNotEmpty
              ? (widget.invitor.contains('-')
                  ? widget.invitor.split('-')[0]
                  : widget.invitor)
              : ''));
      Navigator.pop(context);

      Navigator.pushNamedAndRemoveUntil(
          context,
          '/',
          (route) =>
              false); // Navigate to home page and remove all previous routes
    } catch (e) {
      Navigator.pop(context); // Close the dialog
      if (e is ApiException) {
        // Handle API error
        showToast(
          context,
          msg: e.message,
        );
      } else {
        // Handle other errors
        showToast(
          context,
          // ignore: prefer_interpolation_to_compose_strings
          msg: "Unexpected error: " + e.toString(),
        );
      }
    }
  }

  void handleSendEmail() async {
    try {
      String email = _emailTextController.text;

      if (email.isEmpty) {
        setState(() {
          _emailError.value = 'Email is required';
        });
        return;
      } else {
        if (!isValidEmail(email)) {
          setState(() {
            _emailError.value = 'Email format is incorrect';
          });
          return;
        }
      }
      showLoadingDialog(context);
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);
      // first check if email exists
      var exist = await api.checkEmail(CheckEmailRequest(email: email));
      if (exist) {
        Navigator.pop(context);
        setState(() {
          _emailError.value = 'Email already exists';
        });
        return;
      }

      SendEmailResponse response = await api
          .sendEmail(SendEmailRequest(email: email, type: EmailType.register));
      token = response.data.token;

      Navigator.pop(context);
      _nextPage();
    } catch (e) {
      Navigator.pop(context);
      if (e is ApiException) {
        // Handle API error
        switch (ResponseCodeExtension.fromInt(e.code)) {
          case ResponseCode.emailExisted:
            setState(() {
              // _emailError = 'Email already exists';
              _emailError.value = e.message;
            });
          default:
            showError(e, context);
        }
      } else {
        // Handle other errors
        showError(e as Exception, context);
      }
    }
  }

  Widget buildError(BuildContext context, String text) {
    return Align(
      alignment: Alignment.topLeft,
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.red,
          fontSize: 12,
        ),
      ),
    );
  }

  bool canSubmit() {
    return (_emailError.value == null &&
        _passwordError.value == null &&
        _codeError.value == null &&
        _emailTextController.text.isNotEmpty &&
        _passwordTextController.text.isNotEmpty &&
        _codeTextController.text.isNotEmpty);
  }

  Widget buildPassword(BuildContext context) {
    return ValueListenableBuilder<String?>(
      valueListenable: _passwordError,
      builder: (context, error, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ValueListenableBuilder<bool>(
              valueListenable: _obscureText,
              builder: (context, obscure, __) {
                return CupertinoTextField(
                  key: const ValueKey('password_field'),
                  controller: _passwordTextController,
                  focusNode: _passwordFocusNode,
                  placeholder: 'Password: a-z, 0-9, 8-16 digits',
                  obscureText: obscure,
                  clearButtonMode: OverlayVisibilityMode.editing,
                  suffix: GestureDetector(
                    onTap: () => _obscureText.value = !_obscureText.value,
                    child: Padding(
                      padding: const EdgeInsets.only(right: 5.0),
                      child: Icon(
                        obscure ? CupertinoIcons.eye : CupertinoIcons.eye_slash,
                      ),
                    ),
                  ),
                );
              },
            ),
            if (error != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: buildError(context, error),
              ),
          ],
        );
      },
    );
  }

  Widget buildEmailInput() {
    return ValueListenableBuilder<String?>(
      valueListenable: _emailError,
      builder: (context, error, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CupertinoTextField(
              controller: _emailTextController,
              focusNode: _emailFocusNode,
              placeholder: 'Enter your email',
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
              ),
              clearButtonMode: OverlayVisibilityMode.editing,
            ),
            if (error != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: buildError(context, error),
              ),
          ],
        );
      },
    );
  }

  final _emailFormKey = GlobalKey<FormState>();
  Widget buildEmailForm(BuildContext context) {
    return Form(
      key: _emailFormKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: AppPadding.large),
          const Text(
            "Welcome to TeeTimeBot!",
            style: TextStyle(
              fontSize: 20.0,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
          const SizedBox(height: AppPadding.large),
          if (widget.invitor.isNotEmpty) ...[
            // 有邀请人时显示的文案
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.blue.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(
                        Icons.celebration,
                        color: Colors.blue,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          "You've been invited by ${widget.invitor.contains('-') ? widget.invitor.split('-')[1] : widget.invitor}!",
                          style: const TextStyle(
                            fontSize: 14.0,
                            fontWeight: FontWeight.w600,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    "Join TeeTimeBot today and claim your \$25 credit!",
                    style: TextStyle(
                      fontSize: 16.0,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    "Start automating your tee time bookings and enjoy more time on the course.",
                    style: TextStyle(
                      fontSize: 14.0,
                      color: Colors.black54,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            const Text(
              "Start booking tee times automatically with TeeTimeBot",
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14.0,
                color: Color(0xFF333333),
                height: 1.5,
              ),
            ),
          ],
          const SizedBox(height: AppPadding.large),
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Email Address',
                  style: TextStyle(
                    fontSize: 16.0,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                buildEmailInput(),
              ],
            ),
          ),
          if (widget.invitor.isNotEmpty) ...[
            const SizedBox(height: AppPadding.medium),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.green.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.card_giftcard,
                    color: Colors.green,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      "\$25 credit will be applied after registration",
                      style: TextStyle(
                        fontSize: 14.0,
                        color: Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: AppPadding.medium),
          SizedBox(
            width: MediaQuery.of(context).size.width,
            height: 48,
            child: ValueListenableBuilder<String?>(
              valueListenable: _emailError,
              builder: (context, error, _) {
                return CupertinoButton(
                  padding: EdgeInsets.zero,
                  color: AppColors.primaryColor,
                  disabledColor: AppColors.disabledColor,
                  borderRadius: BorderRadius.circular(8),
                  onPressed:
                      (error != null || _emailTextController.text.isEmpty)
                          ? null
                          : () {
                              handleSendEmail();
                            },
                  child: const Text(
                    'Get Started',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: AppPadding.medium),
          RichText(
            text: TextSpan(
              style: DefaultTextStyle.of(context).style,
              children: <TextSpan>[
                TextSpan(
                  text: 'Terms & Conditions',
                  style: const TextStyle(color: AppColors.primaryColor),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      // 在这里处理链接的点击事件，例如导航到条款和条件页面
                      Navigator.pushNamed(context, '/terms_and_conditions');
                    },
                ),
                const TextSpan(
                  text: ' and ',
                  style: TextStyle(
                    color: AppColors.greyColor, // Set the text color here
                    fontSize: AppFontSizes.small, // Set the font size here
                  ),
                ),
                TextSpan(
                  text: 'Privacy Policy.',
                  style: const TextStyle(color: AppColors.primaryColor),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      // 在这里处理接的点击事件，例如导航到隐私政策页面
                      Navigator.pushNamed(context, '/privacy_policy');
                    },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildCodeInput() {
    return ValueListenableBuilder<String?>(
      valueListenable: _codeError,
      builder: (context, error, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CupertinoTextField(
              key: const ValueKey('code_field'),
              controller: _codeTextController,
              focusNode: _codeFocusNode,
              placeholder: 'Code',
            ),
            if (error != null)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: buildError(context, error),
              ),
          ],
        );
      },
    );
  }

  final _codeFormKey = GlobalKey<FormState>();
  Widget buildCodeForm(BuildContext context) {
    return Form(
      key: _codeFormKey,
      child: Column(
        children: [
          const SizedBox(height: AppPadding.medium),
          const Text(
            "Welcome to TeeTimeBot",
            style: TextStyle(
                fontSize: 20.0, // Set the font size here
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor),
          ),
          Text(
            "Enter the code received at ${_emailTextController.text} to continue",
            style: const TextStyle(
                fontSize: 14.0, // Set the font size here
                color: Color(0xFF333333)),
          ),
          const SizedBox(height: AppPadding.large),
          const SizedBox(height: AppPadding.medium),
          buildPassword(context),
          const SizedBox(height: AppPadding.medium),
          buildCodeInput(),
          const SizedBox(height: AppPadding.large),
          SizedBox(
            width: MediaQuery.of(context).size.width,
            child: ValueListenableBuilder<String?>(
              valueListenable: _emailError,
              builder: (context, emailError, _) {
                return ValueListenableBuilder<String?>(
                  valueListenable: _passwordError,
                  builder: (context, passwordError, _) {
                    return ValueListenableBuilder<String?>(
                      valueListenable: _codeError,
                      builder: (context, codeError, _) {
                        final canSubmit = emailError == null &&
                            passwordError == null &&
                            codeError == null &&
                            _emailTextController.text.isNotEmpty &&
                            _passwordTextController.text.isNotEmpty &&
                            _codeTextController.text.isNotEmpty;

                        return CupertinoButton(
                          color: AppColors.primaryColor,
                          disabledColor: AppColors.disabledColor,
                          onPressed: canSubmit ? handleRegister : null,
                          child: const Text(
                            'Submit',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
          const SizedBox(
            height: AppPadding.small,
          ),
          RichText(
            text: TextSpan(
              style: DefaultTextStyle.of(context).style,
              children: <TextSpan>[
                TextSpan(
                  text: 'Terms & Conditions',
                  style: const TextStyle(color: AppColors.primaryColor),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      // 在这里处理链接的点击事件，例如导航到条款和条件页面
                      Navigator.pushNamed(context, '/terms_and_conditions');
                    },
                ),
                const TextSpan(
                  text: ' and ',
                  style: TextStyle(
                    color: AppColors.greyColor, // Set the text color here
                    fontSize: AppFontSizes.small, // Set the font size here
                  ),
                ),
                TextSpan(
                  text: 'Privacy Policy.',
                  style: const TextStyle(color: AppColors.primaryColor),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      // 在这里处理接的点击事件，例如导航到隐私政策页面
                      Navigator.pushNamed(context, '/privacy_policy');
                    },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _canGoBack,
      onPopInvoked: _onPopInvoked,
      child: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          buildEmailForm(context),
          buildCodeForm(context),
        ],
      ),
    );
  }
}
