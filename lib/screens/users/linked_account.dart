// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import 'package:golf/screens/login_state.dart';
import 'package:golf/screens/users/linked_club.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/user.dart';
import 'package:provider/provider.dart';

class LinkedAccountScreen extends StatefulWidget {
  const LinkedAccountScreen({super.key});

  @override
  _LinkedAccountScreenState createState() {
    return _LinkedAccountScreenState();
  }
}

class _LinkedAccountScreenState
    extends LoginRequiredState<LinkedAccountScreen> {
  int points = 0;
  late Future<List<LinkedAccount>> _accountListFuture;

  @override
  void initState() {
    super.initState();
    _accountListFuture =
        Provider.of<APIBase>(context, listen: false).getLinkedAccounts();
  }

  Widget buildEmpty(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const SizedBox(height: 60),
          Image.asset('assets/imgs/account_empty.png'), // 图片路径根据实际情况修改
          const SizedBox(height: 10),
          const Text(
            "You don't have any golf platform linked with this account yet.",
            style: TextStyle(
                color: AppColors.greyColor,
                fontSize: 14,
                fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: buildContent(context),
          ),
        ),
      ),
    );
  }

  Widget buildCard(BuildContext context, LinkedAccount account) {
    return InkWell(
        onTap: () {
          // 跳回来，需要刷新数据
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => LinkedClubPage(account: account)),
          ).then((value) => {
                // refresh data
                setState(() {
                  _accountListFuture =
                      Provider.of<APIBase>(context, listen: false)
                          .getLinkedAccounts();
                })
              });
        },
        child: Card(
          color: Colors.white,
          // elevation: 0.2, // 阴影
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    const Text(
                      'Account',
                      style: TextStyle(fontSize: 14),
                    ),
                    Text(
                      account.platform.name,
                      style: const TextStyle(
                          fontSize: 14, color: AppColors.greyColor),
                    ),
                  ],
                ),
                const SizedBox(height: 8.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    const Text(
                      'Email',
                      style: TextStyle(fontSize: 14),
                    ),
                    Text(
                      account.bindAccount ?? "",
                      style: const TextStyle(
                          fontSize: 14, color: AppColors.greyColor),
                    ),
                  ],
                ),
                const SizedBox(height: 8.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    const Text(
                      'Status',
                      style: TextStyle(fontSize: 14),
                    ),
                    Text(
                      account.available,
                      style: const TextStyle(
                          fontSize: 14, color: AppColors.greyColor),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
  }

  Widget buildCards(BuildContext context) {
    return FutureBuilder<List<LinkedAccount>>(
      future: _accountListFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: CircularProgressIndicator(
              color: AppColors.primaryColor,
            ),
          );
        } else if (snapshot.hasError) {
          return const Center(
            child: Text('Failed to load data'),
          );
        } else {
          if (snapshot.data!.isEmpty) {
            return buildEmpty(context);
          }
          return Column(
            children: snapshot.data!.map((account) {
              return buildCard(context, account);
            }).toList(),
          );
        }
      },
    );
  }

  Widget buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const SizedBox(height: 12),
        const Divider(
          color: AppColors.primaryColor,
          height: 1,
        ),
        const SizedBox(height: 20),
        const Text(
          "You need to link your TeeeTimeBot account with your favorite golf course account to enable the automatic booking feature. However, waitlist notification feature does not require this binding.",
          style: TextStyle(color: AppColors.greyColor, fontSize: 14),
        ),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'My Accounts',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
            ),
            ElevatedButton(
              onPressed: () {
                // nav to link account page
                Navigator.pushNamed(context, '/user/link_account')
                    .then((value) => {
                          // refresh data
                          setState(() {
                            _accountListFuture =
                                Provider.of<APIBase>(context, listen: false)
                                    .getLinkedAccounts();
                          })
                        });
              },
              style: ButtonStyle(
                backgroundColor:
                    WidgetStateProperty.all(AppColors.primaryColor),
                shape: WidgetStateProperty.all(RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.circular(5), // Set the border radius to 5
                )),
              ),
              child: const Text(
                'Add Account',
                style: TextStyle(color: Colors.white),
              ),
            )
          ],
        ),
        const SizedBox(height: 12),
        RichText(
          text: const TextSpan(
            style: TextStyle(color: AppColors.greyColor, fontSize: 14),
            children: <TextSpan>[
              TextSpan(
                text:
                    'If your account become invalid or expired, please first remove it, and then add it back later.',
              ),
            ],
          ),
        ),
        // call api to get linked account

        buildCards(context),
      ],
    );
  }
}
