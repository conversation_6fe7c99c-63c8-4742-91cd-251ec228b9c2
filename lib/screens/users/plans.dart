// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:golf/screens/login_state.dart';
import 'package:golf/services/provider/api.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:golf/config.dart';
import 'package:golf/utils/logger.dart';

import 'package:golf/services/provider/base.dart';
import 'package:golf/services/provider/pagestate_provider.dart';

import 'package:golf/services/req/booking_req.dart';
import 'package:golf/services/req/req.dart';
import 'package:golf/utils/exception.dart';
import 'package:golf/widgets/golf_dialog.dart';
import 'package:golf/widgets/golf_dropdownbutton.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:golf/auth_guard_observer.dart';
import 'package:golf/screens/users/plan_detail.dart';

class PlansScreen extends StatefulWidget {
  final BookingStatus selectedStatus;
  const PlansScreen({super.key, required this.selectedStatus});

  @override
  _PlansScreenState createState() => _PlansScreenState();
}

class _PlansScreenState extends LoginRequiredState<PlansScreen> {
  static const greenColor = Color(0xFF078B54);
  static const grayColor = Color(0xFF666666);
  BookingStatus selectedStatus = BookingStatus.all;
  Paginator paginator = Paginator(prev: 0, next: 0, total: -1);
  int currentPage = 1;
  final int pageSzie = 10;
  String _selectedCourse = ''; // Initialize the selected course
  late APIBase api;
  final List<Plan> _data = [];
  final ScrollController _scrollController = ScrollController();

  List<SimpleCourse> courses = [];
  bool _courseLoading = false;
  bool _isLoadingMore = false; // 加载更多数据
  bool _isLoadingNew = false; // 刷新

  @override
  void initState() {
    super.initState();
    selectedStatus = widget.selectedStatus;

    _scrollController.addListener(_onScroll);
    selectedStatus = widget.selectedStatus;
    api = Provider.of<APIBase>(context, listen: false);
    loadCourses();
    _refreshData();
  }

  Future<void> loadCourses() async {
    if (_courseLoading) {
      return;
    }
    setState(() {
      _courseLoading = true;
    });

    context.read<APIBase>().getPlanCourses().then((value) {
      setState(() {
        courses = value;
        _courseLoading = false;
      });
    }).catchError((e) {
      setState(() {
        _courseLoading = false;
      });
      showError(e, context);
    });
  }

  Future<void> _refreshData() async {
    if (_isLoadingNew) {
      return;
    }
    _isLoadingNew = true;
    var page = 1;
    try {
      var response = await api.getPlans(BookingListRequest(
          courseId: _selectedCourse,
          status: selectedStatus,
          pageNum: page,
          pageSize: pageSzie));

      setState(() {
        _data.clear();
        _data.addAll(response.items);
        currentPage = page;
        _isLoadingNew = false;
        paginator = response.paginator;
      });
    } catch (e) {
      showError(e, context);
    } finally {
      setState(() {
        _isLoadingNew = false;
      });
    }
  }

  void _onScroll() {
    if (!_isLoadingMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent) {
      _loadMoreData();
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore) {
      return;
    }
    // 没更多页了。也应该返回
    if (currentPage > paginator.total) {
      return;
    }
    if (_data.isEmpty) {
      return;
    }
    setState(() {
      _isLoadingMore = true; //show loading
    });

    var page = currentPage + 1;
    try {
      var response = await api.getPlans(BookingListRequest(
          courseId: _selectedCourse,
          status: selectedStatus,
          pageNum: currentPage,
          pageSize: pageSzie));

      setState(() {
        _isLoadingMore = false;
        paginator = response.paginator;
        currentPage = page;
        if (response.items.isNotEmpty) {
          // 需要滤重
          var newItems = response.items.where((element) {
            return !_data.any((item) => item.id == element.id);
          }).toList();
          _data.addAll(newItems);
        }
      });
    } catch (e) {
      showError(e, context);
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose(); // 不要忘记释放资源
    super.dispose();
  }

  handleDelete(String id) async {
    try {
      showLoadingDialog(context);
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);
      await api.deletePlan(DeleteBookingRequest(id: id));
      Navigator.of(context).pop();
// remove the plan from the list
      //refresh the data
      _refreshData();
    } catch (e) {
      Navigator.of(context).pop();
      showError(e, context);
    }
  }

  @override
  void didUpdateWidget(covariant PlansScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    AppLogger.info('didUpdateWidget: ${oldWidget.selectedStatus}', 'UI');
    _refreshData();
    loadCourses();
  }

  Widget buildCourses(BuildContext context) {
    if (_courseLoading) {
      return const Align(
        alignment: Alignment.center,
        child: SizedBox(
          width: 20, // 自定义宽度
          height: 20, // 自定义高度
          child: CircularProgressIndicator(
            color: AppColors.primaryColor,
            value: null, // 设置为 null 以显示动画效果
            strokeWidth: 1, // 调整线条的粗细
            backgroundColor: Colors.grey, // 设置背景色
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
        ),
      ); // Show a loading spinner while waiting
    }

    return GolfDropdownButton(
      items: courses
          .map((course) =>
              GolfDropdownMenuItem(label: course.name, value: course.id))
          .toList(),
      onChanged: (newValue) {
        setState(() {
          _selectedCourse = newValue;
          _refreshData();
        });
      },
    );
  }

  Widget buildHeadRow(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        buildCourses(context),

        const SizedBox(
            width: 8.0), // Add some space between the dropdown and the button

        GolfDropdownButton(
          value: selectedStatus.name,
          items: BookingStatus.values
              .map((status) =>
                  GolfDropdownMenuItem(label: status.name, value: status.name))
              .toList(),
          onChanged: (newValue) {
            setState(() {
              selectedStatus = BookingStatusExtension.fromString(newValue);
              currentPage = 1;
              _refreshData();
            });
          },
        ),
      ],
    );
  }

  Color _getTimeColorBasedOnStatus(Plan plan) {
    switch (plan.status) {
      case BookingStatus.success:
        if (plan.showTime == 'Past') {
          return Colors.red;
        }
        return greenColor;
      case BookingStatus.pending:
        return Colors.black;
      case BookingStatus.failed:
        return Colors.black;
      default:
        return Colors.black;
    }
  }

  Widget _getStatusDescription(BookingStatus status) {
    switch (status) {
      case BookingStatus.success:
        return const Text(
          'Reservation Completed.',
          style: TextStyle(fontSize: 14.0, color: AppColors.primaryColor),
        );
      case BookingStatus.pending:
        return const Text(
          'Processing.',
          style: TextStyle(fontSize: 14.0, color: Color(0xFFFF2828)),
        );
      case BookingStatus.failed:
        return const Text(
          'Failed to get reservation.',
          style: TextStyle(fontSize: 14.0, color: grayColor),
        );
      default:
        return Container();
    }
  }

  Widget _getButtonBasedOnPlan(Plan plan) {
    // 定义更紧凑的按钮样式
    final buttonStyle = TextButton.styleFrom(
      foregroundColor: Colors.white,
      backgroundColor: AppColors.primaryColor,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0), // 减小内边距
      minimumSize: const Size(70, 32), // 减小最小尺寸
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
    );

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding:
          const EdgeInsets.symmetric(horizontal: 4, vertical: 4), // 减小容器内边距
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              if (plan.status == BookingStatus.success) ...[
                TextButton.icon(
                  icon: const Icon(Icons.info_outline,
                      size: 16, color: Colors.white), // 减小图标大小
                  label: const Text('Info',
                      style: TextStyle(fontSize: 13)), // 减小文字大小
                  style: buttonStyle,
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text('Need to Cancel Your Reservation?'),
                          content: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'We made this reservation on your behalf. To cancel it, please contact the course directly:',
                                style: TextStyle(fontSize: 14),
                              ),
                              const SizedBox(height: 16),
                              if (plan.coursePhone != null) ...[
                                GestureDetector(
                                  onTap: () {
                                    final Uri telLaunchUri = Uri(
                                      scheme: 'tel',
                                      path: plan.coursePhone,
                                    );
                                    launchUrl(telLaunchUri);
                                  },
                                  child: Row(
                                    children: [
                                      const Icon(Icons.phone, size: 16),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Phone: ${plan.coursePhone}',
                                        style: const TextStyle(
                                          color: Colors.blue,
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 8),
                              ],
                              if (plan.courseWebsite != null) ...[
                                GestureDetector(
                                  onTap: () {
                                    launchUrl(Uri.parse(plan.courseWebsite!));
                                  },
                                  child: Row(
                                    children: [
                                      const Icon(Icons.language, size: 16),
                                      const SizedBox(width: 8),
                                      Text(
                                        'Website: ${plan.courseWebsite}',
                                        style: const TextStyle(
                                          color: Colors.blue,
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                              const SizedBox(height: 16),
                              const Text(
                                'The course staff will assist you with your cancellation.',
                                style: TextStyle(fontSize: 14),
                              ),
                            ],
                          ),
                          actions: <Widget>[
                            TextButton(
                              child: const Text('Close'),
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
                const SizedBox(width: 4), // 减小按钮间距
                TextButton.icon(
                  icon: const Icon(Icons.share, size: 16, color: Colors.white),
                  label: const Text('Share', style: TextStyle(fontSize: 13)),
                  style: buttonStyle,
                  onPressed: () {
                    final id = plan.id;

                    if (kIsWeb) {
                      final shareUrl =
                          Uri.base.replace(path: '/reservation/$id').toString();
                      // 如果是 Web 平台，直接复制 URL 到剪贴板
                      Clipboard.setData(ClipboardData(
                          text:
                              'I have booked a tee time through Tee time bot and now I am sharing it with you.\r\n$shareUrl'));
                      // 显示 toast
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('URL copied to clipboard'),
                        ),
                      );
                    } else {
                      final shareUrl = '$siteurl/reservation/$id';
                      // 如果不是 Web 平台，尝试使用设备的分享功能
                      try {
                        Share.share('Check this out: $shareUrl');
                      } catch (e) {
                        // 如果分享失败，复制 URL 到剪贴板
                        Clipboard.setData(ClipboardData(text: shareUrl));
                        // 显示 toast
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('URL copied to clipboard'),
                          ),
                        );
                      }
                    }
                  },
                ),
              ] else if (plan.status == BookingStatus.pending)
                TextButton.icon(
                  icon: const Icon(Icons.cancel_outlined,
                      size: 16, color: Colors.white),
                  label: const Text('Cancel', style: TextStyle(fontSize: 13)),
                  style: buttonStyle,
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text('Confirm to cancel'),
                          content: const Text(
                              'After cancelling this plan, your reservation will no longer be executed.'),
                          actions: <Widget>[
                            TextButton(
                              child: const Text('Cancel'),
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                            ),
                            TextButton(
                              child: const Text('Confirm'),
                              onPressed: () {
                                handleDelete(plan.id);
                                Navigator.of(context).pop();
                              },
                            ),
                          ],
                        );
                      },
                    );
                  },
                )
              else if (plan.status == BookingStatus.failed)
                TextButton.icon(
                  icon: const Icon(Icons.delete_outline,
                      size: 16, color: Colors.white),
                  label: const Text('Delete', style: TextStyle(fontSize: 13)),
                  style: buttonStyle,
                  onPressed: () {
                    handleDelete(plan.id);
                  },
                ),
            ],
          ),
          TextButton.icon(
            icon:
                const Icon(Icons.copy_outlined, size: 16, color: Colors.white),
            label: const Text('Clone', style: TextStyle(fontSize: 13)),
            style: buttonStyle,
            onPressed: () {
              context.read<PageStateNotifier>().updateState(PageState.home);
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/',
                (route) => false,
                arguments: RouteArguments(
                    requiresAuth: true, data: {'clonePlan': plan}),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget buildListView(BuildContext context) {
    if (_isLoadingNew) {
      return const Align(
        alignment: Alignment.center,
        child: SizedBox(
          width: 20, // 自定义宽度
          height: 20, // 自定义高度
          child: CircularProgressIndicator(
            color: AppColors.primaryColor,
            value: null, // 设置为 null 以显示动画效果
            strokeWidth: 1, // 调整线条的粗细
            backgroundColor: Colors.grey, // 设置背景色
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
        ),
      ); // Show a loading spinner while waiting
    }

    if (paginator.total == 0) {
      return buildEmpty(context);
    }

    return RefreshIndicator(
      onRefresh: _refreshData,
      child: ListView.separated(
        physics: const AlwaysScrollableScrollPhysics(),
        controller: _scrollController,
        itemCount: _data.length + 1, // 加1是为了在列表底部添加一个加载提示
        separatorBuilder: (BuildContext context, int index) =>
            const SizedBox(height: 2), // 在这里定义间距
        itemBuilder: (context, index) {
          if (index == _data.length) {
            // 到达列表底部
            if (_data.isNotEmpty && _isLoadingMore) {
              // 检查_data是否不为空

              return const Center(
                  child: CircularProgressIndicator(
                color: AppColors.primaryColor,
              )); // 显示加载指示器
            } else if (paginator.total == 0) {
              return buildEmpty(context);
            } else {
              return const SizedBox(); // 不显示加载指示器
            }
          }

          return buildListTile(context, _data[index]);
        },
      ),
    );
  }

  Widget buildListTile(BuildContext context, Plan plan) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PlanDetailScreen(planId: plan.id),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.grey,
            width: 1.0,
          ),
          borderRadius: const BorderRadius.all(
            Radius.circular(4.0),
          ),
        ),
        child: ListTile(
          title: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${plan.reservationDate} ${plan.teetime}',
                    style: const TextStyle(fontSize: 14.0),
                  ),
                  Text(
                    plan.showTime,
                    style: TextStyle(
                      fontSize: 14.0,
                      color: _getTimeColorBasedOnStatus(plan),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${plan.players} Player',
                    style: const TextStyle(
                        fontSize: 14.0, color: Color(0xFF666666)),
                  ),
                  if (plan.priceName != null)
                    Text(
                      plan.priceName!,
                      style: const TextStyle(fontSize: 14.0),
                    ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ConstrainedBox(
                        constraints: const BoxConstraints(maxWidth: 200),
                        child: Text(
                          plan.courseName,
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 14.0),
                        ),
                      ),
                      _getStatusDescription(plan.status),
                    ],
                  ),
                ],
              ),
              _getButtonBasedOnPlan(plan),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildEmpty(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Image.asset('assets/imgs/plan_empty.png'), // 图片路径根据实际情况修改
          const SizedBox(height: 10),
          const Text(
            "You don't have any automatic tee time booking schedule yet.",
            style: TextStyle(
                color: AppColors.greyColor,
                fontSize: 14,
                fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: () {
              context.read<PageStateNotifier>().updateState(PageState.home);
              Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
            },
            // 设置按钮样式
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor, // 设置按钮背景色

              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0), // 设置按钮圆角
              ),
            ),
            child: const Text(
              'Setup a bot schedule',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Colors.grey[200], // Set the background color here
        padding: const EdgeInsets.all(8.0),

        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.all(8.0),
          child: Column(children: [
            buildHeadRow(context),
            const SizedBox(height: 8),
            FractionallySizedBox(
              widthFactor: 0.95, // Set the width to be 80% of the parent widget
              child: Divider(
                color: Theme.of(context).primaryColor,
                height: 1,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(child: buildListView(context)),
          ]),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // 回到首页
          context.read<PageStateNotifier>().updateState(PageState.home);
          Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
        },
        backgroundColor: Theme.of(context).primaryColor,
        shape: const CircleBorder(),
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }
}
