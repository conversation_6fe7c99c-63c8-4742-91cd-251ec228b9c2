// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:golf/screens/login_state.dart';
import 'package:golf/services/provider/auth_notifier.dart';
import 'package:golf/config.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/email_req.dart';
import 'package:golf/services/req/user.dart';
import 'package:golf/utils/exception.dart';
import 'package:golf/widgets/golf_appbar.dart';
import 'package:golf/widgets/golf_dialog.dart';
import 'package:provider/provider.dart';

class DeleteAccountScreen extends StatefulWidget {
  const DeleteAccountScreen({super.key});

  @override
  _DeleteAccountScreenState createState() => _DeleteAccountScreenState();
}

class _DeleteAccountScreenState
    extends LoginRequiredState<DeleteAccountScreen> {
  final _formKey = GlobalKey<FormState>();
  final PageController _pageController = PageController();
  final _codeTextController = TextEditingController();

  bool _canGoBack = true;

  int _countdownTime = 0;
  Timer? _timer;
  String? email;
  String? token;
  String? _codeError;

  void _startCountdown() {
    setState(() {
      _countdownTime = 60;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdownTime < 1) {
          _timer?.cancel();
        } else {
          _countdownTime -= 1;
        }
      });
    });
  }

  @override
  void initState() {
    super.initState();
    // 监听页面变化
    _pageController.addListener(() {
      final page = _pageController.page ?? 0.0;
      setState(() {
        _canGoBack = page == 0;
      });
    });
  }

  @override
  void dispose() {
    _codeTextController.dispose();
    _pageController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _nextPage() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeIn,
    );
  }

  Widget buildDeleted(BuildContext context) {
    return const Text(
      'Your account will be deleted within the next 48 hours during which time you will not be able to log in to the account.',
      style: TextStyle(fontSize: 14),
    );
  }

  Widget buildSendForm(BuildContext context) {
    AuthNotifier authNotifier = Provider.of<AuthNotifier>(context);
    var user = authNotifier.getUser();
    if (user == null) {
      return buildDeleted(context);
    }
    email = user.email;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'Account deletion is possible after verification.',
          style: TextStyle(fontSize: 14),
        ),
        const Text(
          'It will cause all of your plans to become inactive.',
          style: TextStyle(fontSize: 14, color: Colors.red),
        ),
        const SizedBox(height: 32.0),
        Text(
          'Your email: $email.',
          style: const TextStyle(fontSize: 14),
        ),
        const Spacer(),
        SizedBox(
          width: MediaQuery.of(context).size.width *
              0.85, // Set the button width to 95% of the screen width
          child: CupertinoButton(
            padding: const EdgeInsets.symmetric(
                horizontal:
                    12.0), // Adjust the button size by changing the padding
            color: AppColors.primaryColor,
            onPressed: (_countdownTime > 0 || _codeError != null)
                ? null
                : handleSendEmail,
            child: Text(_countdownTime > 0 ? '$_countdownTime s' : 'Send'),
          ),
        ),
      ],
    );
  }

  Widget buildDelActionForm(BuildContext context) {
    AuthNotifier authNotifier = Provider.of<AuthNotifier>(context);
    var user = authNotifier.getUser();
    if (user == null) {
      return buildDeleted(context);
    }
    email = user.email;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'Account deletion is possible after verification.',
          style: TextStyle(fontSize: 14),
        ),
        const Text(
          'It will cause all of your plans to become inactive.',
          style: TextStyle(fontSize: 14, color: Colors.red),
        ),
        const SizedBox(height: 32.0),
        Text(
          'Your email: $email.',
          style: const TextStyle(fontSize: 14),
        ),
        const SizedBox(height: 8),
        Row(
          children: <Widget>[
            Expanded(
              child: SizedBox(
                height: AppHeight.input, // Set the height of the text field
                child: CupertinoTextField(
                  placeholder: 'Code',
                  controller: _codeTextController,
                  onChanged: (value) {
                    String? error;
                    if (value.isEmpty) {
                      error = 'Code is required';
                    }
                    setState(() {
                      _codeError = error;
                    });
                  },
                ),
              ),
            ),
          ],
        ),
        if (_codeError != null) buildError(context, _codeError!),
        const SizedBox(height: 16.0),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red, // Set the background color
            shape: RoundedRectangleBorder(
              borderRadius:
                  BorderRadius.circular(5.0), // Set the border radius to 5
            ),
            minimumSize:
                const Size(double.infinity, 50), // Set the width to 100%
          ),
          onPressed: (_codeTextController.text.isEmpty) ? null : handleDelete,
          child: const Text(
            'Delete',
            style: TextStyle(color: Colors.white),
          ),
        ),
      ],
    );
  }

  Widget buildError(BuildContext context, String text) {
    return Align(
      alignment: Alignment.topLeft,
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.red,
          fontSize: 12,
        ),
      ),
    );
  }

  void handleSendEmail() async {
    if (_countdownTime > 0) {
      return;
    }

    try {
      showLoadingDialog(context);
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);
      SendEmailResponse response = await api.sendEmail(
          SendEmailRequest(email: email!, type: EmailType.deleteAccount));
      token = response.data.token;
      Navigator.of(context).pop();
      _startCountdown();
      _nextPage();
    } catch (e) {
      Navigator.of(context).pop();
      showError(e as Exception, context);
    }
  }

  handleDelete() async {
    String code = _codeTextController.text;
    if (code.isEmpty) {
      setState(() {
        _codeError = 'Code is required';
      });
      return;
    }
    if (token == null) {
      setState(() {
        _codeError = 'Please send the email first';
      });
      return;
    }
    try {
      showLoadingDialog(context);
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);
      await api.deleteAccount(DeleteAccountRequest(code: code, token: token!));
      _nextPage();
    } catch (e) {
      showError(e as Exception, context);
    } finally {
      Navigator.of(context).pop();
    }
  }

  _onPopInvoked(bool didPop) async {
    if (_canGoBack) {
      return;
    }
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _canGoBack,
      onPopInvoked: _onPopInvoked,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: const GolfAppBar(
          title: 'Delete Account',
        ),
        body: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Container(
            color: Colors.white, // Set the background color to white
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    const Text(
                      'Delete Account',
                      style: TextStyle(
                          fontSize: 18.0, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    const Divider(
                      color: AppColors.primaryColor,
                      height: 1,
                    ),
                    const SizedBox(height: 16),
                    Expanded(
                      child: PageView(
                        controller: _pageController,
                        physics: const NeverScrollableScrollPhysics(),
                        children: <Widget>[
                          buildSendForm(context),
                          buildDelActionForm(context),
                          buildDeleted(context),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
