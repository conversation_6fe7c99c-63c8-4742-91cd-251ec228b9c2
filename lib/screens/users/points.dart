// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:golf/config.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/screens/login_state.dart';
import 'package:golf/screens/users/point_detail.dart';
import 'package:golf/services/provider/api.dart';
import 'package:golf/services/provider/auth_notifier.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/utils/exception.dart';
import 'package:provider/provider.dart';

class PointsScreen extends StatefulWidget {
  const PointsScreen({super.key});

  @override
  _PointsScreenState createState() {
    return _PointsScreenState();
  }
}

class _PointsScreenState extends LoginRequiredState<PointsScreen> {
  int balance = 0;
  int? registerRule;
  int? reservationRule;
  // 邀请用户注册的积分规则
  int? inviteRule;
  final bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    fetchPoints();
  }

  @override
  void didUpdateWidget(covariant PointsScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    AppLogger.info('PointsScreen didUpdateWidget', 'UI');
    fetchPoints();
  }

  void fetchPoints() async {
    // Replace with your actual API call
    final api = Provider.of<APIBase>(context, listen: false);
    try {
      var newPoints = (await api.getUserDetail()).points;
      var rules = (await api.getPointRule());

      setState(() {
        balance = int.parse(newPoints);
        registerRule = rules.pointrules["register"];
        // 转绝对值
        reservationRule = rules.pointrules["plannedPerPlayer"]!.abs();
        inviteRule = rules.pointrules["invitor"]!.abs();
      });
    } catch (e) {
      showError(e as Exception, context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                color: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: buildContent(context),
                ),
              ),
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withValues(alpha: 0.5),
              child: const Center(
                child: CircularProgressIndicator(
                  color: AppColors.primaryColor,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget buildContent(BuildContext context) {
    AuthNotifier authNotifier = Provider.of<AuthNotifier>(context);

    var uid = "";
    var username = "";
    if (authNotifier.getUser() != null) {
      uid = authNotifier.getUser().useId;
      final email = authNotifier.getUser().email;
      username = email.split('@')[0];
    }
    var shareUrl =
        Uri.base.replace(path: '/user/sign_up/$uid-$username').toString();
    if (!kIsWeb) {
      shareUrl = '$siteurl/user/sign_up/$uid-$username';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const SizedBox(height: 10),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              const Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  'My Points',
                  style: TextStyle(
                      fontSize: 28.0,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D3748)),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                "$balance",
                style: const TextStyle(
                    fontSize: 60,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4ADE80)),
              ),
              const Text(
                'Points',
                style: TextStyle(fontSize: 20, color: Colors.grey),
              ),
              const SizedBox(height: 30),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: OutlinedButton(
                  onPressed: () {
                    Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => const PointDetailPage()));
                  },
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: AppColors.primaryColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Points Details',
                    style: TextStyle(
                      color: AppColors.primaryColor,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: () {
                    _showRedeemDialog(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Redeem Points',
                    style: TextStyle(
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
              if (kIsWeb) ...[
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: () async {
                      final result = await Navigator.pushNamed(
                        context,
                        '/user/point_store',
                      );
                      if (result == true) {
                        fetchPoints(); // 刷新积分
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'Buy Points',
                      style: TextStyle(
                        fontSize: 16,
                      ),
                    ),
                  ),
                )
              ],
              const SizedBox(height: 20),
              Text(
                '$reservationRule point will be deducted for each successful TeeTimeBot reservation',
                style: const TextStyle(color: Colors.grey, fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Earn Points',
                style: TextStyle(
                    fontSize: 28.0,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2D3748)),
              ),
              const SizedBox(height: 20),
              const Text(
                'Invite Friends',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              Text(
                'Get ${inviteRule ?? 25} points when you successfully refer a golf buddy to join TeeTimeBot',
                style: const TextStyle(color: Colors.grey, fontSize: 14),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SelectableText(
                  shareUrl,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: OutlinedButton(
                  onPressed: () {
                    final shareText =
                        'No more staying up late to book tee times! Click here to claim the \$25 credit in TeeTimeBot\n$username sent to you!\n$shareUrl';

                    Clipboard.setData(ClipboardData(text: shareText));

                    Fluttertoast.showToast(
                        msg: "Link copied",
                        toastLength: Toast.LENGTH_LONG,
                        gravity: ToastGravity.BOTTOM,
                        timeInSecForIosWeb: 3,
                        backgroundColor: Colors.grey,
                        textColor: Colors.white,
                        fontSize: 16.0);
                  },
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: AppColors.primaryColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Copy Invitation Link',
                    style: TextStyle(
                      color: AppColors.primaryColor,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  void _showRedeemDialog(BuildContext context) {
    final codeController = TextEditingController();
    String? errorMessage;
    bool isRedeeming = false;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Row(
                children: [
                  Icon(Icons.card_giftcard, color: AppColors.primaryColor),
                  SizedBox(width: 8),
                  Text('Redeem Points'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: TextField(
                      controller: codeController,
                      decoration: const InputDecoration(
                        hintText: 'Enter redemption code',
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        border: InputBorder.none,
                      ),
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  if (errorMessage != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        errorMessage!,
                        style: const TextStyle(color: Colors.red, fontSize: 12),
                      ),
                    ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isRedeeming
                      ? null
                      : () async {
                          if (codeController.text.isEmpty) {
                            setState(() {
                              errorMessage = 'Please enter a redemption code';
                            });
                            return;
                          }

                          setState(() {
                            isRedeeming = true;
                            errorMessage = null;
                          });

                          try {
                            final api =
                                Provider.of<APIBase>(context, listen: false);
                            // 调用兑换积分的 API
                            final response =
                                await api.redeemPoints(codeController.text);

                            Navigator.pop(context);

                            // 显示成功提示
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return AlertDialog(
                                  content: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(
                                        Icons.check_circle,
                                        color: Colors.green,
                                        size: 48,
                                      ),
                                      const SizedBox(height: 16),
                                      const Text(
                                        'Success!',
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        "You've earned ${response.points} points!",
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                    ],
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () {
                                        Navigator.pop(context);
                                        // 刷新积分
                                        fetchPoints();
                                      },
                                      child: const Text('OK'),
                                    ),
                                  ],
                                );
                              },
                            );
                          } catch (e) {
                            setState(() {
                              errorMessage = e.toString();
                              isRedeeming = false;
                            });
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: isRedeeming
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            color: AppColors.primaryColor,
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('Redeem Now'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
