// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/screens/login_state.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/provider/pagestate_provider.dart';
import 'package:golf/services/req/booking_req.dart';
import 'package:golf/services/req/notify_req.dart';
import 'package:golf/services/req/req.dart';
import 'package:golf/utils/exception.dart';
import 'package:golf/widgets/golf_dialog.dart';

import 'package:golf/widgets/golf_dropdownbutton.dart';
import 'package:provider/provider.dart';

class NotificationsScreen extends StatefulWidget {
  final NotificationStatus selectedStatus;
  const NotificationsScreen({super.key, required this.selectedStatus});

  @override
  _NotificationsScreenState createState() => _NotificationsScreenState();
}

class _NotificationsScreenState
    extends LoginRequiredState<NotificationsScreen> {
  static const greenColor = Color(0xFF078B54);
  static const grayColor = Color(0xFF666666);
  NotificationStatus selectedStatus = NotificationStatus.all;
  final List<NotificationData> _data = [];
  Paginator paginator = Paginator(prev: 0, next: 0, total: -1);
  int currentPage = 1;
  final int pageSzie = 10;
  String _selectedCourse = '';
  bool _isLoadingMore = false; // 加载更多数据
  bool _isLoadingNew = false; // 刷新
  List<SimpleCourse> courses = [];
  bool _courseLoading = false;

  late APIBase api;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    selectedStatus = widget.selectedStatus;
    api = Provider.of<APIBase>(context, listen: false);
    // _selectedCourse = "All Course"; // Set the selected course
    _refreshData();
    loadCourses();
  }

  @override
  void dispose() {
    _scrollController.dispose(); // 不要忘记释放资源
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant NotificationsScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    AppLogger.info(
        'NotificationsScreen didUpdateWidget: ${oldWidget.selectedStatus}', 'UI');
    _refreshData();
    loadCourses();
  }

  Future<void> loadCourses() async {
    if (_courseLoading) {
      return;
    }
    setState(() {
      _courseLoading = true;
    });

    context.read<APIBase>().getNotificationCourse().then((value) {
      setState(() {
        courses = value;
        _courseLoading = false;
      });
    }).catchError((e) {
      setState(() {
        _courseLoading = false;
      });
      showError(e as Exception, context);
    });
  }

  Widget buildCourses(BuildContext context) {
    if (_courseLoading) {
      return const Align(
        alignment: Alignment.center,
        child: SizedBox(
          width: 20, // 自定义宽度
          height: 20, // 自定义高度
          child: CircularProgressIndicator(
            value: null, // 设置为 null 以显示动画效果
            color: AppColors.primaryColor,
            strokeWidth: 1, // 调整线条的粗细
            backgroundColor: Colors.grey, // 设置背景色
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
        ),
      ); // Show a loading spinner while waiting
    }

    return GolfDropdownButton(
      items: courses
          .map((course) =>
              GolfDropdownMenuItem(label: course.name, value: course.id))
          .toList(),
      onChanged: (newValue) {
        setState(() {
          _selectedCourse = newValue;
          _refreshData();
        });
      },
    );
  }

  Future<void> _refreshData() async {
    if (_isLoadingNew) {
      return;
    }
    _isLoadingNew = true;
    var page = 1;
    try {
      var response = await api.getNotifications(NotificationListRequest(
          courseId: _selectedCourse,
          status: selectedStatus,
          pageNum: page,
          pageSize: pageSzie));

      setState(() {
        _data.clear();
        _data.addAll(response.items);
        currentPage = page;
        _isLoadingNew = false;
        paginator = response.paginator;
      });
    } catch (e) {
      showError(e as Exception, context);
    } finally {
      _isLoadingNew = false;
    }
  }

  void _onScroll() {
    if (!_isLoadingMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent) {
      _loadMoreData();
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore) {
      return;
    }
    // 没更多页了。也应该返回
    if (currentPage > paginator.total) {
      return;
    }
    if (_data.isEmpty) {
      return;
    }
    setState(() {
      _isLoadingMore = true;
    });
    try {
      var page = currentPage + 1;
      var response = await api.getNotifications(NotificationListRequest(
          courseId: _selectedCourse,
          status: selectedStatus,
          pageNum: page,
          pageSize: pageSzie));

      setState(() {
        _isLoadingMore = false;
        paginator = response.paginator;
        currentPage = page;
        if (response.items.isNotEmpty) {
          // 需要滤重
          var newItems = response.items.where((element) {
            return !_data.any((item) => item.id == element.id);
          }).toList();
          _data.addAll(newItems);
        }
      });
    } catch (e) {
      showError(e as Exception, context);
    } finally {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  Widget buildRow(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        buildCourses(context),

        const SizedBox(
            width: 8.0), // Add some space between the dropdown and the button

        GolfDropdownButton(
          value: selectedStatus.name,
          items: NotificationStatus.values
              .map((status) =>
                  GolfDropdownMenuItem(label: status.name, value: status.name))
              .toList(),
          onChanged: (newValue) {
            setState(() {
              currentPage = 1;
              selectedStatus = NotificationStatusExtension.fromString(newValue);
              _refreshData();
            });
          },
        ),
      ],
    );
  }

  Color _getTimeColorBasedOnStatus(NotificationStatus status) {
    switch (status) {
      case NotificationStatus.running:
        return greenColor;
      case NotificationStatus.failed:
        return grayColor;
      case NotificationStatus.success:
        return greenColor;
      default:
        return Colors.black;
    }
  }

  Widget _getStatusDescription(NotificationData notify) {
    switch (NotificationStatusExtension.fromString(notify.status)) {
      case NotificationStatus.success:
        return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text(
            'Available at ${notify.teeTime}',
            style: const TextStyle(fontSize: 14.0, color: greenColor),
          ),
          Text(
            'Notified on ${notify.updateTime}',
            style: const TextStyle(fontSize: 14.0, color: greenColor),
          )
        ]);

      case NotificationStatus.running:
        return const Text(
          'Waiting.',
          style: TextStyle(fontSize: 14.0, color: greenColor),
        );
      default:
        return const Text(
          'No available tee times were detected.',
          style: TextStyle(fontSize: 14.0, color: Colors.red),
        );
    }
  }

  Widget _getButtonBasedOnStatus(NotificationData notify) {
    switch (NotificationStatusExtension.fromString(notify.status)) {
      case NotificationStatus.running:
        return TextButton(
          style: ButtonStyle(
            foregroundColor: WidgetStateProperty.resolveWith((states) {
              return states.contains(WidgetState.disabled)
                  ? null
                  : Colors.white;
            }),
            backgroundColor: WidgetStateProperty.resolveWith((states) {
              return states.contains(WidgetState.disabled)
                  ? null
                  : Theme.of(context).primaryColorLight;
            }),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(5.0), // Adjust the border radius here
              ),
            ),
          ),
          onPressed: () {
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return AlertDialog(
                  title: const Text('Confirm to cancel'),
                  content: const Text(
                      'After cancelling this notification, your notify will no longer be executed.'),
                  actions: <Widget>[
                    TextButton(
                      child: const Text('Cancel'),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    ),
                    TextButton(
                      child: const Text('Confirm'),
                      onPressed: () {
                        handleDelete(notify.id);
                        Navigator.of(context).pop();
                      },
                    ),
                  ],
                );
              },
            );
          },
          child: const Text('Cancel'),
        );
      default:
        return Container(); // Return an empty container for 'Past' and other values
    }
  }

  handleDelete(String id) async {
    try {
      showLoadingDialog(context);
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);
      await api.deleteNotification(DeleteNotificationRequest(id: id));
      Navigator.of(context).pop();
// remove the plan from the list
      setState(() {
        // 遍历_data 删除对应数据
        _data.removeWhere((element) => element.id == id);
      });
    } catch (e) {
      Navigator.of(context).pop();
      showError(e as Exception, context);
    }
  }

  Widget buildListTile(BuildContext context, NotificationData notify) {
    return Container(
        margin: const EdgeInsets.all(8.0), // 添加外边距
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.grey, // Set border color
            width: 1.0, // Set border width
          ),
          borderRadius: const BorderRadius.all(
            Radius.circular(4.0), // Set border radius
          ),
        ),
        child: ListTile(
          title: Column(
            children: [
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    notify.dates,
                    style: const TextStyle(fontSize: 14.0),
                  ),
                  Text(
                    notify.showTime,
                    style: TextStyle(
                        fontSize: 14.0,
                        color: _getTimeColorBasedOnStatus(
                            NotificationStatusExtension.fromString(
                                notify.status))),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${notify.players} Player',
                    style: const TextStyle(
                        fontSize: 14.0, color: Color(0xFF666666)),
                  ),
                  if (notify.priceName != null)
                    Text(
                      notify.priceName!,
                      style: const TextStyle(fontSize: 14.0),
                    ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        notify.courseName,
                        style: const TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 14.0),
                      ),
                      _getStatusDescription(notify),
                    ],
                  ),
                  _getButtonBasedOnStatus(notify),
                ],
              ),
            ],
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Colors.grey[200], // Set the background color here
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Container(
              color: Colors.white,
              child: Column(children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: buildRow(context),
                ),
                const SizedBox(height: 8),
                FractionallySizedBox(
                  widthFactor:
                      0.95, // Set the width to be 80% of the parent widget
                  child: Divider(
                    color: Theme.of(context).primaryColor,
                    height: 1,
                  ),
                ),
                const SizedBox(height: 8),
                Expanded(
                    child: RefreshIndicator(
                  onRefresh: _refreshData,
                  child: ListView.separated(
                    physics: const AlwaysScrollableScrollPhysics(),
                    controller: _scrollController,
                    itemCount: _data.length + 1, // 加1是为了在列表底部添加一个加载提示
                    separatorBuilder: (BuildContext context, int index) =>
                        const SizedBox(height: 2), // 在这里定义间距
                    itemBuilder: (context, index) {
                      if (index == _data.length) {
                        // 到达列表底部
                        if (_data.isNotEmpty && _isLoadingMore) {
                          // 检查_data是否不为空

                          return const Center(
                              child: CircularProgressIndicator(
                            color: AppColors.primaryColor,
                          )); // 显示加载指示器
                        } else if (paginator.total == 0) {
                          return buildEmpty(context);
                        } else {
                          return const SizedBox(); // 不显示加载指示器
                        }
                      }

                      return buildListTile(context, _data[index]);
                    },
                  ),
                )),
              ])),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // 回到首页
          context.read<PageStateNotifier>().updateState(PageState.homeNotify);
          Navigator.pushNamedAndRemoveUntil(context, '/', (route) => false);
        },
        backgroundColor: Theme.of(context).primaryColor,
        shape: const CircleBorder(),
        child: const Icon(Icons.add_outlined, color: Colors.white),
      ),
    );
  }

  Widget buildEmpty(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Image.asset('assets/imgs/notification_empty.png'), // 图片路径根据实际情况修改
          const SizedBox(height: 10),
          const Text(
            "You don't have any waitlist notification yet.",
            style: TextStyle(
                color: AppColors.greyColor,
                fontSize: 14,
                fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: () {
              // 按钮点击事件 navigator to home page. clear all stacks
              context
                  .read<PageStateNotifier>()
                  .updateState(PageState.homeNotify);
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/',
                (route) => false,
              );
            },
            // 设置按钮样式
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor, // 设置按钮背景色

              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(5.0), // 设置按钮圆角
              ),
            ),
            child: const Text(
              'Join a waitlist',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
