// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import 'package:golf/screens/login_state.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/user.dart';
import 'package:golf/utils/exception.dart';
import 'package:golf/utils/validator.dart';
import 'package:golf/widgets/golf_appbar.dart';
import 'package:golf/widgets/golf_dialog.dart';
import 'package:golf/widgets/golf_toast.dart';
import 'package:provider/provider.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  _ChangePasswordScreenState createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState
    extends LoginRequiredState<ChangePasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _oldPasswordTextController = TextEditingController();
  final _newPasswordTextController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  String? _oldPasswordError;
  String? _newPasswordError;
  String? _confirmPasswordError;

  bool _oldPasswordObscureText = true;
  bool _newPasswordObscureText = true;
  bool _confirmPasswordObscureText = true;

  @override
  void dispose() {
    // Clean up the controller when the widget is disposed.
    _oldPasswordTextController.dispose();
    _newPasswordTextController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  Widget buildError(BuildContext context, String text) {
    return Align(
      alignment: Alignment.topLeft,
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.red,
          fontSize: 12,
        ),
      ),
    );
  }

  void handleChangePassword() async {
    String oldPassword = _oldPasswordTextController.text;
    String password = _newPasswordTextController.text;
    String password2 = _confirmPasswordController.text;

    if (oldPassword.isEmpty) {
      setState(() {
        _oldPasswordError = 'Password is required';
      });
      return;
    } else {
      if (!isValidPassword(oldPassword)) {
        setState(() {
          _oldPasswordError = passwordFormatError;
        });
        return;
      }
    }
    if (password.isEmpty) {
      setState(() {
        _newPasswordError = 'Password is required';
      });
      return;
    } else {
      if (!isValidPassword(password)) {
        setState(() {
          _newPasswordError = passwordFormatError;
        });
        return;
      }
    }
    if (oldPassword == password) {
      setState(() {
        _newPasswordError = 'New password cannot be the same as old password';
      });
      return;
    }
    if (password2 != password) {
      setState(() {
        _confirmPasswordError = 'Password does not match';
      });
      return;
    }

    try {
      showLoadingDialog(context);
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);
      await api.changePassword(ChangePasswordRequest(
          oldPassword: oldPassword, newPassword: password));
      Navigator.of(context).pop(); // 取消loading

      // tell user that password has been changed successfully
      showToast(
        context,
        msg: "Password changed",
      );
      Future.delayed(const Duration(seconds: 2)).then((_) {
        Navigator.of(context).pop(); // 关闭当前页面, 返回上一页。
      });
    } catch (e) {
      Navigator.of(context).pop();
      showError(e as Exception, context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: const GolfAppBar(
        title: 'Change Password',
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          color: Colors.white, // Set the background color to white
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Form(
              key: _formKey,
              child: Column(
                children: <Widget>[
                  SizedBox(
                    height: AppHeight.input, // Set the height of the text field
                    child: CupertinoTextField(
                      placeholder: 'Current password',
                      controller: _oldPasswordTextController,
                      obscureText:
                          _oldPasswordObscureText, // Use the _obscureText variable here
                      clearButtonMode: OverlayVisibilityMode.editing,
                      onChanged: (value) {
                        String? error;
                        if (value.isEmpty) {
                          error = 'Password is required';
                        } else if (!isValidPassword(value)) {
                          error = passwordFormatError;
                        }
                        setState(() {
                          _oldPasswordError = error;
                        });
                      },
                      suffix: GestureDetector(
                        onTap: () {
                          setState(() {
                            _oldPasswordObscureText =
                                !_oldPasswordObscureText; // Toggle the state of the _obscureText variable
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(
                              right:
                                  5.0), // Add some padding to the right of the icon
                          child: Icon(
                            _oldPasswordObscureText
                                ? CupertinoIcons.eye
                                : CupertinoIcons.eye_slash,
                          ),
                        ),
                      ),
                    ),
                  ),
                  if (_oldPasswordError != null)
                    buildError(context, _oldPasswordError!),
                  const SizedBox(height: AppPadding.small),
                  SizedBox(
                    height: AppHeight.input, // Set the height of the text field
                    child: CupertinoTextField(
                      placeholder: 'New password: a-z, 0-9, 8-16 digits',
                      controller: _newPasswordTextController,
                      obscureText:
                          _newPasswordObscureText, // Use the _obscureText variable here
                      onChanged: (value) {
                        String? error;
                        if (value.isEmpty) {
                          error = 'New password is required';
                        } else if (!isValidPassword(value)) {
                          error = passwordFormatError;
                        }
                        setState(() {
                          _newPasswordError = error;
                        });
                      },
                      suffix: GestureDetector(
                        onTap: () {
                          setState(() {
                            _newPasswordObscureText =
                                !_newPasswordObscureText; // Toggle the state of the _obscureText variable
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(
                              right:
                                  5.0), // Add some padding to the right of the icon
                          child: Icon(
                            _newPasswordObscureText
                                ? CupertinoIcons.eye
                                : CupertinoIcons.eye_slash,
                          ),
                        ),
                      ),
                    ),
                  ),
                  if (_newPasswordError != null)
                    buildError(context, _newPasswordError!),
                  const SizedBox(height: AppPadding.small),
                  SizedBox(
                    height: AppHeight.input, // Set the height of the text field
                    child: CupertinoTextField(
                      placeholder: 'New password again',
                      controller: _confirmPasswordController,
                      obscureText:
                          _confirmPasswordObscureText, // Use the _obscureText variable here
                      onChanged: (value) {
                        String? error;
                        if (value.isEmpty) {
                          error = 'Enter new password again';
                        } else if (!isValidPassword(value)) {
                          error = passwordFormatError;
                        }
                        setState(() {
                          _confirmPasswordError = error;
                        });
                      },
                      suffix: GestureDetector(
                        onTap: () {
                          setState(() {
                            _confirmPasswordObscureText =
                                !_confirmPasswordObscureText; // Toggle the state of the _obscureText variable
                          });
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(
                              right:
                                  5.0), // Add some padding to the right of the icon
                          child: Icon(
                            _confirmPasswordObscureText
                                ? CupertinoIcons.eye
                                : CupertinoIcons.eye_slash,
                          ),
                        ),
                      ),
                    ),
                  ),
                  if (_confirmPasswordError != null)
                    buildError(context, _confirmPasswordError!),
                  const SizedBox(height: AppPadding.medium),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          AppColors.primaryColor, // Set the background color
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                            5.0), // Set the border radius to 5
                      ),
                      minimumSize: const Size(
                          double.infinity, 50), // Set the width to 100%
                    ),
                    onPressed: (_newPasswordError != null ||
                            _oldPasswordError != null ||
                            _confirmPasswordError != null ||
                            _oldPasswordTextController.text.isEmpty ||
                            _newPasswordTextController.text.isEmpty ||
                            _confirmPasswordController.text.isEmpty)
                        ? null
                        : handleChangePassword,
                    child: const Text(
                      'Submit',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
