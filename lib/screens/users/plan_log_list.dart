import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import 'package:golf/screens/login_state.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/booking_req.dart';
import 'package:golf/widgets/golf_appbar.dart';
import 'package:provider/provider.dart';

class PlanLogListScreen extends StatefulWidget {
  final String planId;
  const PlanLogListScreen({super.key, required this.planId});

  @override
  _PlanLogListScreenState createState() => _PlanLogListScreenState();
}

class _PlanLogListScreenState extends LoginRequiredState<PlanLogListScreen> {
  late APIBase api;
  final List<PlanLogItem> _logs = [];
  final int _pageSize = 20;
  int _currentPage = 1;
  bool _isLoading = false;
  bool _hasMore = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    api = Provider.of<APIBase>(context, listen: false);
    _scrollController.addListener(_onScroll);
    _loadLogs();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMore) {
        _loadMoreLogs();
      }
    }
  }

  Future<void> _loadLogs() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await api.getPlanLogList(
        PlanLogListRequest(
          planId: widget.planId,
          page: 1,
          size: _pageSize,
        ),
      );

      setState(() {
        _logs.clear();
        _logs.addAll(response.items);
        _currentPage = 1;
        _hasMore = response.paginator.next > 0;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load logs: $e')),
        );
      }
    }
  }

  Future<void> _loadMoreLogs() async {
    if (_isLoading || !_hasMore) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await api.getPlanLogList(
        PlanLogListRequest(
          planId: widget.planId,
          page: _currentPage + 1,
          size: _pageSize,
        ),
      );

      setState(() {
        _logs.addAll(response.items);
        _currentPage++;
        _hasMore = response.paginator.next > 0;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load more logs: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const GolfAppBar(
        title: 'Execution Logs',
      ),
      body: RefreshIndicator(
        onRefresh: _loadLogs,
        child: _logs.isEmpty && !_isLoading
            ? const Center(
                child: Text('No logs available'),
              )
            : ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16.0),
                itemCount: _logs.length + (_hasMore ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == _logs.length) {
                    return _buildLoadingIndicator();
                  }

                  final log = _logs[index];
                  return _buildLogCard(log);
                },
              ),
      ),
    );
  }

  Widget _buildLogCard(PlanLogItem log) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12.0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    log.message,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _formatExecuteTime(log),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatExecuteTime(PlanLogItem log) {
    if (log.executeStartTime != null && log.executeEndTime != null) {
      // 提取日期和时间
      final startParts = log.executeStartTime!.split(' ');
      final endParts = log.executeEndTime!.split(' ');

      if (startParts.length >= 2 && endParts.length >= 2) {
        final startDate = startParts[0];
        final startTime = startParts[1];
        final endDate = endParts[0];
        final endTime = endParts[1];

        // 如果日期相同，只显示一次日期
        if (startDate == endDate) {
          return '$startDate $startTime ~ $endTime';
        } else {
          return '$startDate $startTime ~ $endDate $endTime';
        }
      }
      return '${log.executeStartTime} ~ ${log.executeEndTime}';
    } else if (log.executeStartTime != null) {
      return '${log.executeStartTime} ~ --';
    } else if (log.executeEndTime != null) {
      return '-- ~ ${log.executeEndTime}';
    } else {
      // 如果没有执行时间，回退到插入时间
      return log.insertTime;
    }
  }

  Widget _buildLoadingIndicator() {
    if (!_isLoading) {
      return const SizedBox.shrink();
    }
    return const Padding(
      padding: EdgeInsets.all(16.0),
      child: Center(
        child: CircularProgressIndicator(
          color: AppColors.primaryColor,
        ),
      ),
    );
  }
}
