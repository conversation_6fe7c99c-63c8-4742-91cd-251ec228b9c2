import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import 'package:golf/screens/users/club_courses.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/course_req.dart';
import 'package:golf/services/req/user.dart';
import 'package:golf/utils/exception.dart';
import 'package:golf/widgets/golf_dialog.dart';
import 'package:golf/widgets/golf_toast.dart';
import 'package:provider/provider.dart';

class LinkedClubPage extends StatefulWidget {
  final LinkedAccount account;

  const LinkedClubPage({
    super.key,
    required this.account,
  });

  @override
  _LinkedClubPageState createState() => _LinkedClubPageState();
}

class _LinkedClubPageState extends State<LinkedClubPage> {
  @override
  void initState() {
    super.initState();
  }

  Widget buildRow(BuildContext context, String key, String value) {
    return Container(
        height: 50.0,
        decoration: const BoxDecoration(
          border: Border(bottom: BorderSide(color: Colors.grey, width: 1.0)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Text(key, style: const TextStyle(fontSize: 14)),
            Text(value,
                style:
                    const TextStyle(fontSize: 14, color: AppColors.greyColor)),
          ],
        ));
  }

  void handleDelete() async {
    try {
      showLoadingDialog(context);
      final api = Provider.of<APIBase>(context, listen: false);
      await api.unbindClub(UnbindClubRequest(clubId: widget.account.id));
      Navigator.of(context).pop();
      showToast(
        context,
        msg: "remove successfully!",
      );
      Future.delayed(const Duration(seconds: 2)).then((_) {
        Navigator.of(context).pop(); // 关闭当前页面, 返回上一页。
      });
    } catch (e) {
      Navigator.of(context).pop();
      showError(e as Exception, context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.account.platform.name),
      ),
      backgroundColor: Colors.white,
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildRow(context, 'Platform', widget.account.platform.name),
            buildRow(context, 'Linked Email', widget.account.bindAccount ?? ''),
            buildRow(context, 'Linked Date', widget.account.bindDateTime ?? ''),
            buildRow(context, 'Available', widget.account.available),
            InkWell(
                onTap: () {
                  // 在这里添加点击事件的处理逻辑，例如导航到新页面
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => ClubCoursesPage(
                            clubId: widget
                                .account.platformId)), // 假设你有一个名为CoursesPage的页面
                  );
                },
                child: Container(
                    height: 50.0,
                    decoration: const BoxDecoration(
                      border: Border(
                          bottom: BorderSide(color: Colors.grey, width: 1.0)),
                    ),
                    child: const Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Text('View its courses',
                            style: TextStyle(fontSize: 14)),
                        Icon(Icons.arrow_forward),
                      ],
                    ))),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ElevatedButton(
          onPressed: () {
            handleDelete();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            minimumSize: const Size.fromHeight(50), // make the button larger
          ),
          child: const Text(
            'Remove',
            style: TextStyle(color: Colors.white),
          ),
        ),
      ),
    );
  }
}
