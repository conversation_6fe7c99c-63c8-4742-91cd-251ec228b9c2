// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import 'package:golf/screens/login_state.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/email_req.dart';
import 'package:golf/utils/exception.dart';
import 'package:golf/utils/validator.dart';
import 'package:golf/widgets/golf_appbar.dart';
import 'package:golf/widgets/golf_dialog.dart';
import 'package:golf/widgets/golf_toast.dart';
import 'package:provider/provider.dart';

class ChangeEmailScreen extends StatefulWidget {
  const ChangeEmailScreen({super.key});

  @override
  _ChangeEmailScreenState createState() => _ChangeEmailScreenState();
}

class _ChangeEmailScreenState extends LoginRequiredState<ChangeEmailScreen> {
  final _formSendKey = GlobalKey<FormState>();
  final _formVerifyKey = GlobalKey<FormState>();
  final _emailTextController = TextEditingController();
  final _passwordTextController = TextEditingController();
  final PageController _pageController = PageController();
  final _codeTextController = TextEditingController();
  String? token;
  bool _obscureText = true;
  int _countdownTime = 0;
  Timer? _timer;

  String? _emailError;
  String? _passwordError;
  String? _codeError;

  void _startCountdown() {
    setState(() {
      _countdownTime = 60;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdownTime < 1) {
          _timer?.cancel();
        } else {
          _countdownTime -= 1;
        }
      });
    });
  }

  bool _canGoBack = true;
  _onPopInvoked(bool didPop) async {
    if (_canGoBack) {
      return;
    }
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _nextPage() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeIn,
    );
  }

  @override
  void initState() {
    super.initState();
    // 监听页面变化
    _pageController.addListener(() {
      final page = _pageController.page ?? 0.0;
      setState(() {
        _canGoBack = page == 0;
      });
    });
  }

  @override
  void dispose() {
    _emailTextController.dispose();
    _passwordTextController.dispose();
    _codeTextController.dispose();
    _pageController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  Widget buildError(BuildContext context, String text) {
    return Align(
      alignment: Alignment.topLeft,
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.red,
          fontSize: 12,
        ),
      ),
    );
  }

  bool checkEmail(String email) {
    if (email.isEmpty) {
      setState(() {
        _emailError = 'Email is required';
      });
      return false;
    } else {
      if (!isValidEmail(email)) {
        setState(() {
          _emailError = 'Email format is incorrect';
        });
        return false;
      }
    }
    return true;
  }

  void handleSendEmail() async {
    if (_countdownTime > 0) {
      return;
    }
    String email = _emailTextController.text;
    if (!checkEmail(email)) {
      return;
    }
    try {
      showLoadingDialog(context);
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);
      var exist = await api.checkEmail(CheckEmailRequest(email: email));
      if (exist) {
        setState(() {
          _emailError = 'Email already exists, please use another one';
        });
        return;
      }
      SendEmailResponse response = await api.sendEmail(
          SendEmailRequest(email: email, type: EmailType.changeEmail));
      token = response.data.token;
      _startCountdown();
      _nextPage();
    } catch (e) {
      showError(e as Exception, context);
    } finally {
      Navigator.of(context).pop();
    }
  }

  void handleChangeEmail() async {
    String password = _passwordTextController.text;
    String email = _emailTextController.text;
    String code = _codeTextController.text;

    if (!checkEmail(email)) {
      return;
    }
    if (password.isEmpty) {
      setState(() {
        _passwordError = 'Password is required';
      });
      return;
    }
    if (code.isEmpty) {
      setState(() {
        _codeError = 'Code is required';
      });
      return;
    }
    if (token == null) {
      setState(() {
        _codeError = 'Please send email first';
      });
      return;
    }
    try {
      showLoadingDialog(context);
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);
      await api.changeEmail(ChangeEmailRequest(
          email: email, code: code, password: password, token: token!));
      Navigator.of(context).pop(); // 取消loading

      // tell user that password has been changed successfully
      showToast(
        context,
        msg: "Change email successfully!",
      );
      Future.delayed(const Duration(seconds: 2)).then((_) {
        Navigator.of(context).pop(); // 关闭当前页面, 返回上一页。
      });
    } catch (e) {
      Navigator.of(context).pop();
      showError(e as Exception, context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _canGoBack,
      onPopInvoked: _onPopInvoked,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: const GolfAppBar(
          title: 'Change Email',
        ),
        body: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Container(
            color: Colors.white, // Set the background color to white
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  const Text(
                    'Change Email',
                    style:
                        TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 12),
                  const Divider(
                    color: AppColors.primaryColor,
                    height: 1,
                  ),
                  Expanded(
                    child: PageView(
                      controller: _pageController,
                      children: <Widget>[
                        buildSendEmail(context),
                        buildChangedEmail(context),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget buildSendEmail(BuildContext context) {
    return Container(
      color: Colors.white, // Set the background color to white
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Form(
          key: _formSendKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const SizedBox(height: 16),
              SizedBox(
                height: AppHeight.input, // Set the height of the text field
                child: CupertinoTextField(
                  placeholder: 'New email',
                  controller: _emailTextController,
                  onChanged: (value) {
                    String? newEmailError;
                    if (value.isEmpty) {
                      newEmailError = 'Email is required';
                    } else {
                      if (!isValidEmail(value)) {
                        newEmailError = 'Email format is incorrect';
                      }
                    }
                    setState(() {
                      _emailError = newEmailError;
                    });
                  },
                ),
              ),
              if (_emailError != null) buildError(context, _emailError!),
              const SizedBox(height: 8.0),
              const Spacer(),
              SizedBox(
                width: MediaQuery.of(context).size.width,
                child: CupertinoButton(
                  padding: const EdgeInsets.symmetric(
                      horizontal:
                          12.0), // Adjust the button size by changing the padding
                  color: AppColors.primaryColor,
                  onPressed: (_emailError != null ||
                          _countdownTime > 0 ||
                          _emailTextController.text.isEmpty)
                      ? null
                      : handleSendEmail,

                  child: Text(
                    _countdownTime > 0 ? '$_countdownTime s' : 'Send',
                    style: (_emailError != null ||
                            _countdownTime > 0 ||
                            _emailTextController.text.isEmpty)
                        ? const TextStyle(color: AppColors.primaryColor)
                        : const TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildChangedEmail(BuildContext context) {
    return Container(
      color: Colors.white, // Set the background color to white
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Form(
          key: _formVerifyKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const SizedBox(height: 16),
              SizedBox(
                height: AppHeight.input, // Set the height of the text field
                child: CupertinoTextField(
                  obscureText:
                      _obscureText, // Use the _obscureText variable here

                  clearButtonMode: OverlayVisibilityMode.editing,
                  placeholder: 'Current password',
                  controller: _passwordTextController,
                  onChanged: (value) {
                    String? error;
                    if (value.isEmpty) {
                      error = 'Password is required';
                    }
                    setState(() {
                      _passwordError = error;
                    });
                  },
                  suffix: GestureDetector(
                    onTap: () {
                      setState(() {
                        _obscureText =
                            !_obscureText; // Toggle the state of the _obscureText variable
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(
                          right:
                              5.0), // Add some padding to the right of the icon
                      child: Icon(
                        _obscureText
                            ? CupertinoIcons.eye
                            : CupertinoIcons.eye_slash,
                      ),
                    ),
                  ),
                ),
              ),
              if (_passwordError != null) buildError(context, _passwordError!),
              const SizedBox(height: 8.0),
              SizedBox(
                height: AppHeight.input, // Set the height of the text field
                child: CupertinoTextField(
                  placeholder: 'New email',
                  controller: _emailTextController,
                  readOnly: true,
                ),
              ),
              if (_emailError != null) buildError(context, _emailError!),
              const SizedBox(height: 8.0),
              CupertinoTextField(
                placeholder: 'Code',
                controller: _codeTextController,
                onChanged: (value) {
                  String? newCodeError;
                  if (value.isEmpty) {
                    newCodeError = 'Code is required';
                  }

                  setState(() {
                    _codeError = newCodeError;
                  });
                },
              ),
              if (_codeError != null) buildError(context, _codeError!),
              const SizedBox(height: 16.0),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      AppColors.primaryColor, // Set the background color
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                        5.0), // Set the border radius to 5
                  ),
                  minimumSize:
                      const Size(double.infinity, 50), // Set the width to 100%
                ),
                onPressed: (_emailError != null ||
                        _passwordError != null ||
                        _codeError != null ||
                        _emailTextController.text.isEmpty ||
                        _passwordTextController.text.isEmpty ||
                        _codeTextController.text.isEmpty)
                    ? null
                    : handleChangeEmail,
                child: const Text(
                  'Submit',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
