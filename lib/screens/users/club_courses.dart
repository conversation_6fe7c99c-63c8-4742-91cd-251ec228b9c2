import 'package:flutter/material.dart';
import 'package:golf/config.dart';

import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/booking_req.dart';
import 'package:golf/services/req/course_req.dart';
import 'package:provider/provider.dart';

class ClubCoursesPage extends StatelessWidget {
  final String clubId;

  const ClubCoursesPage({super.key, required this.clubId});

  @override
  Widget build(BuildContext context) {
    APIBase api = Provider.of<APIBase>(context, listen: false);
    Future<List<SimpleCourse>> future =
        api.getClubCourses(ClubCoursesRequest(platformId: clubId));
    return Scaffold(
      appBar: AppBar(
        title: const Text('Courses'),
      ),
      backgroundColor: Colors.white,
      // should call api getClubCourses
      body: FutureBuilder<List<SimpleCourse>>(
        future: future,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
                child: CircularProgressIndicator(
              color: AppColors.primaryColor,
            ));
          }
          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }
          List<SimpleCourse> courses = snapshot.data!;
          return ListView.builder(
            itemCount: courses.length,
            itemBuilder: (context, index) {
              SimpleCourse course = courses[index];
              return Container(
                height: 50,
                decoration: const BoxDecoration(
                  border: Border(
                      bottom:
                          BorderSide(color: Colors.grey, width: 1.0)), // 添加下划线
                ),
                child: ListTile(
                  title: Text(course.name),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
