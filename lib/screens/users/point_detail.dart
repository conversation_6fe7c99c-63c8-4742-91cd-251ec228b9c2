// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/screens/login_state.dart';
import 'package:golf/services/provider/base.dart';
import 'dart:async';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:golf/services/req/point_req.dart';
import 'package:golf/services/req/payment_req.dart';
import 'package:golf/services/req/req.dart';
import 'package:provider/provider.dart';
import 'package:golf/utils/exception.dart';
import 'package:golf/services/platform/url_service.dart';

class PointDetailPage extends StatefulWidget {
  final String? orderId;
  final bool isFromPayment;

  const PointDetailPage({super.key, this.orderId, this.isFromPayment = false});

  @override
  _PointDetailPageState createState() => _PointDetailPageState();
}

class _PointDetailPageState extends LoginRequiredState<PointDetailPage> {
  List<PointDetail> details = [];
  bool isLoading = true;
  bool isCheckingPayment = false;
  String? paymentStatus;
  String? orderId;
  int _pollCount = 0; // 轮询计数器
  final int _maxPollCount = 30; // 最大轮询次数（30秒）
  String _pollStatusMessage = ''; // 轮询状态消息
  bool _isCancelled = false; // 是否取消轮询
  bool _isFromPayment = false; // 是否从支付页面返回

  final List<PointDetail> _data = [];
  final size = 10; // 修改为每页10条记录
  late Paginator paginator;
  int currentPage = 1;
  late APIBase api;
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false; // 加载更多数据
  bool _isLoadingNew = false; // 刷新
  late final URLService _urlService;

  @override
  void initState() {
    super.initState();
    _urlService = getURLService();
    _scrollController.addListener(_onScroll);
    api = Provider.of<APIBase>(context, listen: false);
    paginator = Paginator(prev: 0, next: 0, total: -1);

    // 初始化_isFromPayment标志
    _isFromPayment = widget.isFromPayment;

    // 首先从widget中获取orderId
    if (widget.orderId != null && widget.orderId!.isNotEmpty) {
      setState(() {
        orderId = widget.orderId;
        isCheckingPayment = true;
        _pollCount = 0;
        _pollStatusMessage = 'Checking payment status...';
        _isCancelled = false;
        _isFromPayment = true;
      });

      AppLogger.info('从widget获取订单ID: $orderId', 'PAYMENT');
      _checkOrderStatus(orderId!);
    }
    // 然后检查URL参数中是否有订单ID和支付结果
    else if (kIsWeb) {
      _checkPaymentResult();
    }

    _refreshData();
  }

  void _checkPaymentResult() async {
    final params = await _urlService.getQueryParameters();

    if (params.containsKey('payment_result') &&
        params.containsKey('order_id')) {
      // 清除之前的SnackBar
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      setState(() {
        isCheckingPayment = true;
        orderId = params['order_id'];
        _pollCount = 0;
        _pollStatusMessage = 'Checking payment status...';
        _isCancelled = false;
        _isFromPayment = true;
      });

      AppLogger.info('检测到支付结果参数，订单ID: $orderId', 'PAYMENT');

      // 检查支付状态参数
      if (params.containsKey('redirect_status')) {
        final status = params['redirect_status'];
        AppLogger.info('支付状态: $status', 'PAYMENT');

        setState(() {
          paymentStatus = status;
        });

        // 调用订单详情接口
        _checkOrderStatus(orderId!);
      } else {
        // 没有状态参数，也调用订单详情接口检查
        _checkOrderStatus(orderId!);
      }
    }
  }

  Future<void> _checkOrderStatus(String orderId) async {
    // 检查是否已取消轮询
    if (_isCancelled) {
      AppLogger.info('轮询已被用户取消', 'PAYMENT');
      return;
    }

    // 增加轮询计数
    _pollCount++;

    // 更新轮询状态消息
    setState(() {
      _pollStatusMessage =
          'Checking payment status... ($_pollCount/$_maxPollCount)';
    });

    // 检查是否超过最大轮询次数
    if (_pollCount > _maxPollCount) {
      AppLogger.warning('超过最大轮询次数($_maxPollCount)，停止轮询', 'PAYMENT');
      setState(() {
        isCheckingPayment = false;
        _pollStatusMessage = 'Query timeout, please try again';
      });

      // 清除之前的SnackBar
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Payment status query timeout'),
          action: SnackBarAction(
            label: 'Retry',
            onPressed: () {
              setState(() {
                isCheckingPayment = true;
                _pollCount = 0; // 重置轮询计数器
                _pollStatusMessage = 'Checking payment status...'; // 设置初始状态消息
                _isCancelled = false; // 重置取消状态
              });
              _checkOrderStatus(orderId);
            },
          ),
        ),
      );
      return;
    }

    try {
      AppLogger.info('正在查询订单状态: $orderId (第$_pollCount次尝试)', 'PAYMENT');

      // 调用订单详情接口
      final orderDetail = await api.getPaymentOrderDetail(
        PaymentOrderDetailRequest(orderId: orderId),
      );

      // 检查是否已取消轮询（可能在API调用期间被取消）
      if (_isCancelled) {
        AppLogger.info('轮询已被用户取消（API调用期间）', 'PAYMENT');
        return;
      }

      AppLogger.info('订单状态查询成功: ${orderDetail.order.status}', 'PAYMENT');
      AppLogger.info(
          '订单详情: id=${orderDetail.order.id}, uuid=${orderDetail.order.uuid}, amount=${orderDetail.order.amount}, status=${orderDetail.order.status}',
          'PAYMENT');

      // 定义状态常量
      const statusSuccess = 1;
      const statusWaiting = 2;
      const statusFailure = 3;
      const statusDeleted = 4;

      // 获取状态码（可能是字符串或数字）
      var statusCode = orderDetail.order.status;
      int? numericStatus;

      // 尝试将状态转换为数字
      try {
        numericStatus = int.tryParse(statusCode);
      } catch (e) {
        numericStatus = null;
      }

      // 根据订单状态显示不同的提示
      if (statusCode == 'success' ||
          statusCode == 'paid' ||
          statusCode == 'completed' ||
          statusCode == '1' ||
          numericStatus == statusSuccess) {
        // 支付成功状态
        AppLogger.info('订单支付成功: $statusCode', 'PAYMENT');
        setState(() {
          isCheckingPayment = false;
          _pollCount = 0; // 重置轮询计数器
        });

        // 清除之前的SnackBar
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Payment successful! Points have been added to your account'),
          ),
        );
        // 刷新积分历史
        _refreshData();
      } else if (statusCode == 'pending' ||
          statusCode == '0' ||
          statusCode == '2' ||
          numericStatus == statusWaiting) {
        // 支付处理中状态，继续轮询
        AppLogger.info('订单处理中: $statusCode，1秒后重新查询', 'PAYMENT');
        // 不显示SnackBar，只在UI上更新状态
        setState(() {
          _pollStatusMessage = 'Payment processing, please wait...';
        });
        // 1秒后重新查询
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            _checkOrderStatus(orderId);
          }
        });
      } else if (numericStatus == statusFailure || statusCode == '3') {
        // 支付失败状态
        AppLogger.error('订单支付失败: $statusCode', 'PAYMENT');
        setState(() {
          isCheckingPayment = false;
          _pollCount = 0; // 重置轮询计数器
        });

        // 清除之前的SnackBar
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Payment failed, please try again')),
        );
      } else if (numericStatus == statusDeleted || statusCode == '4') {
        // 订单已删除状态
        AppLogger.info('订单已删除: $statusCode', 'PAYMENT');
        setState(() {
          isCheckingPayment = false;
          _pollCount = 0; // 重置轮询计数器
        });

        // 清除之前的SnackBar
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Order has been deleted')),
        );
      } else {
        // 未知状态，继续轮询
        AppLogger.warning('订单状态未知: $statusCode，1秒后重新查询', 'PAYMENT');
        // 不显示SnackBar，只在UI上更新状态
        setState(() {
          _pollStatusMessage = 'Unknown status: $statusCode, checking...';
        });
        // 1秒后重新查询
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted && !_isCancelled) {
            _checkOrderStatus(orderId);
          }
        });
      }
    } catch (e) {
      // 检查是否已取消轮询
      if (_isCancelled) {
        AppLogger.info('轮询已被用户取消（错误处理期间）', 'PAYMENT');
        return;
      }

      AppLogger.error('查询订单状态失败: $e，1秒后重试', 'PAYMENT');
      // 不显示SnackBar，只在UI上更新状态
      setState(() {
        _pollStatusMessage = 'Query failed, retrying...';
      });
      // 查询失败也继续轮询
      Future.delayed(const Duration(seconds: 1), () {
        if (mounted && !_isCancelled) {
          _checkOrderStatus(orderId);
        }
      });
    }
  }

  // 取消轮询
  void _cancelPolling() {
    setState(() {
      _isCancelled = true;
      isCheckingPayment = false;
    });
    AppLogger.info('用户取消了轮询', 'PAYMENT');

    // 清除之前的SnackBar
    ScaffoldMessenger.of(context).hideCurrentSnackBar();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Payment status check cancelled')),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose(); // 不要忘记释放资源
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant PointDetailPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    _refreshData();
  }

  Future<void> _refreshData() async {
    if (_isLoadingNew) {
      return;
    }
    _isLoadingNew = true;
    var page = 1;
    try {
      var response = await api
          .getPointHistory(PointHistoryRequest(page: page, size: size));
      setState(() {
        _data.clear();
        _data.addAll(response.items);
        currentPage = page;
        paginator = response.paginator;
        _isLoadingNew = false;
        isLoading = false;
      });
    } catch (e) {
      showError(e as Exception, context);
      setState(() {
        _isLoadingNew = false;
      });
    }
  }

  void _onScroll() {
    if (!_isLoadingMore &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent) {
      _loadMoreData();
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoadingMore) {
      return;
    }
    if (_data.isEmpty) {
      return;
    }

    // 检查是否还有更多数据可加载
    if (paginator.next <= 0 || currentPage >= paginator.total) {
      AppLogger.info('没有更多数据可加载', 'UI');
      return;
    }

    setState(() {
      _isLoadingMore = true;
    });

    var page = currentPage + 1;
    try {
      AppLogger.info('加载更多数据，页码: $page', 'UI');
      var response = await api
          .getPointHistory(PointHistoryRequest(page: page, size: size));

      setState(() {
        _isLoadingMore = false;
        if (response.items.isNotEmpty) {
          _data.addAll(response.items);
          currentPage = page;
          paginator = response.paginator; // 更新分页器
          AppLogger.info(
              '加载了${response.items.length}条新数据，当前页: $currentPage，总页数: ${paginator.total}',
              'UI');
        } else {
          AppLogger.info('没有更多数据', 'UI');
        }
      });
    } catch (e) {
      AppLogger.error('加载更多数据失败: $e', 'UI');
      showError(e as Exception, context);
      setState(() {
        _isLoadingMore = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Points Consumption Details'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () {
            if (_isFromPayment) {
              // 如果是从支付页面返回，则导航到积分商店页面
              Navigator.pushReplacementNamed(context, '/user/point_store');
            } else {
              // 否则正常返回
              Navigator.pop(context);
            }
          },
        ),
      ),
      body: Stack(
        children: [
          isLoading && !isCheckingPayment
              ? const Center(
                  child: CircularProgressIndicator(
                  color: AppColors.primaryColor,
                )) // 如果正在加载，显示加载指示器
              : RefreshIndicator(
                  onRefresh: _refreshData,
                  child: Container(
                    padding: const EdgeInsets.all(8.0),
                    decoration: const BoxDecoration(
                      color: AppColors.background,
                    ),
                    child: ListView.builder(
                      controller: _scrollController,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.only(top: 8.0, bottom: 16.0),
                      itemCount: _data.length + 1, // 加1是为了在列表底部添加一个加载提示
                      itemBuilder: (context, index) {
                        if (index == _data.length) {
                          // 到达列表底部
                          if (_data.isNotEmpty && _isLoadingMore) {
                            // 检查_data是否不为空
                            return const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Center(
                                child: CircularProgressIndicator(
                                  color: AppColors.primaryColor,
                                ),
                              ),
                            ); // 显示加载指示器
                          } else if (_data.isEmpty && paginator.total == 0) {
                            return buildEmpty(context);
                          } else if (paginator.next > 0 &&
                              currentPage < paginator.total) {
                            // 还有更多数据可加载，显示"加载更多"按钮
                            return Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Center(
                                child: ElevatedButton(
                                  onPressed: _loadMoreData,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.primaryColor,
                                    foregroundColor: Colors.white,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 24.0,
                                      vertical: 12.0,
                                    ),
                                  ),
                                  child: const Text('Load More'),
                                ),
                              ),
                            );
                          } else {
                            // 没有更多数据，显示到底了的提示
                            return const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Center(
                                child: Text(
                                  'No more data',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 14.0,
                                  ),
                                ),
                              ),
                            );
                          }
                        }

                        return buildListTile(context, _data[index]);
                      },
                    ),
                  ),
                ),
          // 显示支付状态查询进度
          if (isCheckingPayment)
            Container(
              color: Colors.black.withValues(alpha: 0.5),
              child: Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text(
                      _pollStatusMessage,
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (_pollCount >= _maxPollCount) // 如果达到最大轮询次数，显示重试按钮
                          ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _pollCount = 0; // 重置轮询计数器
                                _pollStatusMessage =
                                    'Checking payment status...'; // 设置初始状态消息
                                _isCancelled = false; // 重置取消状态
                              });
                              _checkOrderStatus(orderId!);
                            },
                            child: const Text('Retry'),
                          ),
                        if (_pollCount < _maxPollCount) // 如果未达到最大轮询次数，显示取消按钮
                          ElevatedButton(
                            onPressed: _cancelPolling,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                            ),
                            child: const Text('Cancel'),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget buildEmpty(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Image.asset('assets/imgs/notification_empty.png'), // 图片路径根据实际情况修改
          const SizedBox(height: 10),
          const Text(
            "You don't have any data yet.",
            style: TextStyle(
                color: AppColors.greyColor,
                fontSize: 14,
                fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget buildListTile(BuildContext context, PointDetail detail) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
      child: Card(
        elevation: 2.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // 左侧内容区域
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      detail.description,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      detail.date,
                      style: const TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              // 右侧积分显示
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: detail.points > 0
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  detail.points > 0
                      ? '+${detail.points}'
                      : detail.points.toString(),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: detail.points < 0 ? Colors.red : Colors.green,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
