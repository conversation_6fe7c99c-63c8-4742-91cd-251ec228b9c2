// ignore_for_file: use_build_context_synchronously, library_private_types_in_public_api

import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import 'package:golf/auth_guard_observer.dart';
import 'package:golf/config.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/screens/login_state.dart';
import 'package:golf/services/provider/base.dart';

import 'package:golf/services/req/platform_req.dart';

import 'package:golf/services/req/user.dart';
import 'package:golf/utils/exception.dart';
import 'package:golf/utils/validator.dart';
import 'package:golf/widgets/golf_appbar.dart';
import 'package:golf/widgets/golf_dialog.dart';
import 'package:golf/widgets/golf_loading.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

enum LinkAccountState {
  linking, // 绑定中
  success, // 已绑定
  failed, // 绑定失败
}

typedef NotifyParentCallback = void Function(LinkAccountState newState);

class LinkAccountScreen extends StatefulWidget {
  final Platform? selectedPlatform;
  const LinkAccountScreen({super.key, this.selectedPlatform});

  @override
  _LinkAccountScreenState createState() => _LinkAccountScreenState();
}

class _LinkAccountScreenState extends LoginRequiredState<LinkAccountScreen> {
  LinkAccountState _state = LinkAccountState.linking;
  Platform? selectedPlatform;

  final _formKey = GlobalKey<FormState>();

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = ModalRoute.of(context)!.settings.arguments as RouteArguments?;
    if (args != null && args.data['selectedCourse'] != null) {
      selectedPlatform = args.data['selectedCourse'] as Platform?;
    }
  }

  void updateState(LinkAccountState newState) {
    setState(() {
      _state = newState;
    });
  }

  @override
  void initState() {
    super.initState();
    selectedPlatform = widget.selectedPlatform;
  }

  Widget buildWithState(BuildContext context) {
    switch (_state) {
      case LinkAccountState.linking:
        return LinkingAccountForm(
            selectedPlatform: selectedPlatform, updateState: updateState);
      case LinkAccountState.success:
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const SizedBox(height: 50),
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 50,
              ),
              const SizedBox(height: 8),
              const Text(
                'Account linked',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: CupertinoButton(
                  color: AppColors.primaryColor,
                  onPressed: () {
                    // 返回上一页
                    Navigator.of(context).pop();
                  },
                  child: const Text(
                    'OK',
                    style: TextStyle(
                        color: Colors.white, fontSize: AppFontSizes.small),
                  ),
                ),
              ),
            ],
          ),
        );
      case LinkAccountState.failed:
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              SizedBox(height: 50),
              Icon(
                Icons.error,
                color: Colors.red,
                size: 50,
              ),
              SizedBox(height: 8),
              Text(
                'Account link failed',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
              ),
            ],
          ),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: const GolfAppBar(
        title: 'Link account',
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Container(
            color: Colors.white, // Set the background color to white
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    const Text(
                      'Link account',
                      style: TextStyle(
                          fontSize: 18.0, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    const Divider(
                      color: AppColors.primaryColor,
                      height: 1,
                    ),
                    const SizedBox(height: 16),
                    buildWithState(context),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class LinkingAccountForm extends StatefulWidget {
  final Platform? selectedPlatform;
  final NotifyParentCallback updateState;
  const LinkingAccountForm({
    super.key,
    required this.updateState,
    this.selectedPlatform,
  });

  @override
  _LinkingAccountFormState createState() => _LinkingAccountFormState();
}

class _LinkingAccountFormState extends State<LinkingAccountForm> {
  Platform? selectedPlatform;

  bool firstLoad = true;
  LinkType linkType = LinkType.code;

  late Future<PlatformResponse> _platformListFuture;

  @override
  void initState() {
    super.initState();
    selectedPlatform = widget.selectedPlatform;
    if (selectedPlatform != null) {
      linkType = selectedPlatform!.linkType;
    }

    _platformListFuture =
        Provider.of<APIBase>(context, listen: false).getPlatformList();
  }

  Widget buildBindForm(BuildContext context) {
    switch (linkType) {
      case LinkType.code:
        return BindWithCodeForm(
            selectedPlatform: selectedPlatform,
            updateState: widget.updateState);
      case LinkType.password:
        return BindWithPasswordForm(
            selectedPlatform: selectedPlatform,
            updateState: widget.updateState);
    }
  }

  Widget buildCourseList(BuildContext context) {
    return FutureBuilder<PlatformResponse>(
        future: _platformListFuture, // Replace this with your function
        builder:
            (BuildContext context, AsyncSnapshot<PlatformResponse> snapshot) {
          if (snapshot.hasData) {
            if (selectedPlatform == null && snapshot.data!.items.isNotEmpty) {
              // 需要遍历，找到第一个未绑定的球场

              if (firstLoad) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  setState(() {
                    selectedPlatform = snapshot.data!.items.firstWhere(
                        (element) => !element.binded,
                        orElse: () => snapshot.data!.items.first);
                    firstLoad = false;
                    linkType = selectedPlatform!.linkType;
                  });
                });
              }
            }

            return Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppColors.greyColor, // Use this color
                ),
                borderRadius: BorderRadius.circular(4.0),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    isExpanded: true,
                    value: selectedPlatform!.id,
                    onChanged: (String? newValue) {
                      Platform newCourse = snapshot.data!.items
                          .firstWhere((element) => element.id == newValue);
                      if (selectedPlatform!.id == newCourse.id) {
                        return;
                      }
                      setState(() {
                        selectedPlatform = newCourse;
                        linkType = selectedPlatform!.linkType;
                      });
                    },
                    items: snapshot.data!.items
                        .map<DropdownMenuItem<String>>((Platform platform) {
                      TextStyle textStyle = platform.binded
                          ? const TextStyle(color: Colors.green) // 不可选时的样式
                          : const TextStyle(color: Colors.black); // 可选时的样式
                      return DropdownMenuItem<String>(
                        value: platform.id,
                        child: Text(
                          !platform.binded
                              ? platform.name
                              : '${platform.name}(binded)',
                          style: textStyle,
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            );
          } else if (snapshot.hasError) {
            return Text("${snapshot.error}");
          }

          // By default, show a loading spinner.
          return const GolfLoading();
          // 设置前景色
        });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Select a Account",
          style: TextStyle(fontSize: 14),
        ),
        buildCourseList(context),
        const SizedBox(height: 18.0),
        buildBindForm(context),
        const SizedBox(height: 16.0),
        const Text(
          'Help',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8.0),
        RichText(
          text: TextSpan(
            style: DefaultTextStyle.of(context).style,
            children: <TextSpan>[
              const TextSpan(text: 'Before you link your account, '),
              const TextSpan(
                text:
                    'please ensure that the email or account password you entered has been registered ',
                style: TextStyle(color: Colors.red),
              ),
              selectedPlatform != null && selectedPlatform!.website.isNotEmpty
                  ? TextSpan(
                      text: 'on the selected course website(',
                      style: const TextStyle(color: Colors.black),
                      children: [
                        TextSpan(
                          text: selectedPlatform!.website,
                          style: const TextStyle(
                              color: Colors.blue,
                              decoration: TextDecoration.underline),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              launchUrl(Uri.parse(selectedPlatform!.website));
                            },
                        ),
                        const TextSpan(
                          text: ') or App.',
                          style: TextStyle(color: Colors.black),
                        ),
                      ],
                    )
                  : const TextSpan(
                      text: 'on the selected course website or App.'),
            ],
          ),
        ),
        const SizedBox(height: AppPadding.small),
        const Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.circle,
              size: 10,
              color: AppColors.orange,
            ),
            SizedBox(
                width: 8.0), // Adjust the space between the icon and the text
            Expanded(
              child: Text(
                '1.Enter your email or account based on your selected course.',
                style: TextStyle(fontSize: 14, color: AppColors.greyColor),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppPadding.small),
        const Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.circle,
              size: 10,
              color: AppColors.orange,
            ),
            SizedBox(
                width: 8.0), // Adjust the space between the icon and the text
            Expanded(
              child: Text(
                '2.Base on your selected course, click send to get code that will be sent to your email then enter it or enter your password.',
                style: TextStyle(fontSize: 14, color: AppColors.greyColor),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppPadding.small),
        const Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.circle,
              size: 10,
              color: AppColors.orange,
            ),
            SizedBox(
                width: 8.0), // Adjust the space between the icon and the text
            Expanded(
              child: Text(
                '3.Click the submit button to complete account link.\nIf you need to change the linked account, please remove it first.',
                style: TextStyle(fontSize: 14, color: AppColors.greyColor),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class BindWithCodeForm extends StatefulWidget {
  final Platform? selectedPlatform; // 绑定哪个球场
  final NotifyParentCallback updateState;
  const BindWithCodeForm({
    super.key,
    required this.updateState,
    this.selectedPlatform,
  });

  @override
  BindWithCodeState createState() => BindWithCodeState();
}

class BindWithCodeState extends State<BindWithCodeForm> {
  Platform? selectedPlatform;

  final _emailTextController = TextEditingController();
  final _codeTextController = TextEditingController();
  final _pageController = PageController();
  int _countdownTime = 0;
  Timer? _timer;
  String? _emailError;

  String? _codeError;
  @override
  void initState() {
    super.initState();
    selectedPlatform = widget.selectedPlatform;
  }

  @override
  void dispose() {
    _emailTextController.dispose();
    _codeTextController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(BindWithCodeForm oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedPlatform != widget.selectedPlatform) {
      selectedPlatform = widget.selectedPlatform;
      _timer?.cancel();
      _countdownTime = 0;
    }
  }

  void _startCountdown() {
    setState(() {
      _countdownTime = 60;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdownTime < 1) {
          _timer?.cancel();
        } else {
          _countdownTime -= 1;
        }
      });
    });
  }

  void _nextPage() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeIn,
    );
  }

  var errCount = 0;
  void handleSendEmail() async {
    if (_countdownTime > 0) {
      return;
    }
    String email = _emailTextController.text;

    if (email.isEmpty) {
      setState(() {
        _emailError = 'Email is required';
      });
      return;
    } else {
      if (!isValidEmail(email)) {
        setState(() {
          _emailError = 'Email format is incorrect';
        });
        return;
      }
    }
    try {
      showLoadingDialog(context);
      // Get the API instance from the context
      final api = Provider.of<APIBase>(context, listen: false);
      await api.bindSendEmail(
          BindSendEmailRequest(email: email, platformId: selectedPlatform!.id));
      _startCountdown();
      Navigator.of(context).pop();
      _nextPage();
    } on Exception catch (e) {
      errCount++;
      Navigator.of(context).pop();
      if (errCount < 3) {
        if (RegExp(r'invalid email', caseSensitive: false)
            .hasMatch(e.toString())) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: const Text('tips'),
                content: RichText(
                  text: TextSpan(
                    text: 'The current email is not registered on ',
                    style: const TextStyle(color: Colors.black),
                    children: [
                      TextSpan(
                        text: selectedPlatform!.website,
                        style: const TextStyle(
                            color: Colors.blue,
                            decoration: TextDecoration.underline),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            launchUrl(Uri.parse(selectedPlatform!.website));
                          },
                      ),
                      const TextSpan(
                        text:
                            ' Please check for any spelling errors, or visit the website to register before completing the binding.',
                        style: TextStyle(color: Colors.black),
                      ),
                    ],
                  ),
                ),
                actions: <Widget>[
                  TextButton(
                    child: const Text('Ok'),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              );
            },
          );
        } else {
          showError(e, context);
        }
      } else {
        errCount = 0;
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('tips'),
              content: const Text(
                  'Multiple failed attempts. Please check if your email is registered, or visit the website to recover your password.'),
              actions: <Widget>[
                TextButton(
                  child: const Text('Ok'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      }
    }
  }

  Widget buildError(BuildContext context, String text) {
    return Align(
      alignment: Alignment.topLeft,
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.red,
          fontSize: 12,
        ),
      ),
    );
  }

  bool canSubmit() {
    return _emailError == null &&
        _codeError == null &&
        _emailTextController.text.isNotEmpty &&
        _codeTextController.text.isNotEmpty &&
        selectedPlatform != null;
  }

  void handleLinkAccount() async {
    String email = _emailTextController.text;
    String code = _codeTextController.text;

    if (email.isEmpty) {
      setState(() {
        _emailError = 'Email is required';
      });
      return;
    } else {
      if (!isValidEmail(email)) {
        setState(() {
          _emailError = 'Email format is incorrect';
        });
        return;
      }
    }
    if (code.isEmpty) {
      setState(() {
        _codeError = 'Code is required';
      });
      return;
    }

    if (selectedPlatform == null) {
      showError(Exception('Please select a Account'), context);
      return;
    }

    try {
      showLoadingDialog(context);
      final api = Provider.of<APIBase>(context, listen: false);
      await api.linkAccountByCode(LinkAccountByCodeRequest(
          email: email, code: code, platformId: selectedPlatform!.id));
      Navigator.of(context).pop();
      widget.updateState(LinkAccountState.success);
    } catch (e) {
      Navigator.of(context).pop();
      showError(e as Exception, context);
    }
  }

  Widget buildSendForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Enter your email:',
          style: TextStyle(fontSize: 14),
        ),
        SizedBox(
          height: AppHeight.input, // Set the height of the text field
          child: CupertinoTextField(
            placeholder: 'Your email',
            controller: _emailTextController,
            onChanged: (value) {
              String? newEmailError;
              if (value.isEmpty) {
                newEmailError = 'Email is required';
              } else {
                if (!isValidEmail(value)) {
                  newEmailError = 'Email format is incorrect';
                }
              }

              setState(() {
                _emailError = newEmailError;
              });
            },
          ),
        ),
        if (_emailError != null) buildError(context, _emailError!),
        const SizedBox(height: 8.0),
        // Add some space between the text field and the button
        SizedBox(
          height: AppHeight.input, // Set the height of the button
          width: MediaQuery.of(context).size.width, //
          child: CupertinoButton(
            padding: const EdgeInsets.symmetric(
                horizontal:
                    12.0), // Adjust the button size by changing the padding
            color: AppColors.primaryColor,
            onPressed: (_emailError != null ||
                    _countdownTime > 0 ||
                    _emailTextController.text.isEmpty)
                ? null
                : () {
                    handleSendEmail();
                  },
            child:
                Text(_countdownTime > 0 ? '$_countdownTime s' : 'Get Started'),
          ),
        ),
        if (_codeError != null) buildError(context, _codeError!),
      ],
    );
  }

  Widget buildBindForm(BuildContext context) {
    AppLogger.info('BindWithCodeState build', 'UI');

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Row(
        children: [
          Expanded(
            child: Text(
              'Enter the code received at ${_emailTextController.text} to continue',
              style: const TextStyle(fontSize: 14),
            ),
          ),
          CupertinoButton(
            onPressed: () {
              _pageController.previousPage(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeIn,
              );
            },
            child: const Text('Change'),
          ),
        ],
      ),
      if (_emailError != null) buildError(context, _emailError!),
      const SizedBox(height: 8.0),
      SizedBox(
        child: CupertinoTextField(
          controller: _codeTextController,
          placeholder: 'Code',
          onChanged: (value) {
            String? newCodeError;
            if (value.isEmpty) {
              newCodeError = 'Code is required';
            }

            setState(() {
              _codeError = newCodeError;
            });
          },
        ),
      ),
      if (_codeError != null) buildError(context, _codeError!),
      const SizedBox(height: 16.0),
      SizedBox(
        width: double.infinity,
        child: CupertinoButton(
          color: AppColors.primaryColor,
          onPressed: !canSubmit()
              ? null
              : () {
                  handleLinkAccount();
                },
          child: const Text(
            'Submit',
            style: TextStyle(color: Colors.white),
          ),
        ),
      ),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    AppLogger.info('BindWithCodeState build', 'UI');
    // if selectedCourse is null, show error
    if (selectedPlatform == null) {
      return const Text('no course avaiable');
    }
    return SizedBox(
      height: 200,
      child: PageView(
        controller: _pageController,
        children: [
          buildSendForm(context),
          buildBindForm(context),
        ],
      ),
    );
  }
}

class BindWithPasswordForm extends StatefulWidget {
  final Platform? selectedPlatform; // 绑定哪个球场
  final NotifyParentCallback updateState;

  const BindWithPasswordForm({
    super.key,
    required this.updateState,
    this.selectedPlatform,
  });

  @override
  BindWithPasswordState createState() => BindWithPasswordState();
}

class BindWithPasswordState extends State<BindWithPasswordForm> {
  Platform? selectedPlatform;

  final _emailTextController = TextEditingController();
  final _passwordTextController = TextEditingController();
  bool _obscureText = true;

  String? _emailError;

  String? _passwordError;
  @override
  void initState() {
    super.initState();
    selectedPlatform = widget.selectedPlatform;
  }

  @override
  void dispose() {
    _emailTextController.dispose();
    _passwordTextController.dispose();

    super.dispose();
  }

  @override
  void didUpdateWidget(BindWithPasswordForm oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedPlatform != widget.selectedPlatform) {
      selectedPlatform = widget.selectedPlatform;
    }
  }

  Widget buildError(BuildContext context, String text) {
    return Align(
      alignment: Alignment.topLeft,
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.red,
          fontSize: 12,
        ),
      ),
    );
  }

  bool canSubmit() {
    return _emailError == null &&
        _passwordError == null &&
        _emailTextController.text.isNotEmpty &&
        _passwordTextController.text.isNotEmpty &&
        selectedPlatform != null;
  }

  var errCount = 0;
  void handleLinkAccount() async {
    String email = _emailTextController.text;
    String password = _passwordTextController.text;

    if (email.isEmpty) {
      setState(() {
        _emailError = 'Email is required';
      });
      return;
    } else {
      if (!isValidEmail(email)) {
        setState(() {
          _emailError = 'Email format is incorrect';
        });
        return;
      }
    }
    if (password.isEmpty) {
      setState(() {
        _passwordError = 'Password is required';
      });
      return;
    }

    if (selectedPlatform == null) {
      showError(Exception('Please select a Account'), context);
      return;
    }

    try {
      showLoadingDialog(context);
      final api = Provider.of<APIBase>(context, listen: false);
      await api.linkAccountByPassword(LinkAccountByPasswordRequest(
          email: email, password: password, platformId: selectedPlatform!.id));
      Navigator.of(context).pop();
      widget.updateState(LinkAccountState.success);
    } catch (e) {
      errCount++;
      Navigator.of(context).pop();
      if (errCount < 3) {
        if (RegExp(r'invalid email', caseSensitive: false)
            .hasMatch(e.toString())) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: const Text('tips'),
                content: RichText(
                  text: TextSpan(
                    text: 'The current email is not registered on ',
                    style: const TextStyle(color: Colors.black),
                    children: [
                      TextSpan(
                        text: selectedPlatform!.website,
                        style: const TextStyle(
                            color: Colors.blue,
                            decoration: TextDecoration.underline),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            launchUrl(Uri.parse(selectedPlatform!.website));
                          },
                      ),
                      const TextSpan(
                        text:
                            ' Please check for any spelling errors, or visit the website to register before completing the binding.',
                        style: TextStyle(color: Colors.black),
                      ),
                    ],
                  ),
                ),
                actions: <Widget>[
                  TextButton(
                    child: const Text('Ok'),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ],
              );
            },
          );
        } else {
          showError(e as Exception, context);
        }
      } else {
        errCount = 0;
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('tips'),
              content: const Text(
                  'Multiple failed attempts. Please check if your email is registered, or visit the website to recover your password.'),
              actions: <Widget>[
                TextButton(
                  child: const Text('Ok'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (selectedPlatform == null) {
      return const Text('no course avaiable');
    }
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      const Text(
        'Enter your email:',
        style: TextStyle(fontSize: 14),
      ),
      SizedBox(
        height: AppHeight.input, // Set the height of the text field
        child: CupertinoTextField(
          placeholder: 'Your email',
          controller: _emailTextController,
          onChanged: (value) {
            String? newEmailError;
            if (value.isEmpty) {
              newEmailError = 'Email is required';
            } else {
              if (!isValidEmail(value)) {
                newEmailError = 'Email format is incorrect';
              }
            }

            setState(() {
              _emailError = newEmailError;
            });
          },
        ),
      ),
      if (_emailError != null) buildError(context, _emailError!),
      const SizedBox(height: 8.0),
      Row(
        children: [
          Expanded(
            child: SizedBox(
              height: AppHeight.input, // Set the height of the text field
              child: CupertinoTextField(
                controller: _passwordTextController,
                obscureText: _obscureText, // Use the _obscureText variable here

                clearButtonMode: OverlayVisibilityMode.editing,
                placeholder: 'Password',
                onChanged: (value) {
                  String? newError;
                  if (value.isEmpty) {
                    newError = 'Password is required';
                  }

                  setState(() {
                    _passwordError = newError;
                  });
                },
                suffix: GestureDetector(
                  onTap: () {
                    setState(() {
                      _obscureText =
                          !_obscureText; // Toggle the state of the _obscureText variable
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(
                        right:
                            5.0), // Add some padding to the right of the icon
                    child: Icon(
                      _obscureText
                          ? CupertinoIcons.eye
                          : CupertinoIcons.eye_slash,
                    ),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(
              width:
                  8.0), // Add some space between the text field and the button
        ],
      ),
      if (_passwordError != null) buildError(context, _passwordError!),
      const SizedBox(height: 16.0),
      SizedBox(
        width: double.infinity,
        child: CupertinoButton(
          color: AppColors.primaryColor,
          onPressed: !canSubmit()
              ? null
              : () {
                  handleLinkAccount();
                },
          child: const Text(
            'Submit',
            style: TextStyle(color: Colors.white),
          ),
        ),
      ),
    ]);
  }
}
