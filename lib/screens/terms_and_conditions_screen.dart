import 'package:flutter/material.dart';
import 'package:golf/config.dart';

class TermsAndConditionsScreen extends StatelessWidget {
  const TermsAndConditionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Terms'),
      ),
      body: const Padding(
        padding: EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Terms',
                style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Divider(
                color: AppColors.primaryColor,
                height: 1,
              ),
              SizedBox(height: 16),
              Text(
                'Acceptance of Terms',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: Colors.black),
              ),
              Text(
                'By accessing or using Tee time bot, you signify that you have read, understood, and agree to be bound by the terms and any future updates. If you do not agree with these terms, please discontinue your use of the website immediately.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Service Description',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'Tee time bot provides tee time reservation service. We reserve the right to modify or terminate our services at any time without prior notice.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'User Obligations',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'Users must provide accurate, current, and complete information when creating an account and maintain its accuracy throughout their use of the site. Users are responsible for keeping their login credentials secure and will not share them with third parties. Users also agree not to use the site for any illegal or unauthorized purposes.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Intellectual Property',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                "All content on Tee time bot, including text, graphics, logos, images, audio, video, software, and other materials, is owned by or licensed to us. Users are granted a limited, non-exclusive, non-transferable license to access and use the site's content for personal, non-commercial purposes only.",
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Privacy',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'Your privacy is important to us. Please review our Privacy Policy which explains how we collect, use, and protect your personal information.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Liability',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'To the fullest extent permitted by law, Tee time bot shall not be liable for any indirect, incidental, special, or consequential damages arising out of the use or inability to use the service.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Termination',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'We reserve the right to terminate user accounts at our sole discretion without prior notice if we believe users have violated this agreement or applicable laws.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Governing Law',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'This User Agreement shall be governed by and construed in accordance with the laws of U.S..',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Changes to Agreement',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'We may update or modify this Agreement from time to time without prior notice. Your continued use of Tee time bot after such changes constitutes acceptance of the updated Agreement.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Contact Information',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'For questions or concerns about this User Agreement, please contact <NAME_EMAIL>',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
