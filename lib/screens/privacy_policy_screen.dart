import 'package:flutter/material.dart';
import 'package:golf/config.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Privacy Policy'),
      ),
      body: const Padding(
        padding: EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Privacy Policy',
                style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Divider(
                color: AppColors.primaryColor,
                height: 1,
              ),
              SizedBox(height: 16),
              Text(
                'Introduction',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: Colors.black),
              ),
              Text(
                'We respect and value the privacy of our users. This Privacy Policy explains how we collect, use, disclose, and protect your personal information when you visit or use our website and services. By using our site, you consent to the data practices described in this policy.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Information Collection',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'We may collect personal information such as your name, email address, IP address, device information, and usage data when you create an account, interact with our services, or provide it voluntarily. Automatically collected information includes details about your interaction with our site, browser type, and operating system.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Use of Information',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'We use your personal information for purposes including but not limited to: Providing and improving our services Authenticating and verifying user accounts Communicating with you about your account, transactions, and updates to our policies Conducting analytics and research to improve user experience',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Sharing of Information',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                "We do not sell or rent your personal information to third parties without your explicit consent. However, we may share your information with trusted third-party service providers who assist us in operating our website, conducting our business, or serving you, so long as those parties agree to keep this information confidential.",
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Cookies and Tracking Technologies',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'Our website uses cookies and similar technologies to store preferences, record session information, and personalize your online experience. You can manage your cookie settings through your browser settings.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Security',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'We implement a variety of security measures to maintain the safety of your personal information. However, no method of transmission over the internet or electronic storage is 100% secure, so while we strive to use commercially acceptable means to protect your personal information, we cannot guarantee its absolute security.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Data Retention',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'We will retain your personal information only for as long as necessary to fulfill the purposes outlined in this Privacy Policy unless a longer retention period is required or permitted by law.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Your Rights',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'You have the right to access, update, correct, or delete your personal information. Additionally, you can opt out of certain communications from us or request that we restrict processing of your data.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
              SizedBox(height: 16),
              Text(
                'Changes to this Policy',
                style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w500),
              ),
              Text(
                'We reserve the right to update this Privacy Policy at any time. Changes will take effect immediately upon posting on our website. Your continued use of our services following the posting of changes constitutes your acceptance of those changes.',
                style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500,
                    color: AppColors.greyColor),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
