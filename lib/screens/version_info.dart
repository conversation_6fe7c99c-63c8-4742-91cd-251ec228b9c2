import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:golf/config.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// 版本信息显示页面
/// 动态从 pubspec.yaml 读取版本信息，避免硬编码
class VersionInfoScreen extends StatefulWidget {
  const VersionInfoScreen({super.key});

  @override
  State<VersionInfoScreen> createState() => _VersionInfoScreenState();
}

class _VersionInfoScreenState extends State<VersionInfoScreen> {
  /// 应用包信息
  PackageInfo? _packageInfo;

  /// 是否正在加载
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initPackageInfo();
  }

  /// 初始化包信息
  /// 从系统获取应用的版本号、构建号等信息
  /// Web 平台使用备用方案，移动端使用 PackageInfo
  Future<void> _initPackageInfo() async {
    try {
      if (kIsWeb) {
        // Web 平台：使用备用方案
        _packageInfo = _createWebPackageInfo();
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      } else {
        // 移动端和桌面端：使用 PackageInfo
        final info = await PackageInfo.fromPlatform();
        if (mounted) {
          setState(() {
            _packageInfo = info;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      // 错误处理：如果获取失败，使用备用方案
      if (mounted) {
        setState(() {
          _packageInfo = _createWebPackageInfo();
          _isLoading = false;
        });
      }
    }
  }

  /// 为 Web 平台创建包信息
  /// 使用 AppSettings 中的配置信息，确保 web 兼容性
  PackageInfo _createWebPackageInfo() {
    return PackageInfo(
      appName: AppSettings.appName,
      packageName: AppSettings.packageName,
      version: AppSettings.appVersion,
      buildNumber: '', // Web 端不显示构建号
      buildSignature: '',
      installerStore: null,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                borderRadius: BorderRadius.circular(25),
              ),
              child: const Icon(
                Icons.golf_course,
                size: 60,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              AppSettings.appName,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            _buildVersionText(),
            const SizedBox(height: 100),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                '© ${DateTime.now().year} Rich Pearl Limited Corporation. All Rights Reserved.',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建版本显示文本
  /// 根据加载状态显示不同内容
  Widget _buildVersionText() {
    if (_isLoading) {
      return const SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      );
    }

    String versionText = 'Version ';
    if (_packageInfo != null) {
      versionText += _packageInfo!.version;
      // 如果有构建号且不等于版本号，才显示出来
      if (_packageInfo!.buildNumber.isNotEmpty &&
          _packageInfo!.buildNumber != _packageInfo!.version) {
        versionText += ' (${_packageInfo!.buildNumber})';
      }
    } else {
      // 获取失败时的fallback
      versionText += 'Unknown';
    }

    return Text(
      versionText,
      style: const TextStyle(
        fontSize: 14,
        color: Colors.grey,
      ),
    );
  }
}
