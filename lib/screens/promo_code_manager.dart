import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:golf/config.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/coupon_req.dart';
import 'package:golf/widgets/golf_toast.dart';

import 'package:golf/utils/exception.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/cupertino.dart';

/// 优惠码状态常量
class CouponStatus {
  static const int success = 1; // STATUS_SUCCESS
  static const int pending = 2; // STATUS_PENDING
  static const int failure = 3; // STATUS_FAILURE
}

/// 优惠码管理页面
class PromoCodeManagerPage extends StatefulWidget {
  const PromoCodeManagerPage({super.key});

  @override
  State<PromoCodeManagerPage> createState() => _PromoCodeManagerPageState();
}

class _PromoCodeManagerPageState extends State<PromoCodeManagerPage> {
  final TextEditingController _codeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final FocusNode _codeFocusNode = FocusNode();

  List<CouponDetail> _coupons = [];
  bool _isLoading = false;
  bool _isSubmitting = false;
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _loadCoupons();
  }

  @override
  void dispose() {
    _codeController.dispose();
    _codeFocusNode.dispose();
    super.dispose();
  }

  /// 加载优惠码列表
  Future<void> _loadCoupons() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final api = Provider.of<APIBase>(context, listen: false);
      final request = CouponListRequest(page: 1, size: 100); // 不需要分页，获取所有
      final response = await api.getCouponList(request);

      setState(() {
        _coupons = response.items;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        showError(e as Exception, context);
      }
    }
  }

  /// 添加新优惠码
  Future<void> _addCoupon() async {
    // 手动校验输入内容，兼容 Cupertino 风格
    if (_codeController.text.trim().isEmpty) {
      showToast(context, msg: "Promo code cannot be empty");
      return;
    }
    // 新增：有效期必填校验
    if (_selectedDate == null) {
      showToast(context, msg: "Please select expiry date");
      return;
    }
    setState(() {
      _isSubmitting = true;
    });

    try {
      final api = Provider.of<APIBase>(context, listen: false);

      // 将日期转换为datetime字符串
      String? expiryDate;
      if (_selectedDate != null) {
        // 设置时间为23:59:59
        final expiryDateTime = DateTime(
          _selectedDate!.year,
          _selectedDate!.month,
          _selectedDate!.day,
          23,
          59,
          59,
        );
        expiryDate = DateFormat('yyyy-MM-dd HH:mm:ss').format(expiryDateTime);
      }

      final request = AddCouponRequest(
        code: _codeController.text.trim(),
        expiryDate: expiryDate,
      );

      final response = await api.addCoupon(request);
      if (response.code == 0) {
        // 弹窗提示成功
        if (mounted) {
          await showCupertinoDialog(
            context: context,
            builder: (context) => CupertinoAlertDialog(
              title: const Text("Success"),
              content: const Text("Promo code added successfully!"),
              actions: [
                CupertinoDialogAction(
                  child: const Text("OK"),
                  onPressed: () async {
                    Navigator.of(context).pop();
                    setState(() {
                      _codeController.clear();
                      _selectedDate = null;
                    });
                    await _loadCoupons();
                  },
                ),
              ],
            ),
          );
        }
      } else {
        // 弹窗提示失败，内容用 response.msg
        if (mounted) {
          await showCupertinoDialog(
            context: context,
            builder: (context) => CupertinoAlertDialog(
              title: const Text("Error"),
              content: Text(response.msg ?? "Failed to add promo code"),
              actions: [
                CupertinoDialogAction(
                  child: const Text("OK"),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        showError(e, context);
      }
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  /// 选择日期
  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppColors.primaryColor,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  /// 格式化日期显示
  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat('yyyy-MM-dd').format(date);
    } catch (e) {
      return dateString;
    }
  }

  /// 获取状态文本
  String _getStatusText(String status) {
    final statusInt = int.tryParse(status) ?? 0;
    switch (statusInt) {
      case CouponStatus.success:
        return 'Valid';
      case CouponStatus.pending:
        return 'Not verified';
      case CouponStatus.failure:
        return 'Invalid';
      default:
        return 'Unknown';
    }
  }

  /// 构建优惠码列表项
  Widget _buildCouponItem(CouponDetail coupon) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: _getStatusColor(coupon.status).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _getStatusColor(coupon.status),
              width: 2,
            ),
          ),
          child: Icon(
            Icons.local_offer,
            color: _getStatusColor(coupon.status),
            size: 20,
          ),
        ),
        title: Text(
          coupon.code,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (coupon.expiryDate.isNotEmpty)
              Text(
                'Valid until: ${_formatDate(coupon.expiryDate)}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            // 显示状态标签
            Container(
              margin: const EdgeInsets.only(top: 4),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getStatusColor(coupon.status).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _getStatusColor(coupon.status).withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Text(
                coupon.statusText.isNotEmpty
                    ? coupon.statusText
                    : _getStatusText(coupon.status),
                style: TextStyle(
                  color: _getStatusColor(coupon.status),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      ),
    );
  }

  /// 获取状态颜色
  Color _getStatusColor(String status) {
    final statusInt = int.tryParse(status) ?? 0;
    switch (statusInt) {
      case CouponStatus.success:
        return Colors.green;
      case CouponStatus.pending:
        return Colors.orange;
      case CouponStatus.failure:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  KeyboardActionsConfig _buildKeyboardActionsConfig() {
    return KeyboardActionsConfig(
      keyboardActionsPlatform: KeyboardActionsPlatform.IOS,
      actions: [
        KeyboardActionsItem(
          focusNode: _codeFocusNode,
          toolbarButtons: [
            (node) {
              return GestureDetector(
                onTap: () => node.unfocus(),
                child: const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text('Done', style: TextStyle(color: Colors.blue)),
                ),
              );
            }
          ],
        ),
      ],
    );
  }

  Widget _buildForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Promo Code *',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        CupertinoTextField(
          controller: _codeController,
          focusNode: _codeFocusNode,
          placeholder: 'Enter promo code',
          prefix: const Padding(
            padding: EdgeInsets.only(left: 8),
            child: Icon(CupertinoIcons.tag, color: CupertinoColors.systemGrey),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          clearButtonMode: OverlayVisibilityMode.editing,
          decoration: BoxDecoration(
            color: CupertinoColors.systemGrey6,
            borderRadius: BorderRadius.circular(8),
          ),
          style: const TextStyle(fontSize: 16),
        ),
        const SizedBox(height: 16),
        const Text('Expiry Date *',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
        const SizedBox(height: 4),
        const Text('Required',
            style: TextStyle(color: Colors.red, fontSize: 13)),
        CupertinoButton(
          padding: const EdgeInsets.symmetric(horizontal: 0),
          color: CupertinoColors.systemGrey6,
          borderRadius: BorderRadius.circular(8),
          onPressed: _selectCupertinoDate,
          child: Row(
            children: [
              const Icon(CupertinoIcons.calendar,
                  color: CupertinoColors.systemGrey),
              const SizedBox(width: 8),
              Text(
                _selectedDate != null
                    ? DateFormat('yyyy-MM-dd').format(_selectedDate!)
                    : 'Select expiry date',
                style: TextStyle(
                  color: _selectedDate != null
                      ? CupertinoColors.black
                      : CupertinoColors.systemGrey,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 20),
        SizedBox(
          width: double.infinity,
          child: CupertinoButton(
            borderRadius: BorderRadius.circular(12),
            padding: const EdgeInsets.symmetric(vertical: 16),
            color: AppColors.primaryColor,
            onPressed: _isSubmitting ? null : _addCoupon,
            child: _isSubmitting
                ? const CupertinoActivityIndicator()
                : const Text('Submit & Verify',
                    style: TextStyle(color: Colors.white)),
          ),
        ),
      ],
    );
  }

  void _selectCupertinoDate() {
    final now = DateTime.now();
    // 只保留日期部分，去掉时分秒，避免毫秒误差
    DateTime minDate = DateTime(now.year, now.month, now.day);
    DateTime initial = _selectedDate ?? minDate;
    if (initial.isBefore(minDate)) {
      initial = minDate;
    }
    showCupertinoModalPopup(
      context: context,
      builder: (_) => Container(
        height: 300,
        color: CupertinoColors.systemBackground.resolveFrom(context),
        child: Column(
          children: [
            SizedBox(
              height: 200,
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.date,
                initialDateTime: initial,
                minimumDate: minDate,
                maximumDate: minDate.add(const Duration(days: 365)),
                onDateTimeChanged: (date) {
                  setState(() {
                    _selectedDate = date;
                  });
                },
              ),
            ),
            CupertinoButton(
              child: const Text('Done'),
              onPressed: () {
                if (_selectedDate == null) {
                  setState(() {
                    _selectedDate = initial;
                  });
                }
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Manage Promo Codes'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // 优惠码列表，Expanded自适应高度
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _coupons.isEmpty
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.local_offer_outlined,
                              size: 64,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'No promo codes yet',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            SizedBox(height: 8),
                            Text(
                              'Add your first promo code below',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _loadCoupons,
                        child: ListView.builder(
                          itemCount: _coupons.length,
                          itemBuilder: (context, index) {
                            return _buildCouponItem(_coupons[index]);
                          },
                        ),
                      ),
          ),
          // 表单区域，固定在底部
          Padding(
            padding: const EdgeInsets.all(16),
            child: _buildForm(),
          ),
        ],
      ),
    );
  }
}
