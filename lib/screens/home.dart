// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:golf/services/provider/auth_notifier.dart';
import 'package:golf/config.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/screens/about.dart';
import 'package:golf/screens/version_info.dart';
import 'package:golf/screens/message_center.dart';
import 'package:golf/screens/home_form.dart';
import 'package:golf/screens/users/linked_account.dart';
import 'package:golf/screens/users/notifications.dart';
import 'package:golf/screens/users/plans.dart';
import 'package:golf/screens/users/points.dart';
import 'package:golf/screens/users/settings.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/provider/pagestate_provider.dart';
import 'package:golf/services/req/booking_req.dart';
import 'package:golf/services/req/notify_req.dart';
import 'package:golf/services/message_service.dart';
import 'package:golf/widgets/golf_appbar.dart';
import 'package:golf/services/platform/platform_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:provider/provider.dart';
import 'dart:async';

final today = DateUtils.dateOnly(DateTime.now());

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool isDatePickerVisible = false;
  bool isLoggedIn = false;
  PageState state = PageState.home;

  late AuthNotifier authNotifier;
  late PageStateNotifier pageStateProvider;
  late final PlatformService _platformService;
  late final MessageService _messageService;
  BookingStatus selectedBookingStatus = BookingStatus.all;
  NotificationStatus selectedNotificationStatus = NotificationStatus.all;

  Timer? _versionCheckTimer;
  bool _updateDialogShowing = false;
  bool _isCheckingVersion = false; // 添加互斥锁

  // 为 KeyboardActions 添加的 FocusNode
  late final FocusNode _customPlayersFocusNode;
  late final FocusNode _budgetFocusNode;

  // 构建 KeyboardActions 配置
  KeyboardActionsConfig _buildKeyboardActionsConfig(BuildContext context) {
    return KeyboardActionsConfig(
      keyboardActionsPlatform: KeyboardActionsPlatform.IOS, // 只在 iOS 上使用
      keyboardBarColor: Colors.grey[200],
      nextFocus: true,
      actions: [
        KeyboardActionsItem(
          focusNode: _customPlayersFocusNode,
          toolbarButtons: [
            (node) {
              return GestureDetector(
                onTap: () => node.unfocus(),
                child: const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text(
                    "Done",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              );
            }
          ],
        ),
        KeyboardActionsItem(
          focusNode: _budgetFocusNode,
          toolbarButtons: [
            (node) {
              return GestureDetector(
                onTap: () => node.unfocus(),
                child: const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text(
                    "Done",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              );
            }
          ],
        ),
      ],
    );
  }

  @override
  void initState() {
    super.initState();

    // --- 新增：尝试在早期检查网络 ---
    _performInitialNetworkCheck();
    // --- 结束新增 ---

    // 初始化 FocusNode
    _customPlayersFocusNode = FocusNode(debugLabel: 'customPlayers');
    _budgetFocusNode = FocusNode(debugLabel: 'budget');

    _platformService = getPlatformService();
    _messageService = MessageService();
    AppLogger.info('Home initState(${widget.key}): state: $state', 'UI');
    pageStateProvider = context.read<PageStateNotifier>();
    state = pageStateProvider.currentState;
    authNotifier = context.read<AuthNotifier>();
    authNotifier.addListener(onAuthChanged);
    pageStateProvider.addListener(onStateChanged);
    _messageService.addListener(_onMessageServiceChanged);

    // 初始化消息服务（先不传API，在登录后再同步）
    _messageService.initialize();
  }

  // 新增方法：执行初始网络检查
  Future<void> _performInitialNetworkCheck() async {
    AppLogger.info("Performing initial network check...", 'SYSTEM');
    try {
      var connectivityResult = await (Connectivity().checkConnectivity());
      if (!connectivityResult.contains(ConnectivityResult.mobile) &&
          !connectivityResult.contains(ConnectivityResult.wifi)) {
        AppLogger.warning(
            "Initial check: No active network connection detected.", 'SYSTEM');
      } else {
        AppLogger.info(
            "Initial check: Active network connection detected.", 'SYSTEM');
      }
    } catch (e) {
      AppLogger.warning(
          "Initial network check/activity failed (ignored): $e", 'SYSTEM');
    } finally {
      AppLogger.info(
          "Initial network check finished, proceeding with other initState logic.",
          'SYSTEM');
      onAuthChanged();
      if (kIsWeb) {
        _startVersionCheck();
      }
    }
  }

  onAuthChanged() async {
    final api = Provider.of<APIBase>(context, listen: false);
    api.isLoggedIn().then((value) {
      AppLogger.info('isLoggedIn: $value', 'AUTH');
      if (isLoggedIn != value) {
        setState(() {
          isLoggedIn = value;
        });

        // 如果用户登录，同步未读消息数量
        if (value) {
          _messageService.syncUnreadCountFromServer(api);
        } else {
          // 如果用户登出，重置消息服务
          _messageService.reset();
        }
      }
    });
  }

  onStateChanged() async {
    setState(() {
      state = pageStateProvider.currentState;
    });
  }

  _onMessageServiceChanged() {
    // 当消息服务状态改变时，触发UI更新
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    authNotifier.removeListener(onAuthChanged);
    pageStateProvider.removeListener(onStateChanged);
    _messageService.removeListener(_onMessageServiceChanged);
    AppLogger.info('Home dispose(${widget.key}): state: $state', 'UI');
    _versionCheckTimer?.cancel();

    // 释放 FocusNode
    _customPlayersFocusNode.dispose();
    _budgetFocusNode.dispose();

    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void didUpdateWidget(covariant HomeScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  void handleNotification(BookingType type) {
    setState(() {
      switch (type) {
        case BookingType.reservation:
          selectedBookingStatus = BookingStatus.pending;
          state = PageState.plans;

          break;
        case BookingType.notification:
          selectedNotificationStatus = NotificationStatus.running;
          state = PageState.notifications;

          break;
      }
    });
    // Navigator.pop(context);
  }

  String getTitle() {
    switch (state) {
      case PageState.home:
        return 'TeeTimeBot';
      case PageState.plans:
        return 'Bot Schedule';
      case PageState.settings:
        return 'Edit Profile';
      case PageState.notifications:
        return 'Waitlist';
      case PageState.points:
        return 'Points Center';
      case PageState.linkedAccount:
        return 'Manage Accounts';
      case PageState.versionInfo:
        return 'About';
      case PageState.messageCenter:
        return 'Notifications';
      default:
        return 'TeeTimeBot';
    }
  }

  Widget buildContent(BuildContext context) {
    switch (state) {
      case PageState.home:
        return SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              HomeFormScreen(
                isLoggedIn: isLoggedIn,
                notifyParent: handleNotification,
                isBooking: true,
                customPlayersFocusNode: _customPlayersFocusNode,
                budgetFocusNode: _budgetFocusNode,
              ),
              const AboutScreen(),
            ],
          ),
        );
      case PageState.homeNotify:
        return SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              HomeFormScreen(
                isLoggedIn: isLoggedIn,
                notifyParent: handleNotification,
                isBooking: false,
                customPlayersFocusNode: _customPlayersFocusNode,
                budgetFocusNode: _budgetFocusNode,
              ),
              const AboutScreen(),
            ],
          ),
        );
      case PageState.plans:
        return PlansScreen(selectedStatus: selectedBookingStatus);
      case PageState.settings:
        return const SettingScreen();
      case PageState.notifications:
        return NotificationsScreen(selectedStatus: selectedNotificationStatus);
      case PageState.points:
        return const PointsScreen();
      case PageState.linkedAccount:
        return const LinkedAccountScreen();
      case PageState.versionInfo:
        return const VersionInfoScreen();
      case PageState.messageCenter:
        return const MessageCenterScreen();
      default:
        return SingleChildScrollView(
          child: HomeFormScreen(
            isLoggedIn: isLoggedIn,
            notifyParent: handleNotification,
            isBooking: true,
            customPlayersFocusNode: _customPlayersFocusNode,
            budgetFocusNode: _budgetFocusNode,
          ),
        );
    }
  }

  Widget buildMenu(
    Widget leading,
    String title,
    PageState targetState,
  ) {
    final bool isSelected = state == targetState;

    // 定义选中时的颜色
    const Color selectedColor = AppColors.primaryColor; // 使用应用主色调

    return ListTile(
      leading: isSelected
          ? IconTheme(
              data: const IconThemeData(color: AppColors.primaryColor),
              child: leading,
            )
          : leading,
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? selectedColor : Colors.black87,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      onTap: () {
        setState(() {
          if (targetState == PageState.notifications) {
            selectedNotificationStatus = NotificationStatus.all;
          }
          if (targetState == PageState.plans) {
            selectedBookingStatus = BookingStatus.all;
          }

          state = targetState;
        });
        Navigator.pop(context);
      },
      selected: isSelected,
      // 使用轻微透明的主色调作为背景色
      selectedTileColor: AppColors.secondaryColor,
      selectedColor: selectedColor, // 这个属性会被上面的自定义样式覆盖
    );
  }

  /// 构建消息中心菜单项（带未读消息徽章）
  Widget _buildMessageCenterMenu() {
    final bool isSelected = state == PageState.messageCenter;
    const Color selectedColor = AppColors.primaryColor;
    final bool hasUnreadMessages = _messageService.hasUnreadMessages;

    return ListTile(
      leading: Stack(
        children: [
          isSelected
              ? const IconTheme(
                  data: IconThemeData(color: AppColors.primaryColor),
                  child: Icon(Icons.message),
                )
              : const Icon(Icons.message),
          if (hasUnreadMessages)
            Positioned(
              right: 0,
              top: 0,
              child: Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
            ),
        ],
      ),
      title: Text(
        'Notifications',
        style: TextStyle(
          color: isSelected ? selectedColor : Colors.black87,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      onTap: () {
        setState(() {
          state = PageState.messageCenter;
        });
        Navigator.pop(context);
      },
      selected: isSelected,
      selectedTileColor: AppColors.secondaryColor,
      selectedColor: selectedColor,
    );
  }

  Widget? buildDrawer(BuildContext context) {
    // Only build drawer if user is logged in
    if (!isLoggedIn) return null;

    AuthNotifier authNotifier = Provider.of<AuthNotifier>(context);
    var email = "";

    if (authNotifier.getUser() != null) {
      email = authNotifier.getUser().email;
    }

    return Drawer(
      backgroundColor: AppColors.primaryColor, // <--- 设置 Drawer 的背景色为白色
      child: SafeArea(
          child: Container(
        color: AppColors.background,
        child: ListView(
          padding: EdgeInsets.zero,
          children: <Widget>[
            Container(
              // 头部容器保持绿色背景
              color: AppColors.primaryColor, // 这个颜色会覆盖 Drawer 的白色背景
              padding:
                  const EdgeInsets.symmetric(vertical: 16.0, horizontal: 0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      const SizedBox(width: 18),
                      // --- 保持头部内容 ---
                      Container(
                        // 包裹图片的白色圆角容器
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        padding: const EdgeInsets.all(4.0),
                        child: Image.asset(
                          "assets/imgs/logo.png",
                          fit: BoxFit.contain,
                        ),
                      ),
                      const SizedBox(width: 2.0),
                      const Expanded(
                        child: Text(
                          'TeeTimeBot',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20.0,
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      // --- 头部内容结束 ---
                    ],
                  ),
                  const SizedBox(height: 12.0),
                  const Divider(
                    color: Color(0xFF14A848),
                    height: 1,
                  ),
                  const SizedBox(height: 12.0),
                  Row(
                    children: <Widget>[
                      const SizedBox(width: 18),
                      Expanded(
                        child: Text(
                          'Email: $email',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14.0,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // --- ListView 的菜单项 (将显示 Drawer 的白色背景) ---
            buildMenu(const Icon(Icons.home), 'Home', PageState.home),
            buildMenu(const Icon(Icons.calendar_today), 'Bot Schedule',
                PageState.plans),
            buildMenu(const Icon(Icons.notifications), 'Waitlist',
                PageState.notifications),
            buildMenu(const Icon(Icons.stars), 'Points', PageState.points),
            buildMenu(const Icon(Icons.link), 'Manage Accounts',
                PageState.linkedAccount),
            buildMenu(
                const Icon(Icons.settings), 'Edit Profile', PageState.settings),
            _buildMessageCenterMenu(),
            buildMenu(const Icon(Icons.info), 'About', PageState.versionInfo),
          ],
        ),
      )),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Only add drawer if user is logged in
    final drawer = buildDrawer(context);

    // 准备内容
    final body = buildContent(context);

    // 在 iOS 上使用 KeyboardActions 包装 body
    Widget wrappedBody;

    if (!kIsWeb && defaultTargetPlatform == TargetPlatform.iOS) {
      // 使用 Builder 确保正确的构建上下文
      wrappedBody = Builder(
        builder: (context) => KeyboardActions(
          config: _buildKeyboardActionsConfig(context),
          enable: true,
          disableScroll: true, // 避免与其他滚动视图冲突
          child: body,
        ),
      );
    } else {
      wrappedBody = body;
    }

    // 返回 Scaffold
    return Scaffold(
      key: _scaffoldKey,
      appBar: GolfAppBar(
        title: getTitle(),
        scaffoldKey: _scaffoldKey,
      ),
      // backgroundColor: AppColors.primaryColor,
      drawer: drawer,
      body: wrappedBody,
    );
  }

  void _startVersionCheck() {
    _checkVersion();

    _versionCheckTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (!_updateDialogShowing && mounted) {
        _checkVersion();
      }
    });
  }

  Future<void> _checkVersion() async {
    // 如果已经在检查中或者正在显示更新弹窗，则跳过
    if (_isCheckingVersion || _updateDialogShowing) {
      return;
    }

    try {
      _isCheckingVersion = true;
      final api = Provider.of<APIBase>(context, listen: false);
      final version = await api.getVersion();
      final currentVersion = _platformService.getCurrentVersion();

      if (version != currentVersion && mounted) {
        AppLogger.info(
            "new version found: $version, current: $currentVersion", 'VERSION');
        // 设置状态前先检查是否已经在显示弹窗
        if (!_updateDialogShowing) {
          setState(() => _updateDialogShowing = true);
          try {
            await _platformService.handleVersionUpdate(context, version);
          } finally {
            // 确保状态被重置
            if (mounted) {
              setState(() => _updateDialogShowing = false);
            }
          }
        }
      }
    } catch (e) {
      AppLogger.error("Version check error: $e", 'VERSION');
    } finally {
      _isCheckingVersion = false;
    }
  }
}
