import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:golf/config.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/services/message_service.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/message_req.dart';
import 'package:intl/intl.dart';

/// 消息详情页面
/// 显示单条消息的详细内容，并在用户查看时标记为已读
class MessageDetailScreen extends StatefulWidget {
  final MessageItem message;

  const MessageDetailScreen({
    super.key,
    required this.message,
  });

  @override
  State<MessageDetailScreen> createState() => _MessageDetailScreenState();
}

class _MessageDetailScreenState extends State<MessageDetailScreen> {
  late final MessageService _messageService;
  bool _isMarkingAsRead = false;

  @override
  void initState() {
    super.initState();
    _messageService = MessageService();

    // 如果消息未读，则标记为已读
    if (!widget.message.isRead) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _markMessageAsRead();
      });
    }
  }

  /// 标记消息为已读
  Future<void> _markMessageAsRead() async {
    if (_isMarkingAsRead) return;

    setState(() {
      _isMarkingAsRead = true;
    });

    try {
      final api = Provider.of<APIBase>(context, listen: false);
      final messageId = int.parse(widget.message.id);

      await _messageService.markMessageAsRead(messageId, api);
      // 等待API调用完成后同步未读数量
      await _messageService.syncUnreadCountFromServer(api);

      AppLogger.info('Message ${widget.message.id} marked as read', 'MessageDetail');
    } catch (e) {
      AppLogger.error('Failed to mark message as read: $e', 'MessageDetail');
    } finally {
      if (mounted) {
        setState(() {
          _isMarkingAsRead = false;
        });
      }
    }
  }

  /// 格式化时间显示
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 7) {
      return DateFormat('MMM dd, yyyy HH:mm').format(dateTime);
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  /// 获取消息类型图标
  IconData _getMessageTypeIcon() {
    switch (widget.message.type.toLowerCase()) {
      case 'reminder':
        return Icons.schedule;
      case 'booking':
        return Icons.calendar_today;
      case 'promotion':
        return Icons.local_offer;
      case 'system':
        return Icons.info;
      case 'alert':
        return Icons.warning;
      default:
        return Icons.message;
    }
  }

  /// 获取消息类型颜色
  Color _getMessageTypeColor() {
    switch (widget.message.type.toLowerCase()) {
      case 'reminder':
        return Colors.blue;
      case 'booking':
        return Colors.green;
      case 'promotion':
        return Colors.orange;
      case 'system':
        return Colors.grey;
      case 'alert':
        return Colors.red;
      default:
        return AppColors.primaryColor;
    }
  }

  /// 解析额外数据
  Map<String, dynamic>? _parseExtraData() {
    try {
      if (widget.message.extraData != null) {
        return widget.message.extraData;
      }
      return null;
    } catch (e) {
      AppLogger.error('Failed to parse extra data: $e', 'MessageDetail');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final extraData = _parseExtraData();
    final messageTypeColor = _getMessageTypeColor();

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text(
          'Message Details',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppColors.textColor,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textColor),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 消息头部信息
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 消息类型和状态
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8.0,
                          vertical: 4.0,
                        ),
                        decoration: BoxDecoration(
                          color: messageTypeColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6.0),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _getMessageTypeIcon(),
                              size: 14,
                              color: messageTypeColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              widget.message.type.toUpperCase(),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: messageTypeColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      // 显示加载指示器（如果正在标记为已读）
                      if (_isMarkingAsRead)
                        const SizedBox(
                          width: 12,
                          height: 12,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppColors.primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // 消息标题
                  Text(
                    widget.message.title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: AppColors.textColor,
                      height: 1.3,
                    ),
                  ),
                  const SizedBox(height: 8),
                  // 时间信息
                  Text(
                    _formatDateTime(widget.message.createdAt),
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.greyColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            // 消息内容
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Message Content',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textColor,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    widget.message.body.isNotEmpty
                        ? widget.message.body
                        : 'No content available',
                    style: const TextStyle(
                      fontSize: 16,
                      color: AppColors.textColor,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
            // 额外数据（如果有）
            if (extraData != null && extraData.isNotEmpty) ...[
              const SizedBox(height: 20),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Additional Information',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textColor,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...extraData.entries.map((entry) => Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${entry.key}: ',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: AppColors.greyColor,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              entry.value.toString(),
                              style: const TextStyle(
                                fontSize: 14,
                                color: AppColors.textColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )),
                  ],
                ),
              ),
            ],
            // 底部间距
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
