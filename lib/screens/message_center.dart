import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:golf/config.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/widgets/golf_toast.dart';
import 'package:golf/services/message_service.dart';
import 'package:golf/services/provider/base.dart';
import 'package:golf/services/req/message_req.dart';
import 'package:golf/screens/message_detail.dart';
import 'package:golf/screens/users/plan_log_list.dart';
import 'package:provider/provider.dart';

/// 消息中心页面
/// 检查通知权限状态，引导用户开启权限，显示消息列表
class MessageCenterScreen extends StatefulWidget {
  const MessageCenterScreen({super.key});

  @override
  State<MessageCenterScreen> createState() => _MessageCenterScreenState();
}

class _MessageCenterScreenState extends State<MessageCenterScreen>
    with WidgetsBindingObserver {
  bool _isLoading = true;
  bool _hasNotificationPermission = false;
  List<NotificationMessage> _messages = [];
  List<MessageItem> _messageItems = []; // Store original MessageItem data
  late final MessageService _messageService;

  @override
  void initState() {
    super.initState();
    _messageService = MessageService();
    WidgetsBinding.instance.addObserver(this);
    _checkNotificationPermission();
    _loadMessages();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用从后台返回前台时，重新检查权限状态
    if (state == AppLifecycleState.resumed) {
      _checkNotificationPermission();
    }
  }

  /// 检查通知权限状态
  Future<void> _checkNotificationPermission() async {
    if (kIsWeb) {
      // Web平台直接设置为true，因为不需要权限
      setState(() {
        _hasNotificationPermission = true;
        _isLoading = false;
      });
    } else {
      try {
        // 使用Firebase Messaging检查权限状态（非Web平台）
        final messaging = FirebaseMessaging.instance;
        final settings = await messaging.getNotificationSettings();
        setState(() {
          _hasNotificationPermission =
              settings.authorizationStatus == AuthorizationStatus.authorized;
          _isLoading = false;
        });
        AppLogger.info(
            'Notification permission status: $_hasNotificationPermission (${settings.authorizationStatus})',
            'MessageCenter');
      } catch (e) {
        AppLogger.error(
            'Failed to check notification permission: $e', 'MessageCenter');
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 加载消息列表
  Future<void> _loadMessages() async {
    try {
      final api = Provider.of<APIBase>(context, listen: false);
      final request = MessageListRequest(page: 1, size: 50);
      final response = await api.getMessageList(request);

      setState(() {
        _messageItems = response.items; // Store original MessageItem data
        _messages = response.items
            .map((item) => NotificationMessage(
                  id: item.id,
                  title: item.title,
                  body: item.body,
                  timestamp: item.createdAt,
                  isRead: item.isRead,
                  type: item.type,
                ))
            .toList();
      });

      // 更新MessageService中的未读消息数量
      await _messageService.setUnreadCount(response.unreadCount);
    } catch (e) {
      AppLogger.error('Failed to load messages: $e', 'MessageCenter');
      // 如果API调用失败，使用模拟数据作为后备
      setState(() {
        _messages = _getMockMessages();
        _messageItems = []; // Clear original data if using mock
      });
    }
  }

  /// 获取模拟消息数据
  List<NotificationMessage> _getMockMessages() {
    return [
      NotificationMessage(
        id: '1',
        title: 'Tee Time Reminder',
        body: 'Your tee time is at 10:30 AM',
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        isRead: false,
        type: 'reminder',
      ),
      NotificationMessage(
        id: '2',
        title: 'Booking Confirmed',
        body: 'Your booking has been confirmed',
        timestamp: DateTime.now().subtract(const Duration(days: 1)),
        isRead: true,
        type: 'booking',
      ),
      NotificationMessage(
        id: '3',
        title: 'Special Offer',
        body: 'Get 20% off your next booking',
        timestamp: DateTime.now().subtract(const Duration(days: 2)),
        isRead: false,
        type: 'promotion',
      ),
    ];
  }

  /// 请求通知权限
  Future<void> _requestNotificationPermission() async {
    if (kIsWeb) {
      // Web平台直接设置权限为允许
      setState(() {
        _hasNotificationPermission = true;
      });
      if (mounted) {
        showToast(context, msg: 'Notifications are enabled by default on the web');
      }
    } else {
      try {
        // 移动端使用permission_handler
        final status = await Permission.notification.request();
        setState(() {
          _hasNotificationPermission = status == PermissionStatus.granted;
        });

        if (_hasNotificationPermission) {
          if (mounted) {
            showToast(context, msg: 'Notification permission granted');
          }
        } else if (status == PermissionStatus.permanentlyDenied) {
          _showSettingsDialog();
        } else {
          if (mounted) {
            showToast(context, msg: 'Notification permission denied');
          }
        }
      } catch (e) {
        AppLogger.error(
            'Failed to request notification permission: $e', 'MessageCenter');
        if (mounted) {
          showToast(context, msg: 'Failed to request permission');
        }
      }
    }
  }

  /// 显示设置对话框
  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Notification Permission Required'),
          content: const Text(
              'Please enable notification permission in Settings to receive important messages.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text('Go to Settings'),
            ),
          ],
        );
      },
    );
  }

  /// 标记消息为已读
  void _markAsRead(String messageId) async {
    // 先更新UI
    setState(() {
      final index = _messages.indexWhere((msg) => msg.id == messageId);
      if (index != -1) {
        _messages[index] = _messages[index].copyWith(isRead: true);
      }
    });

    // 更新MessageService中的未读数量和调用API更新服务器端的已读状态
    try {
      final api = Provider.of<APIBase>(context, listen: false);
      final messageIdInt = int.parse(messageId);
      await _messageService.markMessageAsRead(messageIdInt, api);
      // Wait for API then sync unread count from server
      await _messageService.syncUnreadCountFromServer(api);
      AppLogger.info(
          'Message $messageId marked as read on server', 'MessageCenter');
    } catch (e) {
      AppLogger.error(
          'Failed to mark message as read on server: $e', 'MessageCenter');
      // 如果API调用失败，可以选择显示错误提示或重试
    }
  }

  /// 显示标记所有已读的确认对话框
  Future<void> _showMarkAllReadConfirmation() async {
    final unreadCount = _messages.where((msg) => !msg.isRead).length;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Mark All Read',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          content: Text(
            'Are you sure you want to mark all $unreadCount unread messages as read? This action cannot be undone.',
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                'Cancel',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 16,
                ),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text(
                'Mark All Read',
                style: TextStyle(
                  color: AppColors.primaryColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );

    if (confirmed == true) {
      await _markAllAsRead();
    }
  }

  /// 标记所有消息为已读
  Future<void> _markAllAsRead() async {
    try {
      final api = Provider.of<APIBase>(context, listen: false);

      // 更新本地状态 - 将所有消息标记为已读
      setState(() {
        _messages = _messages.map((msg) => msg.copyWith(isRead: true)).toList();
      });

      // 更新MessageService中的未读数量为0
      await _messageService.markAllMessagesAsRead(api);
      // Wait for API then sync unread count from server
      await _messageService.syncUnreadCountFromServer(api);

      AppLogger.info('All messages marked as read on server', 'MessageCenter');

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('All messages marked as read'),
            duration: Duration(seconds: 2),
            backgroundColor: AppColors.primaryColor,
          ),
        );
      }
    } catch (e) {
      AppLogger.error(
          'Failed to mark all messages as read on server: $e', 'MessageCenter');

      // 显示错误提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to mark all messages as read'),
            duration: Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: AppColors.background,
        body: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // 如果没有通知权限，显示权限提示横幅
          if (!_hasNotificationPermission) _buildPermissionBanner(),
          // 消息列表始终显示
          Expanded(child: _buildMessageList()),
        ],
      ),
    );
  }

  /// 构建权限提示横幅
  Widget _buildPermissionBanner() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Row(
        children: [
          Icon(
            Icons.notifications_off,
            color: Colors.orange[700],
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Notifications Disabled',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Enable notifications to receive real-time alerts',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.orange[600],
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: _requestNotificationPermission,
            style: TextButton.styleFrom(
              foregroundColor: Colors.orange[700],
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            child: const Text(
              'Enable',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建消息列表
  Widget _buildMessageList() {
    return Column(
      children: [
        // 标题行，包含"Mark All Read"按钮
        _buildMessageHeader(),
        // 消息列表内容
        Expanded(
          child: _messages.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.inbox,
                        size: 80,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'No messages',
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadMessages,
                  color: AppColors.primaryColor,
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _messages.length,
                    itemBuilder: (context, index) {
                      final message = _messages[index];
                      return _buildMessageItem(message);
                    },
                  ),
                ),
        ),
      ],
    );
  }

  /// 构建消息标题行
  Widget _buildMessageHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: const BoxDecoration(
        color: AppColors.background,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey,
            width: 0.2,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          const Text(
            'Notifications',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          // 标记所有已读按钮
          if (_messages.any((msg) => !msg.isRead))
            TextButton(
              onPressed: _showMarkAllReadConfirmation,
              child: const Text(
                'Mark All Read',
                style: TextStyle(
                  color: AppColors.primaryColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建消息项
  Widget _buildMessageItem(NotificationMessage message) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: message.isRead
                ? Colors.grey.withValues(alpha: 0.3)
                : AppColors.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Icon(
            _getMessageIcon(message.type),
            color: message.isRead ? Colors.grey : AppColors.primaryColor,
            size: 24,
          ),
        ),
        title: Text(
          message.title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: message.isRead ? FontWeight.normal : FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              message.body,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _formatTimestamp(message.timestamp),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
        trailing: !message.isRead
            ? Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              )
            : null,
        onTap: () {
          // Mark message as read first if not already read
          if (!message.isRead) {
            _markAsRead(message.id);
          }

          // Find the corresponding MessageItem for navigation
          MessageItem? messageItem;
          if (_messageItems.isNotEmpty) {
            try {
              messageItem = _messageItems.firstWhere(
                (item) => item.id == message.id,
              );
            } catch (e) {
              AppLogger.error('Message item not found: $e', 'MessageCenter');
            }
          }

          // Navigate based on the datatype
          if (messageItem != null) {
            if (messageItem.datatype == '1') {
              // Navigate to plan_log_list page if datatype is "1"
              final planId = messageItem.planId;
              if (planId != null) {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => PlanLogListScreen(planId: planId),
                  ),
                ).then((_) {
                  // Refresh the message list when returning
                  _loadMessages();
                });
              }
            } else {
              // Navigate to detail screen for other types
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => MessageDetailScreen(message: messageItem!),
                ),
              ).then((_) {
                _loadMessages();
              });
            }
          }
        },
      ),
    );
  }

  /// 获取消息图标
  IconData _getMessageIcon(String type) {
    switch (type) {
      case 'reminder':
        return Icons.access_time;
      case 'booking':
        return Icons.event_available;
      case 'promotion':
        return Icons.local_offer;
      default:
        return Icons.notifications;
    }
  }

  /// 格式化时间戳
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// 通知消息数据模型
class NotificationMessage {
  final String id;
  final String title;
  final String body;
  final DateTime timestamp;
  final bool isRead;
  final String type;

  const NotificationMessage({
    required this.id,
    required this.title,
    required this.body,
    required this.timestamp,
    required this.isRead,
    required this.type,
  });

  NotificationMessage copyWith({
    String? id,
    String? title,
    String? body,
    DateTime? timestamp,
    bool? isRead,
    String? type,
  }) {
    return NotificationMessage(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      type: type ?? this.type,
    );
  }
}
