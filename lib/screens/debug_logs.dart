import 'package:flutter/material.dart';
import 'package:golf/config.dart';
import '../utils/crash_reporter.dart';

class DebugLogsScreen extends StatelessWidget {
  const DebugLogsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Debug Logs')),
      body: FutureBuilder<String?>(
        future: CrashReporter.getCrashLogs(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(
                child: CircularProgressIndicator(
              color: AppColors.primaryColor,
            ));
          }
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Text(snapshot.data ?? 'No logs available'),
            ),
          );
        },
      ),
    );
  }
}
