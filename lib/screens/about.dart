import 'package:flutter/material.dart';
import 'package:golf/config.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});
  Widget buildMainContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Center(
            child: Text(
          'About TeeTimeBot',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        )),
        const SizedBox(height: 16),
        const Text(
          'TeeTimeBot helps grab your perfect tee time automatically!',
        ),
        const SizedBox(height: 16),
        const Center(
            child: Text(
          'How to use',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        )),
        // Add your steps here
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: <Widget>[
                SizedBox(
                    width: 120,
                    height: 76,
                    child: Image.asset('assets/imgs/step1.png')),
                const Flexible(
                    // 使用 Flexible 包裹 Text
                    child: Text(
                        'Link your TeeTimeBot account with your favorite golf platforms')),
              ],
            ),
            Row(
              children: <Widget>[
                SizedBox(
                    width: 120,
                    height: 76,
                    child: Image.asset('assets/imgs/step2.png')),
                const Flexible(
                    // 使用 Flexible 包裹 Text
                    child: Text(
                        'Setup your booking schedule or join the waitlist')),
              ],
            ),
            Row(
              children: <Widget>[
                SizedBox(
                    width: 120,
                    height: 76,
                    child: Image.asset('assets/imgs/step3.png')),
                const Flexible(
                  // 使用 Flexible 包裹 Text
                  child: Text(
                      'TeeTimeBot grabs tee time for you automatically or sends out notification once tee time is available'),
                ),
              ],
            ),
          ],
        ),
        // const SizedBox(height: 16),
        // const Text('TeeTimeBot'),
        // const Text('My account'),
        // TextButton(
        //   child: const Text(
        //     'Terms',
        //     style: TextStyle(color: Colors.blue),
        //   ),
        //   onPressed: () =>
        //       Navigator.pushNamed(context, '/terms_and_conditions'),
        // ),
        // TextButton(
        //   child: const Text(
        //     'Privacy Policy',
        //     style: TextStyle(color: Colors.blue),
        //   ),
        //   onPressed: () => Navigator.pushNamed(context, '/privacy_policy'),
        // ),
        // const Text('网站备案信息'),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: buildMainContent(context),
          ),
        ),
      ),
    );
  }
}
