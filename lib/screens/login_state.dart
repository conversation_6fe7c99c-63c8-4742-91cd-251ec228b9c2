import 'package:flutter/material.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/services/provider/auth_notifier.dart';
import 'package:golf/services/provider/base.dart';
import 'package:provider/provider.dart';

abstract class LoginRequiredState<T extends StatefulWidget> extends State<T> {
  bool isLoggedIn = true;
  late AuthNotifier authNotifier;

  @override
  void initState() {
    super.initState();
    authNotifier = context.read<AuthNotifier>();
    authNotifier.addListener(onAuthChanged);
  }

  @override
  void dispose() {
    authNotifier.removeListener(onAuthChanged);

    super.dispose();
  }

  onAuthChanged() async {
    final api = Provider.of<APIBase>(context, listen: false);
    api.isLoggedIn().then((loggined) {
      AppLogger.info('isLoggedIn: $loggined', 'AUTH');
      // redirect to login page if not logged in
      if (isLoggedIn != loggined) {
        isLoggedIn = loggined;
        if (!isLoggedIn) {
          Navigator.pushNamed(context, '/user/sign_in');
        }
      }
    });
  }
}
