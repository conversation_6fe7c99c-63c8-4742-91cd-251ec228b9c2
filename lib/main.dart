import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:golf/services/firebase_messaging_service.dart';
import 'package:golf/services/message_service.dart';
import 'package:golf/screens/search.dart';
import 'package:golf/screens/users/point_detail.dart';
import 'package:golf/screens/users/point_store.dart';
import 'package:golf/screens/promo_code_manager.dart';
import 'package:golf/services/provider/api.dart';
import 'package:golf/services/provider/auth_notifier.dart';
import 'package:golf/screens/privacy_policy_screen.dart';
import 'package:golf/screens/reservation.dart';
import 'package:golf/screens/terms_and_conditions_screen.dart';
import 'package:golf/screens/users/change_email.dart';
import 'package:golf/screens/users/change_password.dart';
import 'package:golf/screens/users/delete_account.dart';
import 'package:golf/screens/users/link_account.dart';
import 'package:golf/screens/users/linked_account.dart';
import 'package:golf/screens/users/notifications.dart';
import 'package:golf/screens/users/plans.dart';
import 'package:golf/screens/users/points.dart';
import 'package:golf/screens/users/settings.dart';

import 'package:golf/services/provider/auth_storage.dart';
import 'package:golf/services/provider/base.dart';

import 'package:golf/services/provider/pagestate_provider.dart';

import 'package:golf/services/req/booking_req.dart';
import 'package:golf/services/req/notify_req.dart';
import 'package:provider/provider.dart';
import 'auth_guard_observer.dart';
import 'screens/about.dart';
import 'screens/version_info.dart';
import 'screens/home.dart';
import 'screens/users/forgot_password.dart';
import 'screens/users/sign_up.dart';
import 'screens/users/sign_in.dart';
import 'dart:async';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart' show PlatformDispatcher;

// 条件导入web插件
import 'url_strategy.dart' if (dart.library.html) 'url_strategy_web.dart';
import 'utils/crash_reporter.dart';
import 'package:golf/config.dart';
import 'utils/logger.dart';

// 添加这个全局错误处理函数
void _errorHandler(Object error, StackTrace stack) {
  developer.log('🔴 Uncaught error:', error: error, stackTrace: stack);
  debugPrint('🔴 Error: $error');
  debugPrint('🔴 Stack: $stack');
}

void main() {
  runZonedGuarded<Future<void>>(() async {
    debugPrint('🚀 Starting application...');

    // 初始化崩溃报告
    await CrashReporter.init();

    // 设置全局错误处理
    PlatformDispatcher.instance.onError = (error, stack) {
      _errorHandler(error, stack);
      return true;
    };

    try {
      debugPrint('⚙️ Initializing app...');

      // 确保在其他操作之前初始化 Flutter 绑定
      WidgetsFlutterBinding.ensureInitialized();
      debugPrint('✅ Flutter binding initialized');

      // 初始化Firebase (仅在非Web平台)
      if (!kIsWeb) {
        await Firebase.initializeApp();
        debugPrint('✅ Firebase initialized');

        // 初始化Firebase消息服务
        await FirebaseMessagingService.initialize();
        debugPrint('✅ Firebase Messaging initialized');
      }

      // 初始化消息服务
      await MessageService().initialize();
      debugPrint('✅ MessageService initialized');

      // 配置URL策略（仅web平台）
      configureUrl();


      // debugPrintRebuildDirtyWidgets = true;

      runApp(
        MultiProvider(
          providers: [
            Provider<AuthStorage>(create: (_) => AuthStorage()),
            ChangeNotifierProvider<PageStateNotifier>(
              create: (_) => PageStateNotifier(),
            ),
            ChangeNotifierProvider<AuthNotifier>(
              create: (_) => AuthNotifier(),
            ),
            ChangeNotifierProvider<MessageService>(
              create: (_) => MessageService(),
            ),
            Provider<APIBase>(
              create: (context) {
                final api = API(DefaultApi(
                  context.read<AuthNotifier>(),
                  context.read<AuthStorage>(),
                ));
                // Set API reference in AuthNotifier for device registration
                context.read<AuthNotifier>().setAPI(api);
                return api;
              },
            ),
          ],
          child: const GolfApp(),
        ),
      );
    } catch (e, stack) {
      debugPrint('❌ Initialization error: $e');
      debugPrint('❌ Stack: $stack');
      rethrow;
    }
  }, (Object error, StackTrace stack) async {
    await CrashReporter.reportError(error, stack);
  });
}

class GolfApp extends StatefulWidget {
  const GolfApp({super.key});

  @override
  State<GolfApp> createState() => _GolfAppState();
}

class _GolfAppState extends State<GolfApp> {
  final AuthGuardObserver _authGuardObserver = AuthGuardObserver();

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: AppColors.primaryColor,
        primaryColorLight: AppColors.primaryColorLight,
        bottomAppBarTheme: const BottomAppBarTheme(color: Colors.white),
      ),
      navigatorObservers: [_authGuardObserver],
      initialRoute: '/',
      routes: {
        '/user/sign_up': (context) {
          final invitor = Uri.base.pathSegments.length > 2
              ? Uri.base.pathSegments[2]
              : null;
          return SignUpScreen(invitor: invitor);
        },
        '/user/sign_in': (context) => const SignInScreen(),
        '/user/settings': (context) => const SettingScreen(),
        '/user/forgot_password': (context) => const ForgotPasswordScreen(),
        '/user/change_password': (context) => const ChangePasswordScreen(),
        '/user/delete_account': (context) => const DeleteAccountScreen(),
        // link account
        '/user/link_account': (context) => const LinkAccountScreen(),
        '/user/linked_account': (context) => const LinkedAccountScreen(),
        // change email
        '/user/change_email': (context) => const ChangeEmailScreen(),
        '/user/points': (context) => const PointsScreen(),
        '/user/point_store': (context) => const PointStoreScreen(),
        '/user/point_detail': (context) {
          return const PointDetailPage(orderId: "");
        },
        '/user/plans': (context) => const PlansScreen(
              selectedStatus: BookingStatus.success,
            ),
        '/user/notification': (context) => const NotificationsScreen(
              selectedStatus: NotificationStatus.all,
            ),
        '/terms_and_conditions': (context) => const TermsAndConditionsScreen(),
        '/privacy_policy': (context) => const PrivacyPolicyScreen(),
        '/': (context) {
          return const HomeScreen();
        },
        '/about': (context) => const AboutScreen(),
        '/version_info': (context) => const VersionInfoScreen(),
        // '/map': (context) => const AnimatedMapControllerPage(),
        '/search': (context) => const SearchPage(),
        '/promo_code_manager': (context) => const PromoCodeManagerPage(),
      },
      // 动态路由处理
      onGenerateRoute: (settings) {
        AppLogger.info('onGenerateRoute route: ${settings.name}', 'ROUTER');
        final Uri uri = Uri.parse(settings.name ?? '');

        // 处理支付完成后的订单详情页面
        if (uri.path == '/user/point_detail') {
          AppLogger.info('动态路由处理订单详情页面: ${uri.toString()}', 'ROUTER');
          final params = uri.queryParameters;
          final orderId = params['order_id'];
          final paymentResult = params['payment_result'];
          final status = params['status'];

          AppLogger.info(
              '订单详情页面参数: orderId=$orderId, paymentResult=$paymentResult, status=$status',
              'ROUTER');

          if (orderId != null) {
            AppLogger.info('从URL参数获取订单ID: $orderId', 'ROUTER');
            return MaterialPageRoute(
              builder: (context) => PointDetailPage(
                orderId: orderId,
              ),
              settings: RouteSettings(
                name: '/user/point_detail',
                arguments: {
                  'order_id': orderId,
                  'payment_result': paymentResult,
                  'status': status,
                },
              ),
            );
          }
        }

        // 处理预约详情页面
        if (uri.pathSegments.length == 2 &&
            uri.pathSegments.first == 'reservation') {
          final param = uri.pathSegments[1];
          return MaterialPageRoute(
              builder: (context) => ReservationScreen(id: param));
        }

        // 处理注册邀请页面
        if (uri.pathSegments.length == 3 &&
            uri.pathSegments.first == 'user' &&
            uri.pathSegments[1] == 'sign_up') {
          final invitor = uri.pathSegments[2];
          return MaterialPageRoute(
              builder: (context) => SignUpScreen(invitor: invitor));
        }

        // 未知路由处理
        return MaterialPageRoute(builder: (context) => const NotFoundScreen());
      },
    );
  }
}

class NotFoundScreen extends StatelessWidget {
  const NotFoundScreen({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('404 Not Found'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Text('404 Not Found'),
            TextButton(
              child: const Text('Go to Home'),
              onPressed: () {
                Navigator.pushNamed(context, '/');
              },
            ),
          ],
        ),
      ),
    );
  }
}
