// config.dart

import 'package:flutter/material.dart';

class AppSettings {
  static const appName = 'TeeTimeBot';

  /// 版本信息配置
  /// 移动端从 pubspec.yaml 动态读取，Web 端使用此备用配置
  static const appVersion = '0.1.83'; // 与 pubspec.yaml 保持同步
  static const packageName = 'com.teetime.golf';

  static const appDescription = 'Golf app';
  static const appAuthor = 'Golf';
  static const siteName = 'http://api.golfteetime.vip/';
}

class AppColors {
  static const primaryColor = Color(0xFF0FB549);
  static const disabledColor = Color(0xFF87DAA4);
  static const secondaryColor = Color(0xFFDFF5E7);
  static const primaryColorLight = Color(0xFF44C2FF);
  static const textColor = Colors.black;
  static const greyColor = Color(0xFF666666);
  static const background = Color(0xFFF5F3F8);
  static const orange = Color(0xDDFF6600);
}

class AppBorderRadius {
  static const small = 4.0;
  static const medium = 10.0;
  static const large = 15.0;
}

class AppFontSizes {
  static const small = 14.0;
  static const medium = 18.0;
  static const large = 24.0;
}

class AppPadding {
  static const small = 8.0;
  static const medium = 16.0;
  static const large = 24.0;
}

class AppHeight {
  static const input = 40.0;
}
