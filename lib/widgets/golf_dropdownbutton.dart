// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:golf/config.dart';

class GolfDropdownMenuItem {
  final String label;
  final String value;

  GolfDropdownMenuItem({required this.label, required this.value});
}

class GolfDropdownButton extends StatefulWidget {
  final List<GolfDropdownMenuItem> items;
  final ValueChanged<String> onChanged;
  final String? value;
  final double? width;

  const GolfDropdownButton({
    super.key,
    required this.items,
    required this.onChanged,
    this.value,
    this.width,
  });

  @override
  _GolfDropdownButtonState createState() => _GolfDropdownButtonState();
}

class _GolfDropdownButtonState extends State<GolfDropdownButton> {
  String _selectedValue = '';

  @override
  void initState() {
    super.initState();
    if (widget.value != null) {
      _selectedValue = widget.value!;
    } else if (widget.items.isNotEmpty) {
      _selectedValue = widget.items[0].value;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width ?? 150,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(5),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          isExpanded: true,
          value: _selectedValue,
          onChanged: (String? value) {
            setState(() {
              _selectedValue = value!;
            });
            widget.onChanged(value!);
          },
          items: widget.items
              .map((GolfDropdownMenuItem item) => DropdownMenuItem<String>(
                    value: item.value,
                    child: Text(
                      item.label,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ))
              .toList(),
        ),
      ),
    );
  }
}
