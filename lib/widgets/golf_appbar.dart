import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:golf/services/provider/auth_notifier.dart';
import 'package:golf/config.dart';
import 'package:golf/services/provider/base.dart';
import 'package:provider/provider.dart';

class GolfAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final GlobalKey<ScaffoldState>? scaffoldKey; // 可空类型

  const GolfAppBar({
    super.key,
    required this.title,
    this.scaffoldKey, // 可选参数
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  Widget buildAppBar(BuildContext context, bool isLoggedIn) {
    const foregroundColor = Colors.black;
    return AppBar(
      centerTitle: true,

      // --- 修改 leading 逻辑 ---
      leading: !isLoggedIn
          ? null // 未登录时不显示 leading
          : (scaffoldKey != null // 已登录：检查 scaffoldKey 是否存在
              ? IconButton(
                  // scaffoldKey 存在 (首页) -> 显示菜单按钮
                  icon: const Icon(Icons.menu, color: foregroundColor),
                  onPressed: () {
                    scaffoldKey?.currentState?.openDrawer(); // 使用 key 打开 drawer
                  },
                )
              : IconButton(
                  // scaffoldKey 不存在 (二级页面) -> 显示返回箭头
                  icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
                  onPressed: () {
                    Navigator.pop(context); // 点击返回上一页
                  },
                )),
      // --- leading 逻辑修改结束 ---
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
      actions: <Widget>[
        if (!isLoggedIn)
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: OutlinedButton(
              onPressed: () {
                Navigator.pushNamed(context, '/user/sign_in');
              },
              style: OutlinedButton.styleFrom(
                side: const BorderSide(
                  color: AppColors.primaryColor,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.0),
                ),
              ),
              child: const Text('Sign In',
                  style: TextStyle(color: AppColors.primaryColor)),
            ),
          )
        else // 用户已登录
        // 使用 collection if 来条件性添加 SizedBox
        if (scaffoldKey != null) // 如果是首页 (scaffoldKey != null)
          const SizedBox(width: 48.0), // 添加占位符以帮助居中标题
        // 如果是二级页面 (scaffoldKey == null)，则不添加任何 Widget
      ],
      backgroundColor: AppColors.background, // 设置背景色为白色
      elevation: 1, // 可以添加一点阴影
      shadowColor: Colors.grey.withOpacity(0.2), // 阴影颜色
    );
  }

  @override
  Widget build(BuildContext context) {
    // 建议直接依赖 AuthNotifier 状态，而不是 FutureBuilder
    final isLoggedIn = context.watch<AuthNotifier>().isLoggedIn;
    return buildAppBar(context, isLoggedIn);

    // --- 如果仍要用 FutureBuilder ---
    // context.watch<AuthNotifier>(); // 仍然可以 watch 以便在状态变化时重建
    // final api = Provider.of<APIBase>(context, listen: false);
    // return FutureBuilder<bool>(
    //   future: api.isLoggedIn(), // 考虑性能影响
    //   initialData: context.read<AuthNotifier>().isLoggedIn, // 使用初始值
    //   builder: (context, snapshot) {
    //     // 优先使用 snapshot.data，如果为 null 则使用 AuthNotifier 的状态
    //     final currentLoginState = snapshot.data ?? context.read<AuthNotifier>().isLoggedIn;
    //     return buildAppBar(context, currentLoginState);
    //   },
    // );
    // --- FutureBuilder 结束 ---
  }
}
