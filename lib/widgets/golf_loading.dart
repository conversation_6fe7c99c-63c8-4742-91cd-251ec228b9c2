import 'package:flutter/material.dart';
import 'package:golf/config.dart';

class GolfLoading extends StatelessWidget {
  const GolfLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return const Align(
      alignment: Alignment.centerRight,
      child: SizedB<PERSON>(
        width: 20, // 自定义宽度
        height: 20, // 自定义高度
        child: CircularProgressIndicator(
          color: AppColors.primaryColor,
          value: null, // 设置为 null 以显示动画效果
          strokeWidth: 1, // 调整线条的粗细
          backgroundColor: Colors.grey, // 设置背景色
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
        ),
      ),
    );
    // 设置前景色
  }
}

void showSuccessDialog(BuildContext context, String s) {
  GolfDialog(
    title: 'Success',
    content: s,
    actions: <Widget>[
      TextButton(
        onPressed: () {
          Navigator.of(context).pop();
        },
        child: const Text('OK'),
      ),
    ],
  ).show(context);
}

class GolfDialog {
  final String title;
  final String content;
  final List<Widget> actions;

  GolfDialog({
    required this.title,
    required this.content,
    required this.actions,
  });

  void show(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(title),
          content: Text(content),
          actions: actions,
        );
      },
    );
  }
}
