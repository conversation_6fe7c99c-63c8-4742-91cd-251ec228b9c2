import 'package:flutter/material.dart';
import 'package:golf/config.dart';

/// 显示一个简单的提示对话框
Future<void> showToast(BuildContext context, {required String msg}) {
  return showDialog<void>(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        content: Text(msg),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定',
                style: TextStyle(color: AppColors.primaryColor)),
          ),
        ],
      );
    },
  );
}

/// 显示一个确认对话框
///
/// @param context 当前的 BuildContext
/// @param title 对话框标题
/// @param content 对话框内容
/// @param onConfirm 确认按钮回调
/// @param confirmText 确认按钮文本，默认为"确认"
/// @param cancelText 取消按钮文本，默认为"取消"
Future<bool?> showConfirmDialog(
  BuildContext context, {
  required String title,
  required String content,
  required VoidCallback onConfirm,
  String confirmText = '确认',
  String cancelText = '取消',
}) {
  return showDialog<bool>(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(true);
              onConfirm();
            },
            child: Text(
              confirmText,
              style: const TextStyle(
                color: Colors.red,
              ),
            ),
          ),
        ],
      );
    },
  );
}
