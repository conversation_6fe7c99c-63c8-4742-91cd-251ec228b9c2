import 'package:flutter/material.dart';
import 'package:golf/config.dart';

void showLoadingDialog(BuildContext context) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius:
              BorderRadius.circular(5.0), // Set the border radius to 5
        ),
        backgroundColor: Colors.white,
        child: const SizedBox(
          width: 200,
          height: 100,
          child: Align(
            alignment: Alignment.center,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 20, // 自定义宽度
                  height: 20, // 自定义高度
                  child: CircularProgressIndicator(
                    color: AppColors.primaryColor,
                    value: null, // 设置为 null 以显示动画效果
                    strokeWidth: 1, // 调整线条的粗细
                    backgroundColor: Colors.grey, // 设置背景色
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Text("Loading"),
              ],
            ),
          ),
        ),
      );
    },
  );
}
