// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/services/provider/base.dart';
import 'package:provider/provider.dart';

class RouteArguments {
  final bool requiresAuth;
  final Map<String, dynamic> data;

  RouteArguments({required this.requiresAuth, required this.data});
}

class AuthGuardObserver extends RouteObserver<PageRoute<dynamic>> {
  String _getCurrentPath(Route<dynamic> route) {
    // 获取当前路由名称
    String path = route.settings.name ?? '';

    // 如果有参数，添加到路径中
    if (route.settings.arguments != null) {
      final args = route.settings.arguments;
      if (args is Map<String, dynamic>) {
        final queryParams = args.entries
            .map((e) => '${e.key}=${Uri.encodeComponent(e.value.toString())}')
            .join('&');
        if (queryParams.isNotEmpty) {
          path += '?$queryParams';
        }
      }
    }

    return path;
  }

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    final currentPath = _getCurrentPath(route);
    AppLogger.info(
        'didPush: ${route.settings.name}, previous: ${previousRoute?.settings.name}, Path: $currentPath', 'ROUTER');

    // 打印路由参数
    if (route.settings.arguments != null) {
      AppLogger.info('Route arguments: ${route.settings.arguments}', 'ROUTER');
    }

    _checkLoginStatus(route);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    final currentPath = _getCurrentPath(route);
    AppLogger.info(
        'didPop: ${route.settings.name}, previous: ${previousRoute?.settings.name}, Path: $currentPath', 'ROUTER');
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didRemove(route, previousRoute);
    final currentPath = _getCurrentPath(route);
    AppLogger.info(
        'didRemove: ${route.settings.name}, previous: ${previousRoute?.settings.name}, Path: $currentPath', 'ROUTER');
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    final currentPath = newRoute != null ? _getCurrentPath(newRoute) : '';
    AppLogger.info(
        'didReplace: ${newRoute?.settings.name}, previous: ${oldRoute?.settings.name}, Path: $currentPath', 'ROUTER');
  }

  void _checkLoginStatus(Route<dynamic> route) async {
    final api = Provider.of<APIBase>(route.navigator!.context, listen: false);
    bool loggedIn = await api.isLoggedIn();
    AppLogger.info('检查登录状态: $loggedIn, 路由: ${route.settings.name}', 'AUTH');

    // 检查路由参数是否包含支付结果参数
    final settings = route.settings;
    if (settings.name == '/user/point_detail') {
      final args = settings.arguments;
      if (args is Map<String, dynamic>) {
        if (args.containsKey('payment_result') &&
            args.containsKey('order_id')) {
          AppLogger.info('检测到支付结果页面，不进行重定向检查', 'AUTH');
          return;
        }
      }
    }

    if (settings.arguments == null) {
      return;
    }

    RouteArguments args = settings.arguments as RouteArguments;
    bool requiresAuth = args.requiresAuth;

    if (!loggedIn && requiresAuth) {
      AppLogger.info('未登录，需要登录权限，重定向到登录页面', 'AUTH');
      Navigator.pushReplacementNamed(route.navigator!.context, '/user/sign_in');
    }
  }
}
