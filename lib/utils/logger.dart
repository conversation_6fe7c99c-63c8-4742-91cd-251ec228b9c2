import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

class FileLogger {
  static Future<void> logToFile(String message) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/app_log.txt');
    await file.writeAsString('${DateTime.now()}: $message\n',
        mode: FileMode.append);
  }
}

class AppLogger {
  static void debug(String message, [String? tag]) {
    if (kDebugMode) {
      final logMessage = tag != null ? '[$tag] $message' : message;
      print(logMessage);
    }
  }

  static void info(String message, [String? tag]) {
    if (kDebugMode) {
      final logMessage = tag != null ? '[$tag] $message' : message;
      print('ℹ️ $logMessage');
    }
  }

  static void warning(String message, [String? tag]) {
    if (kDebugMode) {
      final logMessage = tag != null ? '[$tag] $message' : message;
      print('⚠️ $logMessage');
    }
  }

  static void error(String message, [String? tag]) {
    if (kDebugMode) {
      final logMessage = tag != null ? '[$tag] $message' : message;
      print('❌ $logMessage');
    }
  }

  static Future<void> errorWithFile(String message, [String? tag]) async {
    error(message, tag);
    await FileLogger.logToFile('ERROR: ${tag != null ? '[$tag] ' : ''}$message');
  }

  static Future<void> infoWithFile(String message, [String? tag]) async {
    info(message, tag);
    await FileLogger.logToFile('INFO: ${tag != null ? '[$tag] ' : ''}$message');
  }
}
