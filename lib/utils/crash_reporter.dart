// No platform-specific imports needed for this simplified version
import 'package:flutter/foundation.dart';

class CrashReporter {
  static Future<void> init() async {
    // Set global error capture
    FlutterError.onError = (FlutterErrorDetails details) async {
      await reportError(details.exception, details.stack);
    };
  }

  static Future<void> reportError(dynamic error, StackTrace? stack) async {
    final timestamp = DateTime.now().toIso8601String();
    final errorReport = '''
----- Crash Report -----
Time: $timestamp
Error: $error
Stack trace: ${stack ?? 'No stack trace available'}
----------------------
''';

    // Always print to console for debugging
    debugPrint('CRASH REPORT:');
    debugPrint(errorReport);

    // For mobile platforms, we could write to file
    // But we'll skip that for now to keep it simple
    // and avoid platform-specific code
  }

  static Future<String?> getCrashLogs() async {
    // For simplicity, we'll just return a message
    return 'Crash logs are printed to the console. Check the debug output for error messages.';
  }
}
