bool isValidEmail(String email) {
  final RegExp regex =
      RegExp(r'^[a-zA-Z0-9.a-zA-Z0-9._%-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$');
  return regex.hasMatch(email);
}

bool isValidPhoneNumber(String phoneNumber) {
  final RegExp regex = RegExp(r'^\d{10}$');
  return regex.hasMatch(phoneNumber);
}

const passwordFormatError =
    "Password format is incorrect,a-z, 0-9, 8-16 digits";

bool isValidPassword(String password) {
  // Password needs to be at least 8 characters long
  // and include at least one number and one letter
  // final RegExp regex = RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$');
  // return regex.hasMatch(password);
  return true;
}
