import 'package:flutter/material.dart';
import 'package:golf/utils/logger.dart';
import 'package:golf/services/exceptions.dart';
import 'package:golf/services/req/req.dart';
import 'package:golf/widgets/golf_toast.dart';

Map<String, bool> _errorHandled = {};
void showError(dynamic e, BuildContext context) {
  AppLogger.warning("Error: $e", "ERROR");
  var msg = e.toString();

  if (e is ApiException) {
    if (e.code == ResponseCode.authExpired.value) {
      msg = "session expired, pleasse login";
    } else {
      msg = e.message;
    }
  }
  if (_errorHandled[msg] == true) {
    return;
  }
  _errorHandled[msg] = true;
  // 延迟100ms 再展示动画
  Future.delayed(const Duration(milliseconds: 100), () {
    showToast(context, msg: msg);
    _errorHandled[msg] = false;
  });
  // Handle other errors
}
