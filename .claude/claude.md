# 项目开发注意事项

1. **多端兼容性**
   - 本项目需同时兼容 Web、iOS、Android 平台。
   - 所有 UI、交互、第三方库选择、平台判断等，均需考虑三端差异，避免使用仅支持单一平台的特性。
   - 平台判断建议统一用 `kIsWeb`、`Platform.isIOS`、`Platform.isAndroid` 等 Flutter 官方推荐方式。

2. **最佳实践与可维护性**
   - 代码结构需清晰，遵循分层思想（如 UI、业务逻辑、数据层分离）。
   - 组件/页面应尽量解耦，复用性强，便于单元测试和维护。
   - 表单、网络请求、状态管理等建议采用社区主流方案（如 Provider、Riverpod、Bloc 等），避免自造轮子。
   - 注释需规范，推荐使用 JSDoc 风格，便于团队协作和后续维护。
   - 错误处理要健壮，所有平台下都能给用户友好提示，避免因类型、平台差异导致崩溃。

3. **UI/UX 统一性**
   - 保持 Material 与 Cupertino 风格统一，避免割裂体验。
   - 交互细节需兼容多端（如键盘弹出、表单校验、弹窗提示等）。

4. **接口兼容与健壮性**
   - 后端接口返回结构不稳定时，前端需做健壮兼容，避免因字段缺失或类型不符导致崩溃。

5. **持续集成与测试**
   - 推荐引入自动化测试和持续集成，保证多端质量。
6. api文档地址： https://api.golfteetime.vip/docs/index

7. 支付测试卡地址 https://docs.stripe.com/testing#cards


> 本文档为团队开发约定，后续如有新发现请持续补充。
