#!/bin/bash

# 版本号递增脚本
# 自动增加 pubspec.yaml 中的版本号和构建号，并同步到 config.dart 和 web/index.html

echo "🔄 开始递增版本号..."

# 从 pubspec.yaml 读取当前版本号
CURRENT_VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: *//')

if [ -z "$CURRENT_VERSION" ]; then
    echo "❌ 无法从 pubspec.yaml 读取版本号"
    exit 1
fi

echo "📦 当前版本号: $CURRENT_VERSION"

# 解析版本号 (格式: major.minor.patch+build_number)
if [[ $CURRENT_VERSION =~ ^([0-9]+)\.([0-9]+)\.([0-9]+)\+([0-9]+)$ ]]; then
    MAJOR=${BASH_REMATCH[1]}
    MINOR=${BASH_REMATCH[2]}
    PATCH=${BASH_REMATCH[3]}
    BUILD_NUMBER=${BASH_REMATCH[4]}

    # 递增 patch 版本号和构建号
    NEW_PATCH=$((PATCH + 1))
    NEW_BUILD_NUMBER=$((BUILD_NUMBER + 1))
    NEW_VERSION="$MAJOR.$MINOR.$NEW_PATCH+$NEW_BUILD_NUMBER"

    echo "📈 新版本号: $NEW_VERSION (patch: $PATCH → $NEW_PATCH, build: $BUILD_NUMBER → $NEW_BUILD_NUMBER)"
else
    echo "❌ 版本号格式错误，期望格式: major.minor.patch+build_number"
    echo "   当前格式: $CURRENT_VERSION"
    exit 1
fi

# 更新 pubspec.yaml 中的版本号
sed -i.bak "s/^version: $CURRENT_VERSION/version: $NEW_VERSION/" pubspec.yaml

if [ $? -eq 0 ]; then
    echo "✅ 已更新 pubspec.yaml 中的版本号为: $NEW_VERSION"
    rm pubspec.yaml.bak
else
    echo "❌ 更新 pubspec.yaml 失败"
    exit 1
fi

# 更新 config.dart 中的版本号 (只使用版本号部分，不包含构建号)
VERSION_ONLY="$MAJOR.$MINOR.$NEW_PATCH"
sed -i.bak "s/static const appVersion = '[^']*'/static const appVersion = '$VERSION_ONLY'/" lib/config.dart

if [ $? -eq 0 ]; then
    echo "✅ 已更新 config.dart 中的版本号为: $VERSION_ONLY"
    rm lib/config.dart.bak
else
    echo "❌ 更新 config.dart 失败"
    exit 1
fi

# 更新 web/index.html 中的版本号
sed -i.bak "s/window\.version = \"[^\"]*\"/window\.version = \"$VERSION_ONLY\"/" web/index.html
sed -i.bak "s/flutter\.js\?v=[^\"]*/flutter\.js\?v=$VERSION_ONLY/" web/index.html

if [ $? -eq 0 ]; then
    echo "✅ 已更新 web/index.html 中的版本号为: $VERSION_ONLY"
    rm web/index.html.bak
else
    echo "❌ 更新 web/index.html 失败"
    exit 1
fi

echo "🎉 版本号递增完成！"
echo ""
echo "📋 版本信息更新:"
echo "   - 旧版本: $CURRENT_VERSION"
echo "   - 新版本: $NEW_VERSION"
echo "   - config.dart: $VERSION_ONLY"
echo "   - web/index.html: $VERSION_ONLY"
echo ""
echo "💡 提示:"
echo "   - 构建号 (+$NEW_BUILD_NUMBER) 用于 Android 版本管理"
echo "   - 如需递增 minor 或 major 版本，请手动修改版本号"
