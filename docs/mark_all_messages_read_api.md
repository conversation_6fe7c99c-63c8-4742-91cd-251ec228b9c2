# 标记所有消息为已读 API 实现

## API 接口信息

- **接口地址**: `https://api.teetimebot.vip/message/readall`
- **请求方法**: POST
- **请求体**:
  ```json
  {
    "readstatus": "1"
  }
  ```
- **响应体**:
  ```json
  {
    "code": 0,
    "msg": "ok",
    "data": []
  }
  ```

## 实现的类

### MarkAllMessagesReadRequest
- **用途**: 发送标记所有消息为已读的请求
- **字段**:
  - `readstatus`: String，默认值为 "1"，表示已读状态
- **方法**:
  - `toJson()`: 转换为 API 请求格式

### MarkAllMessagesReadResponse
- **用途**: 处理标记所有消息为已读的响应
- **继承**: BaseResponse
- **方法**:
  - `fromJson()`: 从 API 响应 JSON 创建实例
  - 处理缺失或 null 的 data 字段

## 单元测试

已创建全面的单元测试，覆盖以下场景：

### MarkAllMessagesReadRequest 测试:
- 默认 readstatus 值测试
- 自定义 readstatus 值测试
- JSON 结构正确性测试

### MarkAllMessagesReadResponse 测试:
- 成功响应解析测试
- 缺失 data 字段处理测试
- null data 字段处理测试
- 错误响应处理测试（如授权问题）
- 非空 data 数组处理测试

## 集成说明

1. **MessageService 更新**: 已修复 `markAllMessagesAsRead` 方法中的返回类型，现在正确使用 `MarkAllMessagesReadResponse`

2. **向后兼容**: 新的实现与现有代码完全兼容

3. **错误处理**: 包含适当的错误处理机制

4. **测试覆盖**: 22个测试用例全部通过，确保代码质量

## 注意事项

- 获取消息列表的操作不应该修改消息的未读状态
- `readstatus` 字段为字符串类型，"1" 表示已读，"0" 表示未读
- API 响应的 data 字段通常为空数组，但代码可以处理各种情况

## 使用示例

```dart
// 创建请求
final request = MarkAllMessagesReadRequest(); // 默认 readstatus = "1"

// 发送请求并处理响应
try {
  final response = await api.markAllMessagesAsRead(request);
  if (response.code == 0) {
    print('所有消息已标记为已读: ${response.msg}');
  } else {
    print('操作失败: ${response.msg}');
  }
} catch (e) {
  print('请求失败: $e');
}
```
