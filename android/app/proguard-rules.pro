# 确保必要的混淆规则已添加 

# Stripe 相关混淆规则
-keep class com.stripe.android.** { *; }
-dontwarn com.stripe.android.**
-keep class com.stripe.android.pushprovisioning.** { *; }

# Flutter 相关混淆规则
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# Google Play Core
-keep class com.google.android.play.core.** { *; }
-keep class com.google.android.play.core.splitcompat.** { *; }
-keep class com.google.android.play.core.splitinstall.** { *; }
-keep class com.google.android.play.core.tasks.** { *; }
-dontwarn com.google.android.play.core.** 

# 保留 Kotlin 相关类
-keep class kotlin.** { *; }
-keep class kotlin.Metadata { *; }

# 如果使用了 Retrofit
-keep class retrofit2.** { *; }

# 如果使用了 Gson
-keep class com.google.gson.** { *; }

# 保留自定义的数据模型
-keep class your.package.name.models.** { *; } 