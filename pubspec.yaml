name: golf
description: "A new Flutter project."
publish_to: "none"
version: 0.1.83+83

environment:
  sdk: ">=3.3.3 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  package_info_plus: ^8.0.0
  getwidget: ^4.0.0
  provider: ^6.1.2
  http: ^1.2.1
  shared_preferences: ^2.0.6
  fluttertoast: ^8.2.4
  mockito: ^5.4.4
  time_range_picker: ^2.2.0
  multi_select_flutter: ^4.0.0
  calendar_date_picker2: ^1.0.2
  intl: ^0.19.0
  dropdown_button2: ^2.3.9
  jwt_decode: ^0.3.1
  share_plus: ^10.1.4
  google_maps_flutter: ^2.7.0
  geolocator: ^12.0.0
  syncfusion_flutter_sliders: ^26.2.13
  syncfusion_flutter_core: ^26.2.13
  url_launcher: ^6.3.0
  collection: ^1.18.0
  flutter_stripe: ^11.4.0
  flutter_stripe_web: ^6.4.0
  js: ^0.6.7
  keyboard_actions: ^4.2.0
  retry: ^3.1.2
  connectivity_plus: ^6.1.4
  visibility_detector: ^0.4.0
  firebase_core: ^3.8.0
  firebase_messaging: ^15.1.6
  permission_handler: ^11.3.1
  path_provider: ^2.1.4
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true
  assets:
    - assets/imgs/
    - fixtures/

flutter_launcher_icons:
  android: true # 如果也想生成 Android 图标，设为 true
  ios: true # 确保 iOS 设为 true
  image_path_android: "assets/icon/android-icon.png"
  image_path_ios: "assets/icon/icon.png"
  remove_alpha_ios: true
