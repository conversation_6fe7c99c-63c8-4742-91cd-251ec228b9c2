# 版本管理使用说明

## 概述

本项目支持自动版本号管理，包括版本号递增和同步功能。

## 版本号格式

- **完整格式**: `major.minor.patch+build_number` (例如: `0.1.83+83`)
- **版本号部分**: `major.minor.patch` (例如: `0.1.83`)
- **构建号**: `+build_number` (用于 Android 版本管理)

## 脚本说明

### 1. 版本递增脚本 (`increment_version.sh`)

**功能**: 自动递增版本号和构建号，并同步到所有相关文件

**更新文件**:
- `pubspec.yaml` - 完整版本号 (包含构建号)
- `lib/config.dart` - 版本号部分
- `web/index.html` - 版本号部分

**使用方式**:
```bash
./increment_version.sh
```

### 2. 版本同步脚本 (`update_version.sh`)

**功能**: 将 `pubspec.yaml` 中的版本号同步到其他文件

**使用方式**:
```bash
./update_version.sh
```

## Fabric 任务

### 构建任务

1. **`fab build`** - 构建 Web 版本（仅同步版本号）
2. **`fab buildWithVersion`** - 构建 Web 版本并递增版本号
3. **`fab buildIos`** - 构建 iOS 版本
4. **`fab buildAndroid`** - 构建 Android APK
5. **`fab buildPlay`** - 构建 Google Play AAB

### 版本管理任务

1. **`fab incrementVersion`** - 仅递增版本号，不构建
2. **`fab syncVersion`** - 仅同步版本号，不递增

## 使用建议

### 开发阶段
```bash
# 仅同步版本号，确保一致性
fab syncVersion
```

### 发布阶段
```bash
# 递增版本号并构建
fab buildWithVersion

# 或者分步执行
fab incrementVersion
fab build
```

### 手动管理
```bash
# 直接运行脚本
./increment_version.sh
./update_version.sh
```

## 注意事项

1. **版本号格式**: 确保 `pubspec.yaml` 中的版本号格式为 `major.minor.patch+build_number`
2. **构建号**: 构建号会自动递增，用于 Android 版本管理
3. **同步**: 所有文件的版本号会自动保持同步
4. **备份**: 脚本会自动创建备份文件，操作失败时会保留原文件

## 文件说明

- `pubspec.yaml`: Flutter 项目版本定义
- `lib/config.dart`: 应用内版本号显示
- `web/index.html`: Web 版本号显示
- `fabfile.py`: Fabric 构建任务定义
