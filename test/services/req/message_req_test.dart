import 'package:flutter_test/flutter_test.dart';
import 'package:golf/services/req/message_req.dart';

void main() {
  group('MessageItem', () {
    group('fromJson', () {
      test('should parse valid JSON with all fields', () {
        // Arrange
        final json = {
          'id': '1',
          'user_id': '2',
          'title': 'Test Title',
          'body': 'Test Body',
'datatype': '1',
          'data': '{"plan_id": "123"}',
          'sendstatus': '1',
          'readstatus': '0',
          'status': '1',
          'insert_time': '2025-07-28 09:59:16',
        };

        // Act
        final item = MessageItem.fromJson(json);

        // Assert
        expect(item.id, '1');
        expect(item.userId, '2');
        expect(item.title, 'Test Title');
        expect(item.body, 'Test Body');
expect(item.data, '{"plan_id": "123"}');
        expect(item.datatype, '1');
        expect(item.planId, '123');
        expect(item.sendstatus, '1');
        expect(item.readstatus, '0');
        expect(item.status, '1');
        expect(item.isRead, false);
        expect(item.type, 'general');
      });

      test('should handle read status correctly', () {
        // Arrange - read message
        final jsonRead = {
          'id': '1',
          'user_id': '2',
          'title': 'Read Message',
          'body': 'This is read',
          'datatype': '0',
          'data': '{}',
          'sendstatus': '1',
          'readstatus': '1',
          'status': '1',
          'insert_time': '2025-07-28 09:59:16',
        };

        // Act
        final readItem = MessageItem.fromJson(jsonRead);

        // Assert
        expect(readItem.isRead, true);

        // Arrange - unread message
        final jsonUnread = {
          'id': '2',
          'user_id': '2',
          'title': 'Unread Message',
          'body': 'This is unread',
          'data': '{}',
          'sendstatus': '1',
          'readstatus': '0',
          'status': '1',
          'insert_time': '2025-07-28 09:59:16',
        };

        // Act
        final unreadItem = MessageItem.fromJson(jsonUnread);

        // Assert
        expect(unreadItem.isRead, false);
      });

      test('should handle missing or null fields gracefully', () {
        // Arrange
        final json = {
          'id': '1',
          'user_id': '2',
          'title': null,
          'body': null,
          'data': null,
          'sendstatus': '0',
          'readstatus': '0',
          'status': '1',
          'insert_time': '2025-07-28 09:59:16',
        };

        // Act
        final item = MessageItem.fromJson(json);

        // Assert
        expect(item.title, '');
        expect(item.body, '');
        expect(item.data, '{}');
      });

      test('should parse date correctly', () {
        // Arrange
        final json = {
          'id': '1',
          'user_id': '2',
          'title': 'Test',
          'body': 'Test',
          'data': '{}',
          'sendstatus': '1',
          'readstatus': '0',
          'status': '1',
          'insert_time': '2025-07-28 09:59:16',
        };

        // Act
        final item = MessageItem.fromJson(json);

        // Assert
        expect(item.insertTime, DateTime.parse('2025-07-28 09:59:16'));
        expect(item.createdAt, DateTime.parse('2025-07-28 09:59:16'));
      });

      test('should parse plan_id from data when datatype is "1"', () {
        // Arrange
        final json = {
          'id': '1',
          'user_id': '2',
          'title': 'Plan Notification',
          'body': 'Plan updated',
          'datatype': '1',
          'data': '{"plan_id": 1032}',
          'sendstatus': '1',
          'readstatus': '0',
          'status': '1',
          'insert_time': '2025-07-28 09:59:16',
        };

        // Act
        final item = MessageItem.fromJson(json);

        // Assert
        expect(item.datatype, '1');
        expect(item.planId, '1032');
        expect(item.parsedData, {'plan_id': 1032});
      });

      test('should handle invalid JSON in data field', () {
        // Arrange
        final json = {
          'id': '1',
          'user_id': '2',
          'title': 'Test',
          'body': 'Test',
          'datatype': '1',
          'data': 'invalid json',
          'sendstatus': '1',
          'readstatus': '0',
          'status': '1',
          'insert_time': '2025-07-28 09:59:16',
        };

        // Act
        final item = MessageItem.fromJson(json);

        // Assert
        expect(item.parsedData, null);
        expect(item.planId, null);
      });

      test('should handle empty data field', () {
        // Arrange
        final json = {
          'id': '1',
          'user_id': '2',
          'title': 'Test',
          'body': 'Test',
          'datatype': '1',
          'data': '',
          'sendstatus': '1',
          'readstatus': '0',
          'status': '1',
          'insert_time': '2025-07-28 09:59:16',
        };

        // Act
        final item = MessageItem.fromJson(json);

        // Assert
        expect(item.parsedData, null);
        expect(item.planId, null);
      });

      test('should handle datatype default value', () {
        // Arrange
        final json = {
          'id': '1',
          'user_id': '2',
          'title': 'Test',
          'body': 'Test',
          // datatype missing
          'data': '{}',
          'sendstatus': '1',
          'readstatus': '0',
          'status': '1',
          'insert_time': '2025-07-28 09:59:16',
        };

        // Act
        final item = MessageItem.fromJson(json);

        // Assert
        expect(item.datatype, '0'); // Default value
      });
    });
  });

  group('MessageListResponse', () {
    group('fromJson', () {
      test('should parse valid JSON and calculate unread count', () {
        // Arrange
        final json = {
          'code': 0,
          'msg': 'ok',
          'data': {
            'items': [
              {
                'id': '1',
                'user_id': '2',
                'title': 'Unread Message',
                'body': 'Body 1',
                'data': '{}',
                'sendstatus': '1',
                'readstatus': '0', // unread
                'status': '1',
                'insert_time': '2025-07-28 09:59:16',
              },
              {
                'id': '2',
                'user_id': '2',
                'title': 'Read Message',
                'body': 'Body 2',
                'data': '{}',
                'sendstatus': '1',
                'readstatus': '1', // read
                'status': '1',
                'insert_time': '2025-07-28 09:59:16',
              },
              {
                'id': '3',
                'user_id': '2',
                'title': 'Another Unread',
                'body': 'Body 3',
                'data': '{}',
                'sendstatus': '1',
                'readstatus': '0', // unread
                'status': '1',
                'insert_time': '2025-07-28 09:59:16',
              },
            ],
            'paginator': {
              'prev': 1,
              'next': 1,
              'total': 1,
            }
          }
        };

        // Act
        final response = MessageListResponse.fromJson(json);

        // Assert
        expect(response.code, 0);
        expect(response.msg, 'ok');
        expect(response.items.length, 3);
        expect(response.unreadCount, 2); // Two unread messages
        expect(response.paginator.prev, 1);
        expect(response.paginator.next, 1);
        expect(response.paginator.total, 1);
      });
    });
  });

  group('MessageStatusResponse', () {
    group('fromJson', () {
      test('should parse valid JSON with all fields', () {
        // Arrange
        final json = {
          'code': 0,
          'msg': 'success',
          'data': {
            'unread': 8,
            'read': 12,
            'unsend': 3,
            'send': 20,
          }
        };

        // Act
        final response = MessageStatusResponse.fromJson(json);

        // Assert
        expect(response.code, 0);
        expect(response.msg, 'success');
        expect(response.unread, 8);
        expect(response.read, 12);
        expect(response.unsend, 3);
        expect(response.send, 20);
      });

      test('should handle missing data field', () {
        // Arrange
        final json = {
          'code': 0,
          'msg': 'success',
        };

        // Act
        final response = MessageStatusResponse.fromJson(json);

        // Assert
        expect(response.code, 0);
        expect(response.msg, 'success');
        expect(response.unread, 0);
        expect(response.read, 0);
        expect(response.unsend, 0);
        expect(response.send, 0);
      });

      test('should handle null data field', () {
        // Arrange
        final json = {
          'code': 0,
          'msg': 'success',
          'data': null,
        };

        // Act
        final response = MessageStatusResponse.fromJson(json);

        // Assert
        expect(response.code, 0);
        expect(response.msg, 'success');
        expect(response.unread, 0);
        expect(response.read, 0);
        expect(response.unsend, 0);
        expect(response.send, 0);
      });

      test('should handle empty data field', () {
        // Arrange
        final json = {
          'code': 0,
          'msg': 'success',
          'data': {},
        };

        // Act
        final response = MessageStatusResponse.fromJson(json);

        // Assert
        expect(response.code, 0);
        expect(response.msg, 'success');
        expect(response.unread, 0);
        expect(response.read, 0);
        expect(response.unsend, 0);
        expect(response.send, 0);
      });

      test('should handle partial data fields', () {
        // Arrange
        final json = {
          'code': 1,
          'msg': 'partial data',
          'data': {
            'unread': 5,
            'send': 10,
            // missing: read, unsend
          }
        };

        // Act
        final response = MessageStatusResponse.fromJson(json);

        // Assert
        expect(response.code, 1);
        expect(response.msg, 'partial data');
        expect(response.unread, 5);
        expect(response.read, 0);
        expect(response.unsend, 0);
        expect(response.send, 10);
      });

      test('should handle null values in data fields', () {
        // Arrange
        final json = {
          'code': 0,
          'msg': 'success',
          'data': {
            'unread': null,
            'read': 12,
            'unsend': null,
            'send': 20,
          }
        };

        // Act
        final response = MessageStatusResponse.fromJson(json);

        // Assert
        expect(response.code, 0);
        expect(response.msg, 'success');
        expect(response.unread, 0); // null values default to 0
        expect(response.read, 12);
        expect(response.unsend, 0); // null values default to 0
        expect(response.send, 20);
      });

      test('should handle error response codes', () {
        // Arrange
        final json = {
          'code': 999,
          'msg': 'Internal server error',
          'data': {
            'unread': 0,
            'read': 0,
            'unsend': 0,
            'send': 0,
          }
        };

        // Act
        final response = MessageStatusResponse.fromJson(json);

        // Assert
        expect(response.code, 999);
        expect(response.msg, 'Internal server error');
        expect(response.unread, 0);
        expect(response.read, 0);
        expect(response.unsend, 0);
        expect(response.send, 0);
      });

      test('should handle zero values', () {
        // Arrange
        final json = {
          'code': 0,
          'msg': 'success',
          'data': {
            'unread': 0,
            'read': 0,
            'unsend': 0,
            'send': 0,
          }
        };

        // Act
        final response = MessageStatusResponse.fromJson(json);

        // Assert
        expect(response.code, 0);
        expect(response.msg, 'success');
        expect(response.unread, 0);
        expect(response.read, 0);
        expect(response.unsend, 0);
        expect(response.send, 0);
      });

      test('should handle large numbers', () {
        // Arrange
        final json = {
          'code': 0,
          'msg': 'success',
          'data': {
            'unread': 500000,
            'read': 800000,
            'unsend': 100000,
            'send': 1200000,
          }
        };

        // Act
        final response = MessageStatusResponse.fromJson(json);

        // Assert
        expect(response.code, 0);
        expect(response.msg, 'success');
        expect(response.unread, 500000);
        expect(response.read, 800000);
        expect(response.unsend, 100000);
        expect(response.send, 1200000);
      });
    });
  });

  group('MarkAllMessagesReadRequest', () {
    test('should create request with default readstatus', () {
      // Act
      final request = MarkAllMessagesReadRequest();
      final json = request.toJson();

      // Assert
      expect(json['readstatus'], '1');
    });

    test('should create request with custom readstatus', () {
      // Act
      final request = MarkAllMessagesReadRequest(readstatus: '0');
      final json = request.toJson();

      // Assert
      expect(json['readstatus'], '0');
    });

    test('should generate correct JSON structure', () {
      // Arrange
      final request = MarkAllMessagesReadRequest();

      // Act
      final json = request.toJson();

      // Assert
      expect(json, isA<Map<String, dynamic>>());
      expect(json.keys.length, 1);
      expect(json.containsKey('readstatus'), true);
    });
  });

  group('MarkAllMessagesReadResponse', () {
    group('fromJson', () {
      test('should parse successful response', () {
        // Arrange
        final json = {
          'code': 0,
          'msg': 'ok',
          'data': [],
        };

        // Act
        final response = MarkAllMessagesReadResponse.fromJson(json);

        // Assert
        expect(response.code, 0);
        expect(response.msg, 'ok');
        expect(response.data, []);
      });

      test('should handle missing data field', () {
        // Arrange
        final json = {
          'code': 0,
          'msg': 'ok',
        };

        // Act
        final response = MarkAllMessagesReadResponse.fromJson(json);

        // Assert
        expect(response.code, 0);
        expect(response.msg, 'ok');
        expect(response.data, []);
      });

      test('should handle null data field', () {
        // Arrange
        final json = {
          'code': 0,
          'msg': 'ok',
          'data': null,
        };

        // Act
        final response = MarkAllMessagesReadResponse.fromJson(json);

        // Assert
        expect(response.code, 0);
        expect(response.msg, 'ok');
        expect(response.data, []);
      });

      test('should handle error response', () {
        // Arrange
        final json = {
          'code': 999,
          'msg': 'ISSUE AUTHORIZATION',
          'data': [],
        };

        // Act
        final response = MarkAllMessagesReadResponse.fromJson(json);

        // Assert
        expect(response.code, 999);
        expect(response.msg, 'ISSUE AUTHORIZATION');
        expect(response.data, []);
      });

      test('should handle non-empty data array', () {
        // Arrange
        final json = {
          'code': 0,
          'msg': 'ok',
          'data': ['item1', 'item2'],
        };

        // Act
        final response = MarkAllMessagesReadResponse.fromJson(json);

        // Assert
        expect(response.code, 0);
        expect(response.msg, 'ok');
        expect(response.data, ['item1', 'item2']);
      });
    });
  });

  group('MarkMessageReadRequest', () {
    test('should create request with id and default readstatus', () {
      // Act
      final request = MarkMessageReadRequest(id: 123);
      final json = request.toJson();

      // Assert
      expect(json['id'], 123);
      expect(json['readstatus'], '1');
    });

    test('should create request with id and custom readstatus', () {
      // Act
      final request = MarkMessageReadRequest(id: 456, readstatus: '0');
      final json = request.toJson();

      // Assert
      expect(json['id'], 456);
      expect(json['readstatus'], '0');
    });

    test('should generate correct JSON structure', () {
      // Arrange
      final request = MarkMessageReadRequest(id: 789);

      // Act
      final json = request.toJson();

      // Assert
      expect(json, isA<Map<String, dynamic>>());
      expect(json.keys.length, 2);
      expect(json.containsKey('id'), true);
      expect(json.containsKey('readstatus'), true);
    });
  });

  group('MessageListRequest', () {
    test('should create request with default parameters', () {
      // Act
      final request = MessageListRequest();
      final json = request.toJson();

      // Assert
      expect(json['page'], 1);
      expect(json['size'], 10);
      expect(json['status'], 1); // Should always be 1
      expect(json['order'], 'id desc');
      expect(json.containsKey('readstatus'), false); // Should not be included
      expect(json.containsKey('sendstatus'), false); // Should not be included
    });

    test('should create request with custom parameters', () {
      // Act
      final request = MessageListRequest(
        page: 2,
        size: 20,
        status: 1, // Even if explicitly set
        readstatus: 0,
        sendstatus: 1,
      );
      final json = request.toJson();

      // Assert
      expect(json['page'], 2);
      expect(json['size'], 20);
      expect(json['status'], 1);
      expect(json['readstatus'], 0); // Should be included when explicitly set
      expect(json['sendstatus'], 1); // Should be included when explicitly set
    });

    test('should only include readstatus and sendstatus when explicitly provided', () {
      // Act - Only readstatus provided
      final request1 = MessageListRequest(readstatus: 1);
      final json1 = request1.toJson();

      // Assert
      expect(json1.containsKey('readstatus'), true);
      expect(json1['readstatus'], 1);
      expect(json1.containsKey('sendstatus'), false);

      // Act - Only sendstatus provided
      final request2 = MessageListRequest(sendstatus: 0);
      final json2 = request2.toJson();

      // Assert
      expect(json2.containsKey('readstatus'), false);
      expect(json2.containsKey('sendstatus'), true);
      expect(json2['sendstatus'], 0);
    });

    test('should always include status field', () {
      // Act
      final request = MessageListRequest();
      final json = request.toJson();

      // Assert
      expect(json.containsKey('status'), true);
      expect(json['status'], 1);
    });
  });
}
