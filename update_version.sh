#!/bin/bash

# 版本同步脚本
# 功能：将 pubspec.yaml 中的版本号同步到 config.dart 和 web/index.html 中
# 注意：此脚本不会递增版本号，仅用于同步
# 如需递增版本号，请使用 increment_version.sh 脚本

echo "🔄 开始同步版本信息..."

# 从 pubspec.yaml 读取版本号
FULL_VERSION=$(grep '^version:' pubspec.yaml | sed 's/version: *//')

if [ -z "$FULL_VERSION" ]; then
    echo "❌ 无法从 pubspec.yaml 读取版本号"
    exit 1
fi

echo "📦 检测到完整版本号: $FULL_VERSION"

# 提取版本号部分（不包含构建号）
if [[ $FULL_VERSION =~ ^([0-9]+\.[0-9]+\.[0-9]+) ]]; then
    VERSION_ONLY=${BASH_REMATCH[1]}
    echo "📋 提取版本号部分: $VERSION_ONLY"
else
    echo "❌ 无法解析版本号格式"
    exit 1
fi

# 更新 config.dart 中的版本号
sed -i.bak "s/static const appVersion = '[^']*'/static const appVersion = '$VERSION_ONLY'/" lib/config.dart

if [ $? -eq 0 ]; then
    echo "✅ 已更新 config.dart 中的版本号为: $VERSION_ONLY"
    rm lib/config.dart.bak
else
    echo "❌ 更新 config.dart 失败"
    exit 1
fi

# 更新 web/index.html 中的版本号
sed -i.bak "s/window\.version = \"[^\"]*\"/window\.version = \"$VERSION_ONLY\"/" web/index.html
sed -i.bak "s/flutter\.js\?v=[^\"]*/flutter\.js\?v=$VERSION_ONLY/" web/index.html

if [ $? -eq 0 ]; then
    echo "✅ 已更新 web/index.html 中的版本号为: $VERSION_ONLY"
    rm web/index.html.bak
else
    echo "❌ 更新 web/index.html 失败"
    exit 1
fi

echo "🎉 版本同步完成！"
echo ""
echo "📋 当前版本信息:"
echo "   - pubspec.yaml: $FULL_VERSION"
echo "   - config.dart: $VERSION_ONLY"
echo "   - web/index.html: $VERSION_ONLY"
echo ""
echo "💡 提示: 如需递增版本号，请运行 ./increment_version.sh"
